package iam

import (
	"context"
	"errors"
	"fmt"
	"time"

	"google.golang.org/grpc"

	"github.com/vendasta/IAM/sdks/go/iaminterceptor"
	"github.com/vendasta/IAM/sdks/go/v1/externalid"

	"google.golang.org/grpc/codes"

	iam_v1 "github.com/vendasta/generated-protos-go/iam/v1"
	"github.com/vendasta/gosdks/config"
	"github.com/vendasta/gosdks/vax"
)

var addresses = map[config.Env]string{
	config.Local: "IAM:11000",
	config.Test:  "iam-api-test.vendasta-internal.com",
	config.Demo:  "iam-api-demo.vendasta-internal.com",
	config.Prod:  "iam-api-prod.vendasta-internal.com",
}

var scopes = map[config.Env]string{
	config.Local: "",
	config.Test:  "https://iam-api-test.vendasta-internal.com",
	config.Demo:  "https://iam-api-demo.vendasta-internal.com",
	config.Prod:  "https://iam-api-prod.vendasta-internal.com",
}

var certProviderUrls = map[config.Env]string{
	config.Local: "https://iam-test.vendasta-internal.com/oauth2/v1/certs",
	config.Test:  "https://iam-test.vendasta-internal.com/oauth2/v1/certs",
	config.Demo:  "https://iam-demo.vendasta-internal.com/oauth2/v1/certs",
	config.Prod:  "https://iam-prod.vendasta-internal.com/oauth2/v1/certs",
}

// NewClientV4 conforms close to the mscli generated interface. Accepts vax options and returns the closer function.
func NewClientV4(ctx context.Context, e config.Env, options ...vax.ClientOption) (Interface, func() error, error) {
	defaultOptions := []vax.ClientOption{
		vax.EnableToken(true),
	}

	options = append(defaultOptions, options...)

	return newClient(ctx, e, options...)
}

// Deprecated: use NewClientV4 instead
func NewClientWithOptions(ctx context.Context, e config.Env, options ...vax.ClientOption) (Interface, error) {
	client, _, err := NewClientV4(ctx, e, options...)
	return client, err
}

// NewClient returns an IAM client.
// Deprecated: Use NewClientV4 instead by passing in vax.ClientOption options
func NewClient(ctx context.Context, e config.Env, dialOptions ...grpc.DialOption) (Interface, error) {
	opts := []vax.ClientOption{
		vax.EnableToken(true),
	}

	for _, dialOpt := range dialOptions {
		opts = append(opts, vax.WithGRPCDialOptions(dialOpt))
	}

	client, _, err := newClient(ctx, e, opts...)
	return client, err
}

// NewTokenlessClient returns an IAM client that doesn't have the default token attached for auth
// You must provide your own auth for this client to function.
// Deprecated: Use NewClientV4 instead by passing in vax.ClientOption options
func NewTokenlessClient(ctx context.Context, e config.Env, dialOptions ...grpc.DialOption) (Interface, error) {
	opts := []vax.ClientOption{
		vax.EnableToken(false),
	}

	for _, dialOpt := range dialOptions {
		opts = append(opts, vax.WithGRPCDialOptions(dialOpt))
	}
	client, _, err := newClient(ctx, e, opts...)
	return client, err
}

func newClient(ctx context.Context, e config.Env, opts ...vax.ClientOption) (Interface, func() error, error) {
	var err error
	iaminterceptor.PublicKeyClient, err = iaminterceptor.NewPublicKeyClient(e)
	if err != nil {
		return nil, nil, fmt.Errorf("unable to find a publicKey for env %s: %s", e.Name(), err.Error())
	}
	address := addresses[e]
	providerURL := certProviderUrls[e]
	if address == "" || providerURL == "" {
		return nil, nil, fmt.Errorf("unable to create client with environment %d", e)
	}

	internalOpts := []vax.ClientOption{
		vax.WithAddress(address),
		vax.UseTLS(!config.IsInCluster(address)),
		vax.WithAudience(scopes[e]),
	}

	opts = append(internalOpts, opts...)

	conn, err := vax.NewGRPCConnectionWithOptions(ctx, opts...)
	if err != nil {
		return nil, nil, err
	}
	return &iamClient{
		client:      iam_v1.NewIAMClient(conn),
		userClient:  iam_v1.NewUserIAMClient(conn),
		providerURL: providerURL,
	}, conn.Close, nil
}

// PrivateKey is encoded as a PEM
type PrivateKey string

type UserIdentifier func() *iam_v1.UserIdentifier

// UserID returns a user identifier to look up a user based on their id.
func UserID(userID string) UserIdentifier {
	return func() *iam_v1.UserIdentifier {
		return &iam_v1.UserIdentifier{UserIdentifier: &iam_v1.UserIdentifier_UserId{UserId: userID}}
	}
}

// TokenIdentifier returns a user identifier to look up the user identified by the token signed by the platform (ie. access token or identity tokens from SSO; session tokens from IAM)
func TokenIdentifier(token string) UserIdentifier {
	return func() *iam_v1.UserIdentifier {
		return &iam_v1.UserIdentifier{UserIdentifier: &iam_v1.UserIdentifier_Token{Token: token}}
	}
}

// NamespacedEmail returns an identifier to look up a user by their namespace and email.
// In most cases, a namespace should be provided. However in some administrative workflows, a namespace might not be
// applicable. If that's the case, an empty string can be provided for the namespace parameter.
//
// It is possible for a namespace to be empty. This means that the user has access to all namespaces. If the email
// provided corresponds to a user with no namespace, the non-namespaced user will be returned regardless of the
// namespace provided to this function because they have access to all namespaces.
//
// Deprecated: Use UserID when identifying a user directly
func NamespacedEmail(namespace, email string) UserIdentifier {
	return func() *iam_v1.UserIdentifier {
		return &iam_v1.UserIdentifier{UserIdentifier: &iam_v1.UserIdentifier_NamespacedEmail{NamespacedEmail: &iam_v1.NamespacedEmail{Namespace: namespace, Email: email}}}
	}
}

// NamespacedSession returns an identifier to look up the user of a session. Namespace is ignored by server.
// Deprecated: Use TokenIdentifier
func NamespacedSession(namespace, session string) UserIdentifier {
	return func() *iam_v1.UserIdentifier {
		return &iam_v1.UserIdentifier{UserIdentifier: &iam_v1.UserIdentifier_NamespacedSession{NamespacedSession: &iam_v1.NamespacedSession{Namespace: namespace, Session: session}}}
	}
}

// ExternalID returns an identifier to look up a user by an external id.
func ExternalID(externalIDType externalid.Type, externalID string) UserIdentifier {
	return func() *iam_v1.UserIdentifier {
		return &iam_v1.UserIdentifier{
			UserIdentifier: &iam_v1.UserIdentifier_TypedExternalIdentifier{
				TypedExternalIdentifier: &iam_v1.TypedExternalIdentifier{
					ExternalIdType: string(externalIDType),
					ExternalId:     externalID,
				},
			},
		}
	}
}

// SubjectId returns an identifier to look up a user by a subject id.
func SubjectID(subjectID string) UserIdentifier {
	return func() *iam_v1.UserIdentifier {
		return &iam_v1.UserIdentifier{
			UserIdentifier: &iam_v1.UserIdentifier_SubjectId{
				SubjectId: subjectID,
			},
		}
	}
}

type iamClient struct {
	client      iam_v1.IAMClient
	userClient  iam_v1.UserIAMClient
	providerURL string
}

var (
	// ErrInvalidSubjectType is returned when a specific subject type API is passed a subject with the invalid ype.
	ErrInvalidSubjectType = errors.New("Invalid subject type has been found.")
)

var defaultRetryCallOptions = vax.WithRetry(func() vax.Retryer {
	return vax.OnCodes([]codes.Code{
		codes.DeadlineExceeded,
		codes.Unavailable,
		codes.Unknown,
	}, vax.Backoff{
		Initial:    10 * time.Millisecond,
		Max:        300 * time.Millisecond,
		Multiplier: 3,
	})
})
