package accountgroup

import (
	"context"

	"github.com/vendasta/gosdks/config"
	"github.com/vendasta/gosdks/vax"
	"github.com/vendasta/gosdks/verrors"
	"google.golang.org/grpc"
)

type CompetitorServiceClient struct {
	*CompetitorServiceGRPCClient
}

// NewCompetitorServiceClient creates a new instance of the CompetitorServiceClient
// Deprecated: Use NewCompetitorServiceClientWithOptions instead
func NewCompetitorServiceClient(ctx context.Context, e config.Env, enableAuth bool, options ...grpc.DialOption) (*CompetitorServiceClient, error) {
	env, err := vax.GetSDKEnvironment(e)
	if err != nil {
		return nil, err
	}
	c, ok := environmentConfig[env]
	if !ok {
		return nil, verrors.New(verrors.InvalidArgument, "must provide a valid environment")
	}

	grpcClient, err := NewCompetitorServiceGRPCClient(ctx, c.<PERSON>, c.<PERSON>, <PERSON><PERSON>, enableAuth, options...)
	if err != nil {
		return nil, err
	}

	return &CompetitorServiceClient{
		CompetitorServiceGRPCClient: grpcClient,
	}, nil
}

// NewCompetitorServiceClientWithOptions creates a new instance of the CompetitorServiceClient allowing passing in vax options
// Deprecated: Use NewCompetitorServiceClientV3 instead
func NewCompetitorServiceClientWithOptions(ctx context.Context, e config.Env, enableAuth bool, options ...vax.ClientOption) (*CompetitorServiceClient, error) {
	client, _, err := NewCompetitorServiceClientV3(ctx, e, enableAuth, options...)
	return client, err
}

// NewCompetitorServiceClientV3 creates a new instance of the CompetitorServiceClient allowing passing in vax options as well as returning a closer function
// that you should call to clean up the GRPC connection this makes after you are done using this sdk
func NewCompetitorServiceClientV3(ctx context.Context, e config.Env, enableAuth bool, options ...vax.ClientOption) (*CompetitorServiceClient, func() error, error) {
	env, err := vax.GetSDKEnvironment(e)
	if err != nil {
		return nil, nil, err
	}
	c, ok := environmentConfig[env]
	if !ok {
		return nil, nil, verrors.New(verrors.InvalidArgument, "must provide a valid environment")
	}

	grpcClient, closer, err := NewCompetitorServiceGRPCClientV3(ctx, c.Host, c.Secure, c.Scope, enableAuth, options...)
	if err != nil {
		return nil, nil, err
	}

	return &CompetitorServiceClient{
		CompetitorServiceGRPCClient: grpcClient,
	}, closer, nil
}
