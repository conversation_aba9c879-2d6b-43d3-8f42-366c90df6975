// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.21.0-devel
// 	protoc        v3.11.4
// source: atlas/v1/api.proto

package atlas_v1

import (
	context "context"
	proto "github.com/golang/protobuf/proto"
	empty "github.com/golang/protobuf/ptypes/empty"
	_ "github.com/vendasta/generated-protos-go/vendasta_types"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// This is a compile-time assertion that a sufficiently up-to-date version
// of the legacy proto package is being used.
const _ = proto.ProtoPackageIsVersion4

type UITheme int32

const (
	UITheme_UI_THEME_DARK            UITheme = 0
	UITheme_UI_THEME_LIGHT           UITheme = 1
	UITheme_UI_THEME_USER_PREFERENCE UITheme = 2
)

// Enum value maps for UITheme.
var (
	UITheme_name = map[int32]string{
		0: "UI_THEME_DARK",
		1: "UI_THEME_LIGHT",
		2: "UI_THEME_USER_PREFERENCE",
	}
	UITheme_value = map[string]int32{
		"UI_THEME_DARK":            0,
		"UI_THEME_LIGHT":           1,
		"UI_THEME_USER_PREFERENCE": 2,
	}
)

func (x UITheme) Enum() *UITheme {
	p := new(UITheme)
	*p = x
	return p
}

func (x UITheme) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (UITheme) Descriptor() protoreflect.EnumDescriptor {
	return file_atlas_v1_api_proto_enumTypes[0].Descriptor()
}

func (UITheme) Type() protoreflect.EnumType {
	return &file_atlas_v1_api_proto_enumTypes[0]
}

func (x UITheme) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use UITheme.Descriptor instead.
func (UITheme) EnumDescriptor() ([]byte, []int) {
	return file_atlas_v1_api_proto_rawDescGZIP(), []int{0}
}

type UserViewType int32

const (
	UserViewType_USER_VIEW_TYPE_SMB   UserViewType = 0
	UserViewType_USER_VIEW_TYPE_ADMIN UserViewType = 1
)

// Enum value maps for UserViewType.
var (
	UserViewType_name = map[int32]string{
		0: "USER_VIEW_TYPE_SMB",
		1: "USER_VIEW_TYPE_ADMIN",
	}
	UserViewType_value = map[string]int32{
		"USER_VIEW_TYPE_SMB":   0,
		"USER_VIEW_TYPE_ADMIN": 1,
	}
)

func (x UserViewType) Enum() *UserViewType {
	p := new(UserViewType)
	*p = x
	return p
}

func (x UserViewType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (UserViewType) Descriptor() protoreflect.EnumDescriptor {
	return file_atlas_v1_api_proto_enumTypes[1].Descriptor()
}

func (UserViewType) Type() protoreflect.EnumType {
	return &file_atlas_v1_api_proto_enumTypes[1]
}

func (x UserViewType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use UserViewType.Descriptor instead.
func (UserViewType) EnumDescriptor() ([]byte, []int) {
	return file_atlas_v1_api_proto_rawDescGZIP(), []int{1}
}

type PlatformMode int32

const (
	PlatformMode_WEB    PlatformMode = 0
	PlatformMode_MOBILE PlatformMode = 1
)

// Enum value maps for PlatformMode.
var (
	PlatformMode_name = map[int32]string{
		0: "WEB",
		1: "MOBILE",
	}
	PlatformMode_value = map[string]int32{
		"WEB":    0,
		"MOBILE": 1,
	}
)

func (x PlatformMode) Enum() *PlatformMode {
	p := new(PlatformMode)
	*p = x
	return p
}

func (x PlatformMode) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (PlatformMode) Descriptor() protoreflect.EnumDescriptor {
	return file_atlas_v1_api_proto_enumTypes[2].Descriptor()
}

func (PlatformMode) Type() protoreflect.EnumType {
	return &file_atlas_v1_api_proto_enumTypes[2]
}

func (x PlatformMode) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use PlatformMode.Descriptor instead.
func (PlatformMode) EnumDescriptor() ([]byte, []int) {
	return file_atlas_v1_api_proto_rawDescGZIP(), []int{2}
}

// Represents a User navigation item in the Atlas navbar
type UserNavigationItem struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The text of a navigation item displayed in the Atlas navbar
	Text string `protobuf:"bytes,1,opt,name=text,proto3" json:"text,omitempty"`
	// The url to navigate to upon clicking this navigation item
	Url string `protobuf:"bytes,2,opt,name=url,proto3" json:"url,omitempty"`
	// Optionally, this will be emitted from Atlas if the Atlas navbar has been initialized as within an Angular app
	RouteId string `protobuf:"bytes,3,opt,name=route_id,json=routeId,proto3" json:"route_id,omitempty"`
}

func (x *UserNavigationItem) Reset() {
	*x = UserNavigationItem{}
	if protoimpl.UnsafeEnabled {
		mi := &file_atlas_v1_api_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UserNavigationItem) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UserNavigationItem) ProtoMessage() {}

func (x *UserNavigationItem) ProtoReflect() protoreflect.Message {
	mi := &file_atlas_v1_api_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UserNavigationItem.ProtoReflect.Descriptor instead.
func (*UserNavigationItem) Descriptor() ([]byte, []int) {
	return file_atlas_v1_api_proto_rawDescGZIP(), []int{0}
}

func (x *UserNavigationItem) GetText() string {
	if x != nil {
		return x.Text
	}
	return ""
}

func (x *UserNavigationItem) GetUrl() string {
	if x != nil {
		return x.Url
	}
	return ""
}

func (x *UserNavigationItem) GetRouteId() string {
	if x != nil {
		return x.RouteId
	}
	return ""
}

// Represents a Center navigation item in the Atlas navbar
type CenterNavigationItem struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The name of a center navigation item displayed in the Atlas navbar
	Name string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	// Entry URL
	EntryUrl string `protobuf:"bytes,2,opt,name=entry_url,json=entryUrl,proto3" json:"entry_url,omitempty"`
	// The center's unique ID
	CenterId string `protobuf:"bytes,3,opt,name=center_id,json=centerId,proto3" json:"center_id,omitempty"`
}

func (x *CenterNavigationItem) Reset() {
	*x = CenterNavigationItem{}
	if protoimpl.UnsafeEnabled {
		mi := &file_atlas_v1_api_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CenterNavigationItem) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CenterNavigationItem) ProtoMessage() {}

func (x *CenterNavigationItem) ProtoReflect() protoreflect.Message {
	mi := &file_atlas_v1_api_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CenterNavigationItem.ProtoReflect.Descriptor instead.
func (*CenterNavigationItem) Descriptor() ([]byte, []int) {
	return file_atlas_v1_api_proto_rawDescGZIP(), []int{1}
}

func (x *CenterNavigationItem) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *CenterNavigationItem) GetEntryUrl() string {
	if x != nil {
		return x.EntryUrl
	}
	return ""
}

func (x *CenterNavigationItem) GetCenterId() string {
	if x != nil {
		return x.CenterId
	}
	return ""
}

// Request for Atlas.GetData
type GetDataRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// A partner ID
	PartnerId string `protobuf:"bytes,1,opt,name=partner_id,json=partnerId,proto3" json:"partner_id,omitempty"`
	// A market ID
	MarketId string `protobuf:"bytes,2,opt,name=market_id,json=marketId,proto3" json:"market_id,omitempty"`
	// An account group ID
	AccountGroupId string `protobuf:"bytes,3,opt,name=account_group_id,json=accountGroupId,proto3" json:"account_group_id,omitempty"`
	// The next url the user will be taken to when logout is clicked
	SignOutNextUrl string `protobuf:"bytes,4,opt,name=sign_out_next_url,json=signOutNextUrl,proto3" json:"sign_out_next_url,omitempty"`
	// A brand's group path
	GroupPath string `protobuf:"bytes,5,opt,name=group_path,json=groupPath,proto3" json:"group_path,omitempty"`
}

func (x *GetDataRequest) Reset() {
	*x = GetDataRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_atlas_v1_api_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetDataRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetDataRequest) ProtoMessage() {}

func (x *GetDataRequest) ProtoReflect() protoreflect.Message {
	mi := &file_atlas_v1_api_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetDataRequest.ProtoReflect.Descriptor instead.
func (*GetDataRequest) Descriptor() ([]byte, []int) {
	return file_atlas_v1_api_proto_rawDescGZIP(), []int{2}
}

func (x *GetDataRequest) GetPartnerId() string {
	if x != nil {
		return x.PartnerId
	}
	return ""
}

func (x *GetDataRequest) GetMarketId() string {
	if x != nil {
		return x.MarketId
	}
	return ""
}

func (x *GetDataRequest) GetAccountGroupId() string {
	if x != nil {
		return x.AccountGroupId
	}
	return ""
}

func (x *GetDataRequest) GetSignOutNextUrl() string {
	if x != nil {
		return x.SignOutNextUrl
	}
	return ""
}

func (x *GetDataRequest) GetGroupPath() string {
	if x != nil {
		return x.GroupPath
	}
	return ""
}

type LocationData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The name of the business
	BusinessName string `protobuf:"bytes,1,opt,name=business_name,json=businessName,proto3" json:"business_name,omitempty"`
	// The business address
	Address string `protobuf:"bytes,2,opt,name=address,proto3" json:"address,omitempty"`
}

func (x *LocationData) Reset() {
	*x = LocationData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_atlas_v1_api_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LocationData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LocationData) ProtoMessage() {}

func (x *LocationData) ProtoReflect() protoreflect.Message {
	mi := &file_atlas_v1_api_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LocationData.ProtoReflect.Descriptor instead.
func (*LocationData) Descriptor() ([]byte, []int) {
	return file_atlas_v1_api_proto_rawDescGZIP(), []int{3}
}

func (x *LocationData) GetBusinessName() string {
	if x != nil {
		return x.BusinessName
	}
	return ""
}

func (x *LocationData) GetAddress() string {
	if x != nil {
		return x.Address
	}
	return ""
}

type UserSwitcherData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The user's identifier
	UserId string `protobuf:"bytes,1,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	// The partner ID
	PartnerId string `protobuf:"bytes,2,opt,name=partner_id,json=partnerId,proto3" json:"partner_id,omitempty"`
	// Full name of the partner
	PartnerName string `protobuf:"bytes,3,opt,name=partner_name,json=partnerName,proto3" json:"partner_name,omitempty"`
	// URL to switch to the specified user
	EntryUrl string `protobuf:"bytes,4,opt,name=entry_url,json=entryUrl,proto3" json:"entry_url,omitempty"`
}

func (x *UserSwitcherData) Reset() {
	*x = UserSwitcherData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_atlas_v1_api_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UserSwitcherData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UserSwitcherData) ProtoMessage() {}

func (x *UserSwitcherData) ProtoReflect() protoreflect.Message {
	mi := &file_atlas_v1_api_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UserSwitcherData.ProtoReflect.Descriptor instead.
func (*UserSwitcherData) Descriptor() ([]byte, []int) {
	return file_atlas_v1_api_proto_rawDescGZIP(), []int{4}
}

func (x *UserSwitcherData) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

func (x *UserSwitcherData) GetPartnerId() string {
	if x != nil {
		return x.PartnerId
	}
	return ""
}

func (x *UserSwitcherData) GetPartnerName() string {
	if x != nil {
		return x.PartnerName
	}
	return ""
}

func (x *UserSwitcherData) GetEntryUrl() string {
	if x != nil {
		return x.EntryUrl
	}
	return ""
}

// Response for Atlas.GetData
type GetDataResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Navigation items for the user dropdown
	User []*UserNavigationItem `protobuf:"bytes,1,rep,name=user,proto3" json:"user,omitempty"`
	// Navigation items for the centers dropdown
	Centers []*CenterNavigationItem `protobuf:"bytes,2,rep,name=centers,proto3" json:"centers,omitempty"`
	// The username of the user currently logged in
	Username string `protobuf:"bytes,3,opt,name=username,proto3" json:"username,omitempty"`
	// The email of the user currently logged in
	Email string `protobuf:"bytes,4,opt,name=email,proto3" json:"email,omitempty"`
	// Sign out url that the user is taken to when they logout
	SignOutUrl string `protobuf:"bytes,5,opt,name=sign_out_url,json=signOutUrl,proto3" json:"sign_out_url,omitempty"`
	// The UI theme for Atlas
	Theme UITheme `protobuf:"varint,6,opt,name=theme,proto3,enum=atlas.v1.UITheme" json:"theme,omitempty"`
	// The user's currently selected language
	Language string `protobuf:"bytes,7,opt,name=language,proto3" json:"language,omitempty"`
	// Deprecated (use theme): The theming set for the partner and market
	//
	// Deprecated: Do not use.
	Theming *Theming `protobuf:"bytes,8,opt,name=theming,proto3" json:"theming,omitempty"`
	// Denotes whether or not the notifications bell is enabled
	NotificationsEnabled bool `protobuf:"varint,9,opt,name=notifications_enabled,json=notificationsEnabled,proto3" json:"notifications_enabled,omitempty"`
	// The user's identifier
	UserId string `protobuf:"bytes,10,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	// If a business or brand was requested, the location data will be populated
	LocationData *LocationData `protobuf:"bytes,11,opt,name=location_data,json=locationData,proto3" json:"location_data,omitempty"`
	// The username of the impersonatee if the current user is impersonating
	ImpersonateeUsername string `protobuf:"bytes,12,opt,name=impersonatee_username,json=impersonateeUsername,proto3" json:"impersonatee_username,omitempty"`
	// Whether the currently logged in user's email address is verified
	EmailVerified bool `protobuf:"varint,13,opt,name=email_verified,json=emailVerified,proto3" json:"email_verified,omitempty"`
	// List of users that the current user can switch to
	UserSwitcherData []*UserSwitcherData `protobuf:"bytes,14,rep,name=user_switcher_data,json=userSwitcherData,proto3" json:"user_switcher_data,omitempty"`
	// The name of the current partner
	PartnerName string `protobuf:"bytes,15,opt,name=partner_name,json=partnerName,proto3" json:"partner_name,omitempty"`
	// The theme for business app
	BusinessAppUiTheme UITheme `protobuf:"varint,16,opt,name=business_app_ui_theme,json=businessAppUiTheme,proto3,enum=atlas.v1.UITheme" json:"business_app_ui_theme,omitempty"`
}

func (x *GetDataResponse) Reset() {
	*x = GetDataResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_atlas_v1_api_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetDataResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetDataResponse) ProtoMessage() {}

func (x *GetDataResponse) ProtoReflect() protoreflect.Message {
	mi := &file_atlas_v1_api_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetDataResponse.ProtoReflect.Descriptor instead.
func (*GetDataResponse) Descriptor() ([]byte, []int) {
	return file_atlas_v1_api_proto_rawDescGZIP(), []int{5}
}

func (x *GetDataResponse) GetUser() []*UserNavigationItem {
	if x != nil {
		return x.User
	}
	return nil
}

func (x *GetDataResponse) GetCenters() []*CenterNavigationItem {
	if x != nil {
		return x.Centers
	}
	return nil
}

func (x *GetDataResponse) GetUsername() string {
	if x != nil {
		return x.Username
	}
	return ""
}

func (x *GetDataResponse) GetEmail() string {
	if x != nil {
		return x.Email
	}
	return ""
}

func (x *GetDataResponse) GetSignOutUrl() string {
	if x != nil {
		return x.SignOutUrl
	}
	return ""
}

func (x *GetDataResponse) GetTheme() UITheme {
	if x != nil {
		return x.Theme
	}
	return UITheme_UI_THEME_DARK
}

func (x *GetDataResponse) GetLanguage() string {
	if x != nil {
		return x.Language
	}
	return ""
}

// Deprecated: Do not use.
func (x *GetDataResponse) GetTheming() *Theming {
	if x != nil {
		return x.Theming
	}
	return nil
}

func (x *GetDataResponse) GetNotificationsEnabled() bool {
	if x != nil {
		return x.NotificationsEnabled
	}
	return false
}

func (x *GetDataResponse) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

func (x *GetDataResponse) GetLocationData() *LocationData {
	if x != nil {
		return x.LocationData
	}
	return nil
}

func (x *GetDataResponse) GetImpersonateeUsername() string {
	if x != nil {
		return x.ImpersonateeUsername
	}
	return ""
}

func (x *GetDataResponse) GetEmailVerified() bool {
	if x != nil {
		return x.EmailVerified
	}
	return false
}

func (x *GetDataResponse) GetUserSwitcherData() []*UserSwitcherData {
	if x != nil {
		return x.UserSwitcherData
	}
	return nil
}

func (x *GetDataResponse) GetPartnerName() string {
	if x != nil {
		return x.PartnerName
	}
	return ""
}

func (x *GetDataResponse) GetBusinessAppUiTheme() UITheme {
	if x != nil {
		return x.BusinessAppUiTheme
	}
	return UITheme_UI_THEME_DARK
}

// Request for Atlas.GetNavigationData
type GetNavigationDataRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// An account group ID
	AccountGroupId string `protobuf:"bytes,1,opt,name=account_group_id,json=accountGroupId,proto3" json:"account_group_id,omitempty"`
	// A brand's group path
	GroupPath string `protobuf:"bytes,2,opt,name=group_path,json=groupPath,proto3" json:"group_path,omitempty"`
	// The partner ID
	PartnerId string `protobuf:"bytes,3,opt,name=partner_id,json=partnerId,proto3" json:"partner_id,omitempty"`
	// The market ID
	MarketId string `protobuf:"bytes,4,opt,name=market_id,json=marketId,proto3" json:"market_id,omitempty"`
	// Platform mode whether is mobile or web version
	PlatformMode PlatformMode `protobuf:"varint,5,opt,name=platform_mode,json=platformMode,proto3,enum=atlas.v1.PlatformMode" json:"platform_mode,omitempty"`
}

func (x *GetNavigationDataRequest) Reset() {
	*x = GetNavigationDataRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_atlas_v1_api_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetNavigationDataRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetNavigationDataRequest) ProtoMessage() {}

func (x *GetNavigationDataRequest) ProtoReflect() protoreflect.Message {
	mi := &file_atlas_v1_api_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetNavigationDataRequest.ProtoReflect.Descriptor instead.
func (*GetNavigationDataRequest) Descriptor() ([]byte, []int) {
	return file_atlas_v1_api_proto_rawDescGZIP(), []int{6}
}

func (x *GetNavigationDataRequest) GetAccountGroupId() string {
	if x != nil {
		return x.AccountGroupId
	}
	return ""
}

func (x *GetNavigationDataRequest) GetGroupPath() string {
	if x != nil {
		return x.GroupPath
	}
	return ""
}

func (x *GetNavigationDataRequest) GetPartnerId() string {
	if x != nil {
		return x.PartnerId
	}
	return ""
}

func (x *GetNavigationDataRequest) GetMarketId() string {
	if x != nil {
		return x.MarketId
	}
	return ""
}

func (x *GetNavigationDataRequest) GetPlatformMode() PlatformMode {
	if x != nil {
		return x.PlatformMode
	}
	return PlatformMode_WEB
}

// Request for Atlas.GetSalesInfoRequest
type GetSalesInfoRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// An account group ID
	AccountGroupId string `protobuf:"bytes,1,opt,name=account_group_id,json=accountGroupId,proto3" json:"account_group_id,omitempty"`
	// A brand's group path
	GroupPath string `protobuf:"bytes,2,opt,name=group_path,json=groupPath,proto3" json:"group_path,omitempty"`
}

func (x *GetSalesInfoRequest) Reset() {
	*x = GetSalesInfoRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_atlas_v1_api_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetSalesInfoRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetSalesInfoRequest) ProtoMessage() {}

func (x *GetSalesInfoRequest) ProtoReflect() protoreflect.Message {
	mi := &file_atlas_v1_api_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetSalesInfoRequest.ProtoReflect.Descriptor instead.
func (*GetSalesInfoRequest) Descriptor() ([]byte, []int) {
	return file_atlas_v1_api_proto_rawDescGZIP(), []int{7}
}

func (x *GetSalesInfoRequest) GetAccountGroupId() string {
	if x != nil {
		return x.AccountGroupId
	}
	return ""
}

func (x *GetSalesInfoRequest) GetGroupPath() string {
	if x != nil {
		return x.GroupPath
	}
	return ""
}

// Response for Atlas.GetSalesInfoResponse
type GetSalesInfoResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The sales information
	SalesInfo *SalesInfo `protobuf:"bytes,3,opt,name=sales_info,json=salesInfo,proto3" json:"sales_info,omitempty"`
}

func (x *GetSalesInfoResponse) Reset() {
	*x = GetSalesInfoResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_atlas_v1_api_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetSalesInfoResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetSalesInfoResponse) ProtoMessage() {}

func (x *GetSalesInfoResponse) ProtoReflect() protoreflect.Message {
	mi := &file_atlas_v1_api_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetSalesInfoResponse.ProtoReflect.Descriptor instead.
func (*GetSalesInfoResponse) Descriptor() ([]byte, []int) {
	return file_atlas_v1_api_proto_rawDescGZIP(), []int{8}
}

func (x *GetSalesInfoResponse) GetSalesInfo() *SalesInfo {
	if x != nil {
		return x.SalesInfo
	}
	return nil
}

// The Container Item for the Side Navigation Bar
type SideNavigationSection struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The id of the translation to use
	TranslationId string `protobuf:"bytes,1,opt,name=translation_id,json=translationId,proto3" json:"translation_id,omitempty"`
	// A repeated item for the side nav
	SideNavigationItems []*SideNavigationItem `protobuf:"bytes,2,rep,name=side_navigation_items,json=sideNavigationItems,proto3" json:"side_navigation_items,omitempty"`
	// the default label in the case that a translation id does not exist
	Label string `protobuf:"bytes,3,opt,name=label,proto3" json:"label,omitempty"`
	// Any text content to be shown in a chip on the item
	ChipContent string `protobuf:"bytes,4,opt,name=chip_content,json=chipContent,proto3" json:"chip_content,omitempty"`
}

func (x *SideNavigationSection) Reset() {
	*x = SideNavigationSection{}
	if protoimpl.UnsafeEnabled {
		mi := &file_atlas_v1_api_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SideNavigationSection) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SideNavigationSection) ProtoMessage() {}

func (x *SideNavigationSection) ProtoReflect() protoreflect.Message {
	mi := &file_atlas_v1_api_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SideNavigationSection.ProtoReflect.Descriptor instead.
func (*SideNavigationSection) Descriptor() ([]byte, []int) {
	return file_atlas_v1_api_proto_rawDescGZIP(), []int{9}
}

func (x *SideNavigationSection) GetTranslationId() string {
	if x != nil {
		return x.TranslationId
	}
	return ""
}

func (x *SideNavigationSection) GetSideNavigationItems() []*SideNavigationItem {
	if x != nil {
		return x.SideNavigationItems
	}
	return nil
}

func (x *SideNavigationSection) GetLabel() string {
	if x != nil {
		return x.Label
	}
	return ""
}

func (x *SideNavigationSection) GetChipContent() string {
	if x != nil {
		return x.ChipContent
	}
	return ""
}

// The Container Item for the Side Navigation Bar
type SideNavigationContainer struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The id of the translation to use
	TranslationId string `protobuf:"bytes,1,opt,name=translation_id,json=translationId,proto3" json:"translation_id,omitempty"`
	// A repeated item for the side nav
	SideNavigationItems []*SideNavigationItem `protobuf:"bytes,2,rep,name=side_navigation_items,json=sideNavigationItems,proto3" json:"side_navigation_items,omitempty"`
	// The icon of the container
	Icon string `protobuf:"bytes,3,opt,name=icon,proto3" json:"icon,omitempty"`
	// The logo url for the products icon
	LogoUrl string `protobuf:"bytes,4,opt,name=logo_url,json=logoUrl,proto3" json:"logo_url,omitempty"`
	// the default label in the case that a translation id does not exist
	Label string `protobuf:"bytes,5,opt,name=label,proto3" json:"label,omitempty"`
	// Whether or not the icon be shown
	ShowIcon bool `protobuf:"varint,6,opt,name=show_icon,json=showIcon,proto3" json:"show_icon,omitempty"`
	// Any text content to be shown in a chip on the item
	ChipContent string `protobuf:"bytes,7,opt,name=chip_content,json=chipContent,proto3" json:"chip_content,omitempty"`
	// A URL that can be accessed by clicking on the right side of the container
	Url string `protobuf:"bytes,8,opt,name=url,proto3" json:"url,omitempty"`
	// Whether or not a link is pinnable
	Pinnable bool `protobuf:"varint,9,opt,name=pinnable,proto3" json:"pinnable,omitempty"`
	// The navigation ID
	NavigationId string `protobuf:"bytes,10,opt,name=navigation_id,json=navigationId,proto3" json:"navigation_id,omitempty"`
}

func (x *SideNavigationContainer) Reset() {
	*x = SideNavigationContainer{}
	if protoimpl.UnsafeEnabled {
		mi := &file_atlas_v1_api_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SideNavigationContainer) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SideNavigationContainer) ProtoMessage() {}

func (x *SideNavigationContainer) ProtoReflect() protoreflect.Message {
	mi := &file_atlas_v1_api_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SideNavigationContainer.ProtoReflect.Descriptor instead.
func (*SideNavigationContainer) Descriptor() ([]byte, []int) {
	return file_atlas_v1_api_proto_rawDescGZIP(), []int{10}
}

func (x *SideNavigationContainer) GetTranslationId() string {
	if x != nil {
		return x.TranslationId
	}
	return ""
}

func (x *SideNavigationContainer) GetSideNavigationItems() []*SideNavigationItem {
	if x != nil {
		return x.SideNavigationItems
	}
	return nil
}

func (x *SideNavigationContainer) GetIcon() string {
	if x != nil {
		return x.Icon
	}
	return ""
}

func (x *SideNavigationContainer) GetLogoUrl() string {
	if x != nil {
		return x.LogoUrl
	}
	return ""
}

func (x *SideNavigationContainer) GetLabel() string {
	if x != nil {
		return x.Label
	}
	return ""
}

func (x *SideNavigationContainer) GetShowIcon() bool {
	if x != nil {
		return x.ShowIcon
	}
	return false
}

func (x *SideNavigationContainer) GetChipContent() string {
	if x != nil {
		return x.ChipContent
	}
	return ""
}

func (x *SideNavigationContainer) GetUrl() string {
	if x != nil {
		return x.Url
	}
	return ""
}

func (x *SideNavigationContainer) GetPinnable() bool {
	if x != nil {
		return x.Pinnable
	}
	return false
}

func (x *SideNavigationContainer) GetNavigationId() string {
	if x != nil {
		return x.NavigationId
	}
	return ""
}

// The navigation link
type SideNavigationLink struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The navigation ID
	NavigationId string `protobuf:"bytes,1,opt,name=navigation_id,json=navigationId,proto3" json:"navigation_id,omitempty"`
	// The place the link takes you
	Url string `protobuf:"bytes,2,opt,name=url,proto3" json:"url,omitempty"`
	// The path that angular would navigate you to, assuming this link takes you to a route in the angular app
	Path string `protobuf:"bytes,3,opt,name=path,proto3" json:"path,omitempty"`
	// The service provider ID that owns this link
	ServiceProviderId string `protobuf:"bytes,4,opt,name=service_provider_id,json=serviceProviderId,proto3" json:"service_provider_id,omitempty"`
	// The logo url for the products icon
	LogoUrl string `protobuf:"bytes,5,opt,name=logo_url,json=logoUrl,proto3" json:"logo_url,omitempty"`
	// The name of the material icon to use
	Icon string `protobuf:"bytes,6,opt,name=icon,proto3" json:"icon,omitempty"`
	// The ID for the translation of this Link
	TranslationId string `protobuf:"bytes,7,opt,name=translation_id,json=translationId,proto3" json:"translation_id,omitempty"`
	// Whether or not this link is for an external page
	External bool `protobuf:"varint,8,opt,name=external,proto3" json:"external,omitempty"`
	// the default label in the case that a translation id does not exist
	Label string `protobuf:"bytes,9,opt,name=label,proto3" json:"label,omitempty"`
	// Whether or not the icon be shown
	ShowIcon bool `protobuf:"varint,10,opt,name=show_icon,json=showIcon,proto3" json:"show_icon,omitempty"`
	// Whether or not a link is pinnable
	Pinnable bool `protobuf:"varint,11,opt,name=pinnable,proto3" json:"pinnable,omitempty"`
	// Any text content to be shown in a chip on the item
	ChipContent string `protobuf:"bytes,12,opt,name=chip_content,json=chipContent,proto3" json:"chip_content,omitempty"`
	// A flag to indicate whether an activation is a trial
	IsTrial bool `protobuf:"varint,13,opt,name=is_trial,json=isTrial,proto3" json:"is_trial,omitempty"`
	// A flag to show an auth wall when navigation is attempted for items that require a user on the account group
	UserRequired bool `protobuf:"varint,14,opt,name=user_required,json=userRequired,proto3" json:"user_required,omitempty"`
	// A flag to indicate if we should open a new tab when clicked
	OpenInNewTab bool `protobuf:"varint,15,opt,name=open_in_new_tab,json=openInNewTab,proto3" json:"open_in_new_tab,omitempty"`
	// Subrouted links
	SubLinks []*SideNavigationLink `protobuf:"bytes,16,rep,name=sub_links,json=subLinks,proto3" json:"sub_links,omitempty"`
	// This url is used as the navigation target for a launch button on the links respective page.
	LaunchUrl string `protobuf:"bytes,17,opt,name=launch_url,json=launchUrl,proto3" json:"launch_url,omitempty"`
	// The ID for the translation of this Link's description
	DescriptionTranslationId string `protobuf:"bytes,18,opt,name=description_translation_id,json=descriptionTranslationId,proto3" json:"description_translation_id,omitempty"`
}

func (x *SideNavigationLink) Reset() {
	*x = SideNavigationLink{}
	if protoimpl.UnsafeEnabled {
		mi := &file_atlas_v1_api_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SideNavigationLink) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SideNavigationLink) ProtoMessage() {}

func (x *SideNavigationLink) ProtoReflect() protoreflect.Message {
	mi := &file_atlas_v1_api_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SideNavigationLink.ProtoReflect.Descriptor instead.
func (*SideNavigationLink) Descriptor() ([]byte, []int) {
	return file_atlas_v1_api_proto_rawDescGZIP(), []int{11}
}

func (x *SideNavigationLink) GetNavigationId() string {
	if x != nil {
		return x.NavigationId
	}
	return ""
}

func (x *SideNavigationLink) GetUrl() string {
	if x != nil {
		return x.Url
	}
	return ""
}

func (x *SideNavigationLink) GetPath() string {
	if x != nil {
		return x.Path
	}
	return ""
}

func (x *SideNavigationLink) GetServiceProviderId() string {
	if x != nil {
		return x.ServiceProviderId
	}
	return ""
}

func (x *SideNavigationLink) GetLogoUrl() string {
	if x != nil {
		return x.LogoUrl
	}
	return ""
}

func (x *SideNavigationLink) GetIcon() string {
	if x != nil {
		return x.Icon
	}
	return ""
}

func (x *SideNavigationLink) GetTranslationId() string {
	if x != nil {
		return x.TranslationId
	}
	return ""
}

func (x *SideNavigationLink) GetExternal() bool {
	if x != nil {
		return x.External
	}
	return false
}

func (x *SideNavigationLink) GetLabel() string {
	if x != nil {
		return x.Label
	}
	return ""
}

func (x *SideNavigationLink) GetShowIcon() bool {
	if x != nil {
		return x.ShowIcon
	}
	return false
}

func (x *SideNavigationLink) GetPinnable() bool {
	if x != nil {
		return x.Pinnable
	}
	return false
}

func (x *SideNavigationLink) GetChipContent() string {
	if x != nil {
		return x.ChipContent
	}
	return ""
}

func (x *SideNavigationLink) GetIsTrial() bool {
	if x != nil {
		return x.IsTrial
	}
	return false
}

func (x *SideNavigationLink) GetUserRequired() bool {
	if x != nil {
		return x.UserRequired
	}
	return false
}

func (x *SideNavigationLink) GetOpenInNewTab() bool {
	if x != nil {
		return x.OpenInNewTab
	}
	return false
}

func (x *SideNavigationLink) GetSubLinks() []*SideNavigationLink {
	if x != nil {
		return x.SubLinks
	}
	return nil
}

func (x *SideNavigationLink) GetLaunchUrl() string {
	if x != nil {
		return x.LaunchUrl
	}
	return ""
}

func (x *SideNavigationLink) GetDescriptionTranslationId() string {
	if x != nil {
		return x.DescriptionTranslationId
	}
	return ""
}

// The navigation link for atlas' dropdown
type DropdownItem struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The place the link takes you
	Url string `protobuf:"bytes,1,opt,name=url,proto3" json:"url,omitempty"`
	// The path that angular would navigate you to, assuming this link takes you to a route in the angular app
	Path string `protobuf:"bytes,2,opt,name=path,proto3" json:"path,omitempty"`
	// The ID for the translation of this Link
	TranslationId string `protobuf:"bytes,3,opt,name=translation_id,json=translationId,proto3" json:"translation_id,omitempty"`
	// the default label in the case that a translation id does not exist
	Label string `protobuf:"bytes,4,opt,name=label,proto3" json:"label,omitempty"`
}

func (x *DropdownItem) Reset() {
	*x = DropdownItem{}
	if protoimpl.UnsafeEnabled {
		mi := &file_atlas_v1_api_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DropdownItem) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DropdownItem) ProtoMessage() {}

func (x *DropdownItem) ProtoReflect() protoreflect.Message {
	mi := &file_atlas_v1_api_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DropdownItem.ProtoReflect.Descriptor instead.
func (*DropdownItem) Descriptor() ([]byte, []int) {
	return file_atlas_v1_api_proto_rawDescGZIP(), []int{12}
}

func (x *DropdownItem) GetUrl() string {
	if x != nil {
		return x.Url
	}
	return ""
}

func (x *DropdownItem) GetPath() string {
	if x != nil {
		return x.Path
	}
	return ""
}

func (x *DropdownItem) GetTranslationId() string {
	if x != nil {
		return x.TranslationId
	}
	return ""
}

func (x *DropdownItem) GetLabel() string {
	if x != nil {
		return x.Label
	}
	return ""
}

type SideNavigationItem struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The actual item
	//
	// Types that are assignable to Item:
	//	*SideNavigationItem_SideNavigationSection
	//	*SideNavigationItem_SideNavigationContainer
	//	*SideNavigationItem_SideNavigationLink
	Item isSideNavigationItem_Item `protobuf_oneof:"item"`
}

func (x *SideNavigationItem) Reset() {
	*x = SideNavigationItem{}
	if protoimpl.UnsafeEnabled {
		mi := &file_atlas_v1_api_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SideNavigationItem) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SideNavigationItem) ProtoMessage() {}

func (x *SideNavigationItem) ProtoReflect() protoreflect.Message {
	mi := &file_atlas_v1_api_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SideNavigationItem.ProtoReflect.Descriptor instead.
func (*SideNavigationItem) Descriptor() ([]byte, []int) {
	return file_atlas_v1_api_proto_rawDescGZIP(), []int{13}
}

func (m *SideNavigationItem) GetItem() isSideNavigationItem_Item {
	if m != nil {
		return m.Item
	}
	return nil
}

func (x *SideNavigationItem) GetSideNavigationSection() *SideNavigationSection {
	if x, ok := x.GetItem().(*SideNavigationItem_SideNavigationSection); ok {
		return x.SideNavigationSection
	}
	return nil
}

func (x *SideNavigationItem) GetSideNavigationContainer() *SideNavigationContainer {
	if x, ok := x.GetItem().(*SideNavigationItem_SideNavigationContainer); ok {
		return x.SideNavigationContainer
	}
	return nil
}

func (x *SideNavigationItem) GetSideNavigationLink() *SideNavigationLink {
	if x, ok := x.GetItem().(*SideNavigationItem_SideNavigationLink); ok {
		return x.SideNavigationLink
	}
	return nil
}

type isSideNavigationItem_Item interface {
	isSideNavigationItem_Item()
}

type SideNavigationItem_SideNavigationSection struct {
	// It could be a section
	SideNavigationSection *SideNavigationSection `protobuf:"bytes,1,opt,name=side_navigation_section,json=sideNavigationSection,proto3,oneof"`
}

type SideNavigationItem_SideNavigationContainer struct {
	// It could also be a container
	SideNavigationContainer *SideNavigationContainer `protobuf:"bytes,2,opt,name=side_navigation_container,json=sideNavigationContainer,proto3,oneof"`
}

type SideNavigationItem_SideNavigationLink struct {
	// It could also just be a normal navigation link
	SideNavigationLink *SideNavigationLink `protobuf:"bytes,3,opt,name=side_navigation_link,json=sideNavigationLink,proto3,oneof"`
}

func (*SideNavigationItem_SideNavigationSection) isSideNavigationItem_Item() {}

func (*SideNavigationItem_SideNavigationContainer) isSideNavigationItem_Item() {}

func (*SideNavigationItem_SideNavigationLink) isSideNavigationItem_Item() {}

type SalesInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The market name
	MarketName string `protobuf:"bytes,1,opt,name=market_name,json=marketName,proto3" json:"market_name,omitempty"`
	// The sales contact information
	SalesContact *SalesContact `protobuf:"bytes,2,opt,name=sales_contact,json=salesContact,proto3" json:"sales_contact,omitempty"`
}

func (x *SalesInfo) Reset() {
	*x = SalesInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_atlas_v1_api_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SalesInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SalesInfo) ProtoMessage() {}

func (x *SalesInfo) ProtoReflect() protoreflect.Message {
	mi := &file_atlas_v1_api_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SalesInfo.ProtoReflect.Descriptor instead.
func (*SalesInfo) Descriptor() ([]byte, []int) {
	return file_atlas_v1_api_proto_rawDescGZIP(), []int{14}
}

func (x *SalesInfo) GetMarketName() string {
	if x != nil {
		return x.MarketName
	}
	return ""
}

func (x *SalesInfo) GetSalesContact() *SalesContact {
	if x != nil {
		return x.SalesContact
	}
	return nil
}

type SalesContact struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The salesperson id
	SalesPersonId string `protobuf:"bytes,1,opt,name=sales_person_id,json=salesPersonId,proto3" json:"sales_person_id,omitempty"`
	// The salesperson's email
	Email string `protobuf:"bytes,2,opt,name=email,proto3" json:"email,omitempty"`
	// The salesperson's first name
	FirstName string `protobuf:"bytes,3,opt,name=first_name,json=firstName,proto3" json:"first_name,omitempty"`
	// The salesperson's last name
	LastName string `protobuf:"bytes,4,opt,name=last_name,json=lastName,proto3" json:"last_name,omitempty"`
	// The salesperson's phone number
	PhoneNumber string `protobuf:"bytes,5,opt,name=phone_number,json=phoneNumber,proto3" json:"phone_number,omitempty"`
	// The salesperson's secure photo URL
	PhotoUrlSecure string `protobuf:"bytes,6,opt,name=photo_url_secure,json=photoUrlSecure,proto3" json:"photo_url_secure,omitempty"`
	// The salesperson's job title
	JobTitle string `protobuf:"bytes,7,opt,name=job_title,json=jobTitle,proto3" json:"job_title,omitempty"`
	// The salesperson's country
	Country string `protobuf:"bytes,8,opt,name=country,proto3" json:"country,omitempty"`
	// The user's meeting booking url
	MeetingBookingUrl string `protobuf:"bytes,9,opt,name=meeting_booking_url,json=meetingBookingUrl,proto3" json:"meeting_booking_url,omitempty"`
}

func (x *SalesContact) Reset() {
	*x = SalesContact{}
	if protoimpl.UnsafeEnabled {
		mi := &file_atlas_v1_api_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SalesContact) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SalesContact) ProtoMessage() {}

func (x *SalesContact) ProtoReflect() protoreflect.Message {
	mi := &file_atlas_v1_api_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SalesContact.ProtoReflect.Descriptor instead.
func (*SalesContact) Descriptor() ([]byte, []int) {
	return file_atlas_v1_api_proto_rawDescGZIP(), []int{15}
}

func (x *SalesContact) GetSalesPersonId() string {
	if x != nil {
		return x.SalesPersonId
	}
	return ""
}

func (x *SalesContact) GetEmail() string {
	if x != nil {
		return x.Email
	}
	return ""
}

func (x *SalesContact) GetFirstName() string {
	if x != nil {
		return x.FirstName
	}
	return ""
}

func (x *SalesContact) GetLastName() string {
	if x != nil {
		return x.LastName
	}
	return ""
}

func (x *SalesContact) GetPhoneNumber() string {
	if x != nil {
		return x.PhoneNumber
	}
	return ""
}

func (x *SalesContact) GetPhotoUrlSecure() string {
	if x != nil {
		return x.PhotoUrlSecure
	}
	return ""
}

func (x *SalesContact) GetJobTitle() string {
	if x != nil {
		return x.JobTitle
	}
	return ""
}

func (x *SalesContact) GetCountry() string {
	if x != nil {
		return x.Country
	}
	return ""
}

func (x *SalesContact) GetMeetingBookingUrl() string {
	if x != nil {
		return x.MeetingBookingUrl
	}
	return ""
}

// A representation of a brand
type Brand struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The name of the brand
	Name string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	// The path nodes of the brand
	PathNodes []string `protobuf:"bytes,2,rep,name=path_nodes,json=pathNodes,proto3" json:"path_nodes,omitempty"`
	// Whether or not access is granted
	HasAccess bool `protobuf:"varint,3,opt,name=has_access,json=hasAccess,proto3" json:"has_access,omitempty"`
	// The url to the brand
	Url string `protobuf:"bytes,4,opt,name=url,proto3" json:"url,omitempty"`
}

func (x *Brand) Reset() {
	*x = Brand{}
	if protoimpl.UnsafeEnabled {
		mi := &file_atlas_v1_api_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Brand) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Brand) ProtoMessage() {}

func (x *Brand) ProtoReflect() protoreflect.Message {
	mi := &file_atlas_v1_api_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Brand.ProtoReflect.Descriptor instead.
func (*Brand) Descriptor() ([]byte, []int) {
	return file_atlas_v1_api_proto_rawDescGZIP(), []int{16}
}

func (x *Brand) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *Brand) GetPathNodes() []string {
	if x != nil {
		return x.PathNodes
	}
	return nil
}

func (x *Brand) GetHasAccess() bool {
	if x != nil {
		return x.HasAccess
	}
	return false
}

func (x *Brand) GetUrl() string {
	if x != nil {
		return x.Url
	}
	return ""
}

// An item which would be pinned in the sidenav
type PinnedItem struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The ID of the pinned navigation item
	NavigationId string `protobuf:"bytes,1,opt,name=navigation_id,json=navigationId,proto3" json:"navigation_id,omitempty"`
}

func (x *PinnedItem) Reset() {
	*x = PinnedItem{}
	if protoimpl.UnsafeEnabled {
		mi := &file_atlas_v1_api_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PinnedItem) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PinnedItem) ProtoMessage() {}

func (x *PinnedItem) ProtoReflect() protoreflect.Message {
	mi := &file_atlas_v1_api_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PinnedItem.ProtoReflect.Descriptor instead.
func (*PinnedItem) Descriptor() ([]byte, []int) {
	return file_atlas_v1_api_proto_rawDescGZIP(), []int{17}
}

func (x *PinnedItem) GetNavigationId() string {
	if x != nil {
		return x.NavigationId
	}
	return ""
}

type ExitLinkConfiguration struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// the text to display for the exit link (back to dashboard)
	ExitLinkText string `protobuf:"bytes,1,opt,name=exit_link_text,json=exitLinkText,proto3" json:"exit_link_text,omitempty"`
	// the url to redirect to when the exit link is clicked (https://sso.your-identity-provider.com)
	ExitLinkUrl string `protobuf:"bytes,2,opt,name=exit_link_url,json=exitLinkUrl,proto3" json:"exit_link_url,omitempty"`
}

func (x *ExitLinkConfiguration) Reset() {
	*x = ExitLinkConfiguration{}
	if protoimpl.UnsafeEnabled {
		mi := &file_atlas_v1_api_proto_msgTypes[18]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ExitLinkConfiguration) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ExitLinkConfiguration) ProtoMessage() {}

func (x *ExitLinkConfiguration) ProtoReflect() protoreflect.Message {
	mi := &file_atlas_v1_api_proto_msgTypes[18]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ExitLinkConfiguration.ProtoReflect.Descriptor instead.
func (*ExitLinkConfiguration) Descriptor() ([]byte, []int) {
	return file_atlas_v1_api_proto_rawDescGZIP(), []int{18}
}

func (x *ExitLinkConfiguration) GetExitLinkText() string {
	if x != nil {
		return x.ExitLinkText
	}
	return ""
}

func (x *ExitLinkConfiguration) GetExitLinkUrl() string {
	if x != nil {
		return x.ExitLinkUrl
	}
	return ""
}

// Branding contains the major visual data for a branded partner/market context
type Branding struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Major theme of the UI -- Dark/Light
	Theme UITheme `protobuf:"varint,1,opt,name=theme,proto3,enum=atlas.v1.UITheme" json:"theme,omitempty"`
	// The URL of the partner's logo
	LogoUrl string `protobuf:"bytes,2,opt,name=logo_url,json=logoUrl,proto3" json:"logo_url,omitempty"`
	// The partner's name
	PartnerName string `protobuf:"bytes,3,opt,name=partner_name,json=partnerName,proto3" json:"partner_name,omitempty"`
	// The whitelabeled business center name
	CenterName string `protobuf:"bytes,4,opt,name=center_name,json=centerName,proto3" json:"center_name,omitempty"`
	// The theming set for the partner and market
	//
	// Deprecated: Do not use.
	Theming *Theming `protobuf:"bytes,5,opt,name=theming,proto3" json:"theming,omitempty"`
	// The cobranding-logo url to show for the tiers with cobranding
	CobrandingLogoUrl string `protobuf:"bytes,6,opt,name=cobranding_logo_url,json=cobrandingLogoUrl,proto3" json:"cobranding_logo_url,omitempty"`
	// market name
	MarketName string `protobuf:"bytes,7,opt,name=market_name,json=marketName,proto3" json:"market_name,omitempty"`
	// Optional dark mode logo, used when theme is set to dark mode
	DarkModeLogoUrl string `protobuf:"bytes,8,opt,name=dark_mode_logo_url,json=darkModeLogoUrl,proto3" json:"dark_mode_logo_url,omitempty"`
	// The business app ui theme
	BusinessAppUiTheme UITheme `protobuf:"varint,9,opt,name=business_app_ui_theme,json=businessAppUiTheme,proto3,enum=atlas.v1.UITheme" json:"business_app_ui_theme,omitempty"`
	// The exit link configuration
	ExitLinkConfiguration *ExitLinkConfiguration `protobuf:"bytes,10,opt,name=exit_link_configuration,json=exitLinkConfiguration,proto3" json:"exit_link_configuration,omitempty"`
}

func (x *Branding) Reset() {
	*x = Branding{}
	if protoimpl.UnsafeEnabled {
		mi := &file_atlas_v1_api_proto_msgTypes[19]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Branding) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Branding) ProtoMessage() {}

func (x *Branding) ProtoReflect() protoreflect.Message {
	mi := &file_atlas_v1_api_proto_msgTypes[19]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Branding.ProtoReflect.Descriptor instead.
func (*Branding) Descriptor() ([]byte, []int) {
	return file_atlas_v1_api_proto_rawDescGZIP(), []int{19}
}

func (x *Branding) GetTheme() UITheme {
	if x != nil {
		return x.Theme
	}
	return UITheme_UI_THEME_DARK
}

func (x *Branding) GetLogoUrl() string {
	if x != nil {
		return x.LogoUrl
	}
	return ""
}

func (x *Branding) GetPartnerName() string {
	if x != nil {
		return x.PartnerName
	}
	return ""
}

func (x *Branding) GetCenterName() string {
	if x != nil {
		return x.CenterName
	}
	return ""
}

// Deprecated: Do not use.
func (x *Branding) GetTheming() *Theming {
	if x != nil {
		return x.Theming
	}
	return nil
}

func (x *Branding) GetCobrandingLogoUrl() string {
	if x != nil {
		return x.CobrandingLogoUrl
	}
	return ""
}

func (x *Branding) GetMarketName() string {
	if x != nil {
		return x.MarketName
	}
	return ""
}

func (x *Branding) GetDarkModeLogoUrl() string {
	if x != nil {
		return x.DarkModeLogoUrl
	}
	return ""
}

func (x *Branding) GetBusinessAppUiTheme() UITheme {
	if x != nil {
		return x.BusinessAppUiTheme
	}
	return UITheme_UI_THEME_DARK
}

func (x *Branding) GetExitLinkConfiguration() *ExitLinkConfiguration {
	if x != nil {
		return x.ExitLinkConfiguration
	}
	return nil
}

// A representation of the information on an account group that we're interested in
type AccountGroup struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The ID of the account group
	AccountGroupId string `protobuf:"bytes,1,opt,name=account_group_id,json=accountGroupId,proto3" json:"account_group_id,omitempty"`
	// The name of the account group
	Name string `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	// The address of the account group
	Address string `protobuf:"bytes,3,opt,name=address,proto3" json:"address,omitempty"`
	// The list of activated aproducts on the account group
	ActivatedProductIds []string `protobuf:"bytes,4,rep,name=activated_product_ids,json=activatedProductIds,proto3" json:"activated_product_ids,omitempty"`
	// The url to the account group
	Url string `protobuf:"bytes,5,opt,name=url,proto3" json:"url,omitempty"`
}

func (x *AccountGroup) Reset() {
	*x = AccountGroup{}
	if protoimpl.UnsafeEnabled {
		mi := &file_atlas_v1_api_proto_msgTypes[20]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AccountGroup) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AccountGroup) ProtoMessage() {}

func (x *AccountGroup) ProtoReflect() protoreflect.Message {
	mi := &file_atlas_v1_api_proto_msgTypes[20]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AccountGroup.ProtoReflect.Descriptor instead.
func (*AccountGroup) Descriptor() ([]byte, []int) {
	return file_atlas_v1_api_proto_rawDescGZIP(), []int{20}
}

func (x *AccountGroup) GetAccountGroupId() string {
	if x != nil {
		return x.AccountGroupId
	}
	return ""
}

func (x *AccountGroup) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *AccountGroup) GetAddress() string {
	if x != nil {
		return x.Address
	}
	return ""
}

func (x *AccountGroup) GetActivatedProductIds() []string {
	if x != nil {
		return x.ActivatedProductIds
	}
	return nil
}

func (x *AccountGroup) GetUrl() string {
	if x != nil {
		return x.Url
	}
	return ""
}

// A location is either an account group or a brand
type Location struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Types that are assignable to Location:
	//	*Location_AccountGroup
	//	*Location_Brand
	Location isLocation_Location `protobuf_oneof:"location"`
}

func (x *Location) Reset() {
	*x = Location{}
	if protoimpl.UnsafeEnabled {
		mi := &file_atlas_v1_api_proto_msgTypes[21]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Location) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Location) ProtoMessage() {}

func (x *Location) ProtoReflect() protoreflect.Message {
	mi := &file_atlas_v1_api_proto_msgTypes[21]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Location.ProtoReflect.Descriptor instead.
func (*Location) Descriptor() ([]byte, []int) {
	return file_atlas_v1_api_proto_rawDescGZIP(), []int{21}
}

func (m *Location) GetLocation() isLocation_Location {
	if m != nil {
		return m.Location
	}
	return nil
}

func (x *Location) GetAccountGroup() *AccountGroup {
	if x, ok := x.GetLocation().(*Location_AccountGroup); ok {
		return x.AccountGroup
	}
	return nil
}

func (x *Location) GetBrand() *Brand {
	if x, ok := x.GetLocation().(*Location_Brand); ok {
		return x.Brand
	}
	return nil
}

type isLocation_Location interface {
	isLocation_Location()
}

type Location_AccountGroup struct {
	AccountGroup *AccountGroup `protobuf:"bytes,1,opt,name=account_group,json=accountGroup,proto3,oneof"`
}

type Location_Brand struct {
	Brand *Brand `protobuf:"bytes,2,opt,name=brand,proto3,oneof"`
}

func (*Location_AccountGroup) isLocation_Location() {}

func (*Location_Brand) isLocation_Location() {}

// The list of associated ids
type AssociatedLocationIDs struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AccountGroupIds []string `protobuf:"bytes,1,rep,name=account_group_ids,json=accountGroupIds,proto3" json:"account_group_ids,omitempty"`
	GroupPaths      []string `protobuf:"bytes,2,rep,name=group_paths,json=groupPaths,proto3" json:"group_paths,omitempty"`
}

func (x *AssociatedLocationIDs) Reset() {
	*x = AssociatedLocationIDs{}
	if protoimpl.UnsafeEnabled {
		mi := &file_atlas_v1_api_proto_msgTypes[22]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AssociatedLocationIDs) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AssociatedLocationIDs) ProtoMessage() {}

func (x *AssociatedLocationIDs) ProtoReflect() protoreflect.Message {
	mi := &file_atlas_v1_api_proto_msgTypes[22]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AssociatedLocationIDs.ProtoReflect.Descriptor instead.
func (*AssociatedLocationIDs) Descriptor() ([]byte, []int) {
	return file_atlas_v1_api_proto_rawDescGZIP(), []int{22}
}

func (x *AssociatedLocationIDs) GetAccountGroupIds() []string {
	if x != nil {
		return x.AccountGroupIds
	}
	return nil
}

func (x *AssociatedLocationIDs) GetGroupPaths() []string {
	if x != nil {
		return x.GroupPaths
	}
	return nil
}

// Theming values set for a partner and market
type Theming struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// primary color and variations
	PrimaryColor       string `protobuf:"bytes,1,opt,name=primary_color,json=primaryColor,proto3" json:"primary_color,omitempty"`
	PrimaryHoverColor  string `protobuf:"bytes,2,opt,name=primary_hover_color,json=primaryHoverColor,proto3" json:"primary_hover_color,omitempty"`
	PrimaryActiveColor string `protobuf:"bytes,3,opt,name=primary_active_color,json=primaryActiveColor,proto3" json:"primary_active_color,omitempty"`
	// secondary color and variations
	SecondaryColor       string `protobuf:"bytes,4,opt,name=secondary_color,json=secondaryColor,proto3" json:"secondary_color,omitempty"`
	SecondaryHoverColor  string `protobuf:"bytes,5,opt,name=secondary_hover_color,json=secondaryHoverColor,proto3" json:"secondary_hover_color,omitempty"`
	SecondaryActiveColor string `protobuf:"bytes,6,opt,name=secondary_active_color,json=secondaryActiveColor,proto3" json:"secondary_active_color,omitempty"`
	// font color
	FontColor         string `protobuf:"bytes,7,opt,name=font_color,json=fontColor,proto3" json:"font_color,omitempty"`
	FontDisabledColor string `protobuf:"bytes,8,opt,name=font_disabled_color,json=fontDisabledColor,proto3" json:"font_disabled_color,omitempty"`
	// accents color and variation, accents are things like the pin icons in the navigation
	AccentsColor       string `protobuf:"bytes,9,opt,name=accents_color,json=accentsColor,proto3" json:"accents_color,omitempty"`
	AccentsActiveColor string `protobuf:"bytes,10,opt,name=accents_active_color,json=accentsActiveColor,proto3" json:"accents_active_color,omitempty"`
	// focus color is the target of something that has focus in the current context, i.e. the link to the page you're
	// currently on has this color as its background in the side navigation
	FocusColor string `protobuf:"bytes,11,opt,name=focus_color,json=focusColor,proto3" json:"focus_color,omitempty"`
	// the color of borders
	BorderColor string `protobuf:"bytes,12,opt,name=border_color,json=borderColor,proto3" json:"border_color,omitempty"`
}

func (x *Theming) Reset() {
	*x = Theming{}
	if protoimpl.UnsafeEnabled {
		mi := &file_atlas_v1_api_proto_msgTypes[23]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Theming) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Theming) ProtoMessage() {}

func (x *Theming) ProtoReflect() protoreflect.Message {
	mi := &file_atlas_v1_api_proto_msgTypes[23]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Theming.ProtoReflect.Descriptor instead.
func (*Theming) Descriptor() ([]byte, []int) {
	return file_atlas_v1_api_proto_rawDescGZIP(), []int{23}
}

func (x *Theming) GetPrimaryColor() string {
	if x != nil {
		return x.PrimaryColor
	}
	return ""
}

func (x *Theming) GetPrimaryHoverColor() string {
	if x != nil {
		return x.PrimaryHoverColor
	}
	return ""
}

func (x *Theming) GetPrimaryActiveColor() string {
	if x != nil {
		return x.PrimaryActiveColor
	}
	return ""
}

func (x *Theming) GetSecondaryColor() string {
	if x != nil {
		return x.SecondaryColor
	}
	return ""
}

func (x *Theming) GetSecondaryHoverColor() string {
	if x != nil {
		return x.SecondaryHoverColor
	}
	return ""
}

func (x *Theming) GetSecondaryActiveColor() string {
	if x != nil {
		return x.SecondaryActiveColor
	}
	return ""
}

func (x *Theming) GetFontColor() string {
	if x != nil {
		return x.FontColor
	}
	return ""
}

func (x *Theming) GetFontDisabledColor() string {
	if x != nil {
		return x.FontDisabledColor
	}
	return ""
}

func (x *Theming) GetAccentsColor() string {
	if x != nil {
		return x.AccentsColor
	}
	return ""
}

func (x *Theming) GetAccentsActiveColor() string {
	if x != nil {
		return x.AccentsActiveColor
	}
	return ""
}

func (x *Theming) GetFocusColor() string {
	if x != nil {
		return x.FocusColor
	}
	return ""
}

func (x *Theming) GetBorderColor() string {
	if x != nil {
		return x.BorderColor
	}
	return ""
}

type RetentionConfig struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The email address to email if the business would like to cancel
	CancellationNotificationEmail string `protobuf:"bytes,1,opt,name=cancellation_notification_email,json=cancellationNotificationEmail,proto3" json:"cancellation_notification_email,omitempty"`
}

func (x *RetentionConfig) Reset() {
	*x = RetentionConfig{}
	if protoimpl.UnsafeEnabled {
		mi := &file_atlas_v1_api_proto_msgTypes[24]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RetentionConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RetentionConfig) ProtoMessage() {}

func (x *RetentionConfig) ProtoReflect() protoreflect.Message {
	mi := &file_atlas_v1_api_proto_msgTypes[24]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RetentionConfig.ProtoReflect.Descriptor instead.
func (*RetentionConfig) Descriptor() ([]byte, []int) {
	return file_atlas_v1_api_proto_rawDescGZIP(), []int{24}
}

func (x *RetentionConfig) GetCancellationNotificationEmail() string {
	if x != nil {
		return x.CancellationNotificationEmail
	}
	return ""
}

type TotalLocations struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// the total number of accounts available to the user
	Accounts int64 `protobuf:"varint,1,opt,name=accounts,proto3" json:"accounts,omitempty"`
	// the total number of brands available to the user
	Brands int64 `protobuf:"varint,2,opt,name=brands,proto3" json:"brands,omitempty"`
}

func (x *TotalLocations) Reset() {
	*x = TotalLocations{}
	if protoimpl.UnsafeEnabled {
		mi := &file_atlas_v1_api_proto_msgTypes[25]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TotalLocations) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TotalLocations) ProtoMessage() {}

func (x *TotalLocations) ProtoReflect() protoreflect.Message {
	mi := &file_atlas_v1_api_proto_msgTypes[25]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TotalLocations.ProtoReflect.Descriptor instead.
func (*TotalLocations) Descriptor() ([]byte, []int) {
	return file_atlas_v1_api_proto_rawDescGZIP(), []int{25}
}

func (x *TotalLocations) GetAccounts() int64 {
	if x != nil {
		return x.Accounts
	}
	return 0
}

func (x *TotalLocations) GetBrands() int64 {
	if x != nil {
		return x.Brands
	}
	return 0
}

// Response for Atlas.GetNavigationData
type GetNavigationDataResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The partner's whitelabel branding data
	Branding *Branding `protobuf:"bytes,2,opt,name=branding,proto3" json:"branding,omitempty"`
	// The sales information
	SalesInfo *SalesInfo `protobuf:"bytes,3,opt,name=sales_info,json=salesInfo,proto3" json:"sales_info,omitempty"`
	// The list of pinned products
	PinnedItems []*PinnedItem `protobuf:"bytes,4,rep,name=pinned_items,json=pinnedItems,proto3" json:"pinned_items,omitempty"`
	// The list of associated account groups
	AssociatedLocationIds *AssociatedLocationIDs `protobuf:"bytes,5,opt,name=associated_location_ids,json=associatedLocationIds,proto3" json:"associated_location_ids,omitempty"`
	// The default location (account_group_id)
	DefaultLocation string `protobuf:"bytes,6,opt,name=default_location,json=defaultLocation,proto3" json:"default_location,omitempty"`
	// The user's currently selected language
	Language string `protobuf:"bytes,8,opt,name=language,proto3" json:"language,omitempty"`
	// Dropdown buttons to place in atlas' dropdown menu
	DropdownItems []*DropdownItem `protobuf:"bytes,9,rep,name=dropdown_items,json=dropdownItems,proto3" json:"dropdown_items,omitempty"`
	// The currently requested brand's name. Used for navigation purposes
	CurrentBrandName string `protobuf:"bytes,10,opt,name=current_brand_name,json=currentBrandName,proto3" json:"current_brand_name,omitempty"`
	// The type of view the user has
	UserView UserViewType `protobuf:"varint,11,opt,name=user_view,json=userView,proto3,enum=atlas.v1.UserViewType" json:"user_view,omitempty"`
	// The configuration for how the business can contact the partner's retention team
	RetentionConfig *RetentionConfig `protobuf:"bytes,12,opt,name=retention_config,json=retentionConfig,proto3" json:"retention_config,omitempty"`
	// The total number of Accounts and Brands the user has
	TotalLocations *TotalLocations `protobuf:"bytes,13,opt,name=total_locations,json=totalLocations,proto3" json:"total_locations,omitempty"`
	// The user's identifier
	UserId string `protobuf:"bytes,14,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	// Sets partner branding to the style of the "Business App" initiative
	BusinessAppBranding bool `protobuf:"varint,15,opt,name=business_app_branding,json=businessAppBranding,proto3" json:"business_app_branding,omitempty"`
	// Determines whether or not the business side navigation should be visible.
	// Partners who don't use Business Nav don't want their users going to business nav from
	// the products they do serve.
	DisableBusinessNav bool `protobuf:"varint,16,opt,name=disable_business_nav,json=disableBusinessNav,proto3" json:"disable_business_nav,omitempty"`
	// the nav items to display to the user
	NavigationItems []*SideNavigationItem `protobuf:"bytes,17,rep,name=navigation_items,json=navigationItems,proto3" json:"navigation_items,omitempty"`
	// Determines whether or not the product switcher should be visible.
	DisableProductSwitcher bool `protobuf:"varint,18,opt,name=disable_product_switcher,json=disableProductSwitcher,proto3" json:"disable_product_switcher,omitempty"`
}

func (x *GetNavigationDataResponse) Reset() {
	*x = GetNavigationDataResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_atlas_v1_api_proto_msgTypes[26]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetNavigationDataResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetNavigationDataResponse) ProtoMessage() {}

func (x *GetNavigationDataResponse) ProtoReflect() protoreflect.Message {
	mi := &file_atlas_v1_api_proto_msgTypes[26]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetNavigationDataResponse.ProtoReflect.Descriptor instead.
func (*GetNavigationDataResponse) Descriptor() ([]byte, []int) {
	return file_atlas_v1_api_proto_rawDescGZIP(), []int{26}
}

func (x *GetNavigationDataResponse) GetBranding() *Branding {
	if x != nil {
		return x.Branding
	}
	return nil
}

func (x *GetNavigationDataResponse) GetSalesInfo() *SalesInfo {
	if x != nil {
		return x.SalesInfo
	}
	return nil
}

func (x *GetNavigationDataResponse) GetPinnedItems() []*PinnedItem {
	if x != nil {
		return x.PinnedItems
	}
	return nil
}

func (x *GetNavigationDataResponse) GetAssociatedLocationIds() *AssociatedLocationIDs {
	if x != nil {
		return x.AssociatedLocationIds
	}
	return nil
}

func (x *GetNavigationDataResponse) GetDefaultLocation() string {
	if x != nil {
		return x.DefaultLocation
	}
	return ""
}

func (x *GetNavigationDataResponse) GetLanguage() string {
	if x != nil {
		return x.Language
	}
	return ""
}

func (x *GetNavigationDataResponse) GetDropdownItems() []*DropdownItem {
	if x != nil {
		return x.DropdownItems
	}
	return nil
}

func (x *GetNavigationDataResponse) GetCurrentBrandName() string {
	if x != nil {
		return x.CurrentBrandName
	}
	return ""
}

func (x *GetNavigationDataResponse) GetUserView() UserViewType {
	if x != nil {
		return x.UserView
	}
	return UserViewType_USER_VIEW_TYPE_SMB
}

func (x *GetNavigationDataResponse) GetRetentionConfig() *RetentionConfig {
	if x != nil {
		return x.RetentionConfig
	}
	return nil
}

func (x *GetNavigationDataResponse) GetTotalLocations() *TotalLocations {
	if x != nil {
		return x.TotalLocations
	}
	return nil
}

func (x *GetNavigationDataResponse) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

func (x *GetNavigationDataResponse) GetBusinessAppBranding() bool {
	if x != nil {
		return x.BusinessAppBranding
	}
	return false
}

func (x *GetNavigationDataResponse) GetDisableBusinessNav() bool {
	if x != nil {
		return x.DisableBusinessNav
	}
	return false
}

func (x *GetNavigationDataResponse) GetNavigationItems() []*SideNavigationItem {
	if x != nil {
		return x.NavigationItems
	}
	return nil
}

func (x *GetNavigationDataResponse) GetDisableProductSwitcher() bool {
	if x != nil {
		return x.DisableProductSwitcher
	}
	return false
}

// Request for PinsService.SetPins
type SetPinsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Identifies a pin set, this could be a brand id or a business id
	Identifier string `protobuf:"bytes,1,opt,name=identifier,proto3" json:"identifier,omitempty"`
	// An ordered list of the updated state of pinned navigation items
	Items []*PinnedItem `protobuf:"bytes,3,rep,name=items,proto3" json:"items,omitempty"`
}

func (x *SetPinsRequest) Reset() {
	*x = SetPinsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_atlas_v1_api_proto_msgTypes[27]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SetPinsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SetPinsRequest) ProtoMessage() {}

func (x *SetPinsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_atlas_v1_api_proto_msgTypes[27]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SetPinsRequest.ProtoReflect.Descriptor instead.
func (*SetPinsRequest) Descriptor() ([]byte, []int) {
	return file_atlas_v1_api_proto_rawDescGZIP(), []int{27}
}

func (x *SetPinsRequest) GetIdentifier() string {
	if x != nil {
		return x.Identifier
	}
	return ""
}

func (x *SetPinsRequest) GetItems() []*PinnedItem {
	if x != nil {
		return x.Items
	}
	return nil
}

// Request for PinsService.GetPins
type GetPinsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Identifies a pin set, this could be a brand id or a business id
	Identifier string `protobuf:"bytes,1,opt,name=identifier,proto3" json:"identifier,omitempty"`
}

func (x *GetPinsRequest) Reset() {
	*x = GetPinsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_atlas_v1_api_proto_msgTypes[28]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetPinsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetPinsRequest) ProtoMessage() {}

func (x *GetPinsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_atlas_v1_api_proto_msgTypes[28]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetPinsRequest.ProtoReflect.Descriptor instead.
func (*GetPinsRequest) Descriptor() ([]byte, []int) {
	return file_atlas_v1_api_proto_rawDescGZIP(), []int{28}
}

func (x *GetPinsRequest) GetIdentifier() string {
	if x != nil {
		return x.Identifier
	}
	return ""
}

// Response for PinsService.SetPins
type GetPinsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// An ordered list of the pinned navigation items
	Items []*PinnedItem `protobuf:"bytes,1,rep,name=items,proto3" json:"items,omitempty"`
}

func (x *GetPinsResponse) Reset() {
	*x = GetPinsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_atlas_v1_api_proto_msgTypes[29]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetPinsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetPinsResponse) ProtoMessage() {}

func (x *GetPinsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_atlas_v1_api_proto_msgTypes[29]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetPinsResponse.ProtoReflect.Descriptor instead.
func (*GetPinsResponse) Descriptor() ([]byte, []int) {
	return file_atlas_v1_api_proto_rawDescGZIP(), []int{29}
}

func (x *GetPinsResponse) GetItems() []*PinnedItem {
	if x != nil {
		return x.Items
	}
	return nil
}

// Request for Atlas.GetLocations
type GetLocationsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Types that are assignable to Identifier:
	//	*GetLocationsRequest_AccountGroups_
	//	*GetLocationsRequest_Groups_
	Identifier isGetLocationsRequest_Identifier `protobuf_oneof:"identifier"`
}

func (x *GetLocationsRequest) Reset() {
	*x = GetLocationsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_atlas_v1_api_proto_msgTypes[30]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetLocationsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetLocationsRequest) ProtoMessage() {}

func (x *GetLocationsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_atlas_v1_api_proto_msgTypes[30]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetLocationsRequest.ProtoReflect.Descriptor instead.
func (*GetLocationsRequest) Descriptor() ([]byte, []int) {
	return file_atlas_v1_api_proto_rawDescGZIP(), []int{30}
}

func (m *GetLocationsRequest) GetIdentifier() isGetLocationsRequest_Identifier {
	if m != nil {
		return m.Identifier
	}
	return nil
}

func (x *GetLocationsRequest) GetAccountGroups() *GetLocationsRequest_AccountGroups {
	if x, ok := x.GetIdentifier().(*GetLocationsRequest_AccountGroups_); ok {
		return x.AccountGroups
	}
	return nil
}

func (x *GetLocationsRequest) GetGroups() *GetLocationsRequest_Groups {
	if x, ok := x.GetIdentifier().(*GetLocationsRequest_Groups_); ok {
		return x.Groups
	}
	return nil
}

type isGetLocationsRequest_Identifier interface {
	isGetLocationsRequest_Identifier()
}

type GetLocationsRequest_AccountGroups_ struct {
	AccountGroups *GetLocationsRequest_AccountGroups `protobuf:"bytes,1,opt,name=account_groups,json=accountGroups,proto3,oneof"`
}

type GetLocationsRequest_Groups_ struct {
	Groups *GetLocationsRequest_Groups `protobuf:"bytes,2,opt,name=groups,proto3,oneof"`
}

func (*GetLocationsRequest_AccountGroups_) isGetLocationsRequest_Identifier() {}

func (*GetLocationsRequest_Groups_) isGetLocationsRequest_Identifier() {}

// Response for Atlas.GetLocations
type GetLocationsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Locations []*Location `protobuf:"bytes,1,rep,name=locations,proto3" json:"locations,omitempty"`
}

func (x *GetLocationsResponse) Reset() {
	*x = GetLocationsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_atlas_v1_api_proto_msgTypes[31]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetLocationsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetLocationsResponse) ProtoMessage() {}

func (x *GetLocationsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_atlas_v1_api_proto_msgTypes[31]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetLocationsResponse.ProtoReflect.Descriptor instead.
func (*GetLocationsResponse) Descriptor() ([]byte, []int) {
	return file_atlas_v1_api_proto_rawDescGZIP(), []int{31}
}

func (x *GetLocationsResponse) GetLocations() []*Location {
	if x != nil {
		return x.Locations
	}
	return nil
}

// Response for Atlas.ListElevatedLocations
type ListElevatedLocationsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PartnerId string `protobuf:"bytes,1,opt,name=partner_id,json=partnerId,proto3" json:"partner_id,omitempty"`
	Cursor    string `protobuf:"bytes,2,opt,name=cursor,proto3" json:"cursor,omitempty"`
	PageSize  int64  `protobuf:"varint,3,opt,name=page_size,json=pageSize,proto3" json:"page_size,omitempty"`
	Search    string `protobuf:"bytes,4,opt,name=search,proto3" json:"search,omitempty"`
	// Types that are assignable to Type:
	//	*ListElevatedLocationsRequest_AccountGroups
	//	*ListElevatedLocationsRequest_Brands
	Type isListElevatedLocationsRequest_Type `protobuf_oneof:"type"`
}

func (x *ListElevatedLocationsRequest) Reset() {
	*x = ListElevatedLocationsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_atlas_v1_api_proto_msgTypes[32]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListElevatedLocationsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListElevatedLocationsRequest) ProtoMessage() {}

func (x *ListElevatedLocationsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_atlas_v1_api_proto_msgTypes[32]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListElevatedLocationsRequest.ProtoReflect.Descriptor instead.
func (*ListElevatedLocationsRequest) Descriptor() ([]byte, []int) {
	return file_atlas_v1_api_proto_rawDescGZIP(), []int{32}
}

func (x *ListElevatedLocationsRequest) GetPartnerId() string {
	if x != nil {
		return x.PartnerId
	}
	return ""
}

func (x *ListElevatedLocationsRequest) GetCursor() string {
	if x != nil {
		return x.Cursor
	}
	return ""
}

func (x *ListElevatedLocationsRequest) GetPageSize() int64 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

func (x *ListElevatedLocationsRequest) GetSearch() string {
	if x != nil {
		return x.Search
	}
	return ""
}

func (m *ListElevatedLocationsRequest) GetType() isListElevatedLocationsRequest_Type {
	if m != nil {
		return m.Type
	}
	return nil
}

func (x *ListElevatedLocationsRequest) GetAccountGroups() bool {
	if x, ok := x.GetType().(*ListElevatedLocationsRequest_AccountGroups); ok {
		return x.AccountGroups
	}
	return false
}

func (x *ListElevatedLocationsRequest) GetBrands() bool {
	if x, ok := x.GetType().(*ListElevatedLocationsRequest_Brands); ok {
		return x.Brands
	}
	return false
}

type isListElevatedLocationsRequest_Type interface {
	isListElevatedLocationsRequest_Type()
}

type ListElevatedLocationsRequest_AccountGroups struct {
	AccountGroups bool `protobuf:"varint,5,opt,name=account_groups,json=accountGroups,proto3,oneof"`
}

type ListElevatedLocationsRequest_Brands struct {
	Brands bool `protobuf:"varint,6,opt,name=brands,proto3,oneof"`
}

func (*ListElevatedLocationsRequest_AccountGroups) isListElevatedLocationsRequest_Type() {}

func (*ListElevatedLocationsRequest_Brands) isListElevatedLocationsRequest_Type() {}

// Response for Atlas.ListElevatedLocations
type ListElevatedLocationsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Locations []*Location `protobuf:"bytes,1,rep,name=locations,proto3" json:"locations,omitempty"`
	Cursor    string      `protobuf:"bytes,2,opt,name=cursor,proto3" json:"cursor,omitempty"`
	HasMore   bool        `protobuf:"varint,3,opt,name=has_more,json=hasMore,proto3" json:"has_more,omitempty"`
}

func (x *ListElevatedLocationsResponse) Reset() {
	*x = ListElevatedLocationsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_atlas_v1_api_proto_msgTypes[33]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListElevatedLocationsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListElevatedLocationsResponse) ProtoMessage() {}

func (x *ListElevatedLocationsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_atlas_v1_api_proto_msgTypes[33]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListElevatedLocationsResponse.ProtoReflect.Descriptor instead.
func (*ListElevatedLocationsResponse) Descriptor() ([]byte, []int) {
	return file_atlas_v1_api_proto_rawDescGZIP(), []int{33}
}

func (x *ListElevatedLocationsResponse) GetLocations() []*Location {
	if x != nil {
		return x.Locations
	}
	return nil
}

func (x *ListElevatedLocationsResponse) GetCursor() string {
	if x != nil {
		return x.Cursor
	}
	return ""
}

func (x *ListElevatedLocationsResponse) GetHasMore() bool {
	if x != nil {
		return x.HasMore
	}
	return false
}

// Response for Atlas.ListLocations
type ListLocationsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PartnerId            string `protobuf:"bytes,2,opt,name=partner_id,json=partnerId,proto3" json:"partner_id,omitempty"`
	Cursor               string `protobuf:"bytes,3,opt,name=cursor,proto3" json:"cursor,omitempty"`
	PageSize             int64  `protobuf:"varint,4,opt,name=page_size,json=pageSize,proto3" json:"page_size,omitempty"`
	Search               string `protobuf:"bytes,5,opt,name=search,proto3" json:"search,omitempty"`
	IncludeAccountGroups bool   `protobuf:"varint,6,opt,name=include_account_groups,json=includeAccountGroups,proto3" json:"include_account_groups,omitempty"`
	IncludeBrands        bool   `protobuf:"varint,7,opt,name=include_brands,json=includeBrands,proto3" json:"include_brands,omitempty"`
}

func (x *ListLocationsRequest) Reset() {
	*x = ListLocationsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_atlas_v1_api_proto_msgTypes[34]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListLocationsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListLocationsRequest) ProtoMessage() {}

func (x *ListLocationsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_atlas_v1_api_proto_msgTypes[34]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListLocationsRequest.ProtoReflect.Descriptor instead.
func (*ListLocationsRequest) Descriptor() ([]byte, []int) {
	return file_atlas_v1_api_proto_rawDescGZIP(), []int{34}
}

func (x *ListLocationsRequest) GetPartnerId() string {
	if x != nil {
		return x.PartnerId
	}
	return ""
}

func (x *ListLocationsRequest) GetCursor() string {
	if x != nil {
		return x.Cursor
	}
	return ""
}

func (x *ListLocationsRequest) GetPageSize() int64 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

func (x *ListLocationsRequest) GetSearch() string {
	if x != nil {
		return x.Search
	}
	return ""
}

func (x *ListLocationsRequest) GetIncludeAccountGroups() bool {
	if x != nil {
		return x.IncludeAccountGroups
	}
	return false
}

func (x *ListLocationsRequest) GetIncludeBrands() bool {
	if x != nil {
		return x.IncludeBrands
	}
	return false
}

// Response for Atlas.ListLocations
type ListLocationsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Locations []*Location `protobuf:"bytes,1,rep,name=locations,proto3" json:"locations,omitempty"`
	Cursor    string      `protobuf:"bytes,2,opt,name=cursor,proto3" json:"cursor,omitempty"`
	HasMore   bool        `protobuf:"varint,3,opt,name=has_more,json=hasMore,proto3" json:"has_more,omitempty"`
}

func (x *ListLocationsResponse) Reset() {
	*x = ListLocationsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_atlas_v1_api_proto_msgTypes[35]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListLocationsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListLocationsResponse) ProtoMessage() {}

func (x *ListLocationsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_atlas_v1_api_proto_msgTypes[35]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListLocationsResponse.ProtoReflect.Descriptor instead.
func (*ListLocationsResponse) Descriptor() ([]byte, []int) {
	return file_atlas_v1_api_proto_rawDescGZIP(), []int{35}
}

func (x *ListLocationsResponse) GetLocations() []*Location {
	if x != nil {
		return x.Locations
	}
	return nil
}

func (x *ListLocationsResponse) GetCursor() string {
	if x != nil {
		return x.Cursor
	}
	return ""
}

func (x *ListLocationsResponse) GetHasMore() bool {
	if x != nil {
		return x.HasMore
	}
	return false
}

// SetLanguageRequest remember the specified language string
type SetLanguageRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Language string `protobuf:"bytes,1,opt,name=language,proto3" json:"language,omitempty"`
}

func (x *SetLanguageRequest) Reset() {
	*x = SetLanguageRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_atlas_v1_api_proto_msgTypes[36]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SetLanguageRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SetLanguageRequest) ProtoMessage() {}

func (x *SetLanguageRequest) ProtoReflect() protoreflect.Message {
	mi := &file_atlas_v1_api_proto_msgTypes[36]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SetLanguageRequest.ProtoReflect.Descriptor instead.
func (*SetLanguageRequest) Descriptor() ([]byte, []int) {
	return file_atlas_v1_api_proto_rawDescGZIP(), []int{36}
}

func (x *SetLanguageRequest) GetLanguage() string {
	if x != nil {
		return x.Language
	}
	return ""
}

// GetLanguageRequest get the remembered language for the caller
type GetLanguageRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *GetLanguageRequest) Reset() {
	*x = GetLanguageRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_atlas_v1_api_proto_msgTypes[37]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetLanguageRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetLanguageRequest) ProtoMessage() {}

func (x *GetLanguageRequest) ProtoReflect() protoreflect.Message {
	mi := &file_atlas_v1_api_proto_msgTypes[37]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetLanguageRequest.ProtoReflect.Descriptor instead.
func (*GetLanguageRequest) Descriptor() ([]byte, []int) {
	return file_atlas_v1_api_proto_rawDescGZIP(), []int{37}
}

// GetLanguageResponse returns the remembered language
type GetLanguageResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// the remembered language
	Language string `protobuf:"bytes,1,opt,name=language,proto3" json:"language,omitempty"`
}

func (x *GetLanguageResponse) Reset() {
	*x = GetLanguageResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_atlas_v1_api_proto_msgTypes[38]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetLanguageResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetLanguageResponse) ProtoMessage() {}

func (x *GetLanguageResponse) ProtoReflect() protoreflect.Message {
	mi := &file_atlas_v1_api_proto_msgTypes[38]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetLanguageResponse.ProtoReflect.Descriptor instead.
func (*GetLanguageResponse) Descriptor() ([]byte, []int) {
	return file_atlas_v1_api_proto_rawDescGZIP(), []int{38}
}

func (x *GetLanguageResponse) GetLanguage() string {
	if x != nil {
		return x.Language
	}
	return ""
}

// ContactUsRequest
type ContactUsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The ID of the account group
	AccountGroupId string `protobuf:"bytes,1,opt,name=account_group_id,json=accountGroupId,proto3" json:"account_group_id,omitempty"`
	// Message sent from the contacter
	Message string `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
}

func (x *ContactUsRequest) Reset() {
	*x = ContactUsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_atlas_v1_api_proto_msgTypes[39]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ContactUsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ContactUsRequest) ProtoMessage() {}

func (x *ContactUsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_atlas_v1_api_proto_msgTypes[39]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ContactUsRequest.ProtoReflect.Descriptor instead.
func (*ContactUsRequest) Descriptor() ([]byte, []int) {
	return file_atlas_v1_api_proto_rawDescGZIP(), []int{39}
}

func (x *ContactUsRequest) GetAccountGroupId() string {
	if x != nil {
		return x.AccountGroupId
	}
	return ""
}

func (x *ContactUsRequest) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

// SetDefaultLocationRequest
type SetDefaultLocationRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The partner ID that the default location is applicable to.
	PartnerId string `protobuf:"bytes,2,opt,name=partner_id,json=partnerId,proto3" json:"partner_id,omitempty"`
	// The default location - one of either a single or multi location.
	//
	// Types that are assignable to Location:
	//	*SetDefaultLocationRequest_AccountGroupId
	//	*SetDefaultLocationRequest_GroupId
	Location isSetDefaultLocationRequest_Location `protobuf_oneof:"location"`
}

func (x *SetDefaultLocationRequest) Reset() {
	*x = SetDefaultLocationRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_atlas_v1_api_proto_msgTypes[40]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SetDefaultLocationRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SetDefaultLocationRequest) ProtoMessage() {}

func (x *SetDefaultLocationRequest) ProtoReflect() protoreflect.Message {
	mi := &file_atlas_v1_api_proto_msgTypes[40]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SetDefaultLocationRequest.ProtoReflect.Descriptor instead.
func (*SetDefaultLocationRequest) Descriptor() ([]byte, []int) {
	return file_atlas_v1_api_proto_rawDescGZIP(), []int{40}
}

func (x *SetDefaultLocationRequest) GetPartnerId() string {
	if x != nil {
		return x.PartnerId
	}
	return ""
}

func (m *SetDefaultLocationRequest) GetLocation() isSetDefaultLocationRequest_Location {
	if m != nil {
		return m.Location
	}
	return nil
}

func (x *SetDefaultLocationRequest) GetAccountGroupId() string {
	if x, ok := x.GetLocation().(*SetDefaultLocationRequest_AccountGroupId); ok {
		return x.AccountGroupId
	}
	return ""
}

func (x *SetDefaultLocationRequest) GetGroupId() string {
	if x, ok := x.GetLocation().(*SetDefaultLocationRequest_GroupId); ok {
		return x.GroupId
	}
	return ""
}

type isSetDefaultLocationRequest_Location interface {
	isSetDefaultLocationRequest_Location()
}

type SetDefaultLocationRequest_AccountGroupId struct {
	AccountGroupId string `protobuf:"bytes,1,opt,name=account_group_id,json=accountGroupId,proto3,oneof"`
}

type SetDefaultLocationRequest_GroupId struct {
	GroupId string `protobuf:"bytes,3,opt,name=group_id,json=groupId,proto3,oneof"`
}

func (*SetDefaultLocationRequest_AccountGroupId) isSetDefaultLocationRequest_Location() {}

func (*SetDefaultLocationRequest_GroupId) isSetDefaultLocationRequest_Location() {}

// GetDefaultLocationRequest
type GetDefaultLocationRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The partner ID that the default location is applicable to.
	PartnerId string `protobuf:"bytes,1,opt,name=partner_id,json=partnerId,proto3" json:"partner_id,omitempty"`
}

func (x *GetDefaultLocationRequest) Reset() {
	*x = GetDefaultLocationRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_atlas_v1_api_proto_msgTypes[41]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetDefaultLocationRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetDefaultLocationRequest) ProtoMessage() {}

func (x *GetDefaultLocationRequest) ProtoReflect() protoreflect.Message {
	mi := &file_atlas_v1_api_proto_msgTypes[41]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetDefaultLocationRequest.ProtoReflect.Descriptor instead.
func (*GetDefaultLocationRequest) Descriptor() ([]byte, []int) {
	return file_atlas_v1_api_proto_rawDescGZIP(), []int{41}
}

func (x *GetDefaultLocationRequest) GetPartnerId() string {
	if x != nil {
		return x.PartnerId
	}
	return ""
}

// GetDefaultLocationResponse
type GetDefaultLocationResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The default location - one of either a single or multi location.
	//
	// Types that are assignable to Location:
	//	*GetDefaultLocationResponse_AccountGroupId
	//	*GetDefaultLocationResponse_GroupId
	Location isGetDefaultLocationResponse_Location `protobuf_oneof:"location"`
}

func (x *GetDefaultLocationResponse) Reset() {
	*x = GetDefaultLocationResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_atlas_v1_api_proto_msgTypes[42]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetDefaultLocationResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetDefaultLocationResponse) ProtoMessage() {}

func (x *GetDefaultLocationResponse) ProtoReflect() protoreflect.Message {
	mi := &file_atlas_v1_api_proto_msgTypes[42]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetDefaultLocationResponse.ProtoReflect.Descriptor instead.
func (*GetDefaultLocationResponse) Descriptor() ([]byte, []int) {
	return file_atlas_v1_api_proto_rawDescGZIP(), []int{42}
}

func (m *GetDefaultLocationResponse) GetLocation() isGetDefaultLocationResponse_Location {
	if m != nil {
		return m.Location
	}
	return nil
}

func (x *GetDefaultLocationResponse) GetAccountGroupId() string {
	if x, ok := x.GetLocation().(*GetDefaultLocationResponse_AccountGroupId); ok {
		return x.AccountGroupId
	}
	return ""
}

func (x *GetDefaultLocationResponse) GetGroupId() string {
	if x, ok := x.GetLocation().(*GetDefaultLocationResponse_GroupId); ok {
		return x.GroupId
	}
	return ""
}

type isGetDefaultLocationResponse_Location interface {
	isGetDefaultLocationResponse_Location()
}

type GetDefaultLocationResponse_AccountGroupId struct {
	AccountGroupId string `protobuf:"bytes,1,opt,name=account_group_id,json=accountGroupId,proto3,oneof"`
}

type GetDefaultLocationResponse_GroupId struct {
	GroupId string `protobuf:"bytes,2,opt,name=group_id,json=groupId,proto3,oneof"`
}

func (*GetDefaultLocationResponse_AccountGroupId) isGetDefaultLocationResponse_Location() {}

func (*GetDefaultLocationResponse_GroupId) isGetDefaultLocationResponse_Location() {}

type GetLocationsRequest_AccountGroups struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AccountGroupIds []string `protobuf:"bytes,1,rep,name=account_group_ids,json=accountGroupIds,proto3" json:"account_group_ids,omitempty"`
}

func (x *GetLocationsRequest_AccountGroups) Reset() {
	*x = GetLocationsRequest_AccountGroups{}
	if protoimpl.UnsafeEnabled {
		mi := &file_atlas_v1_api_proto_msgTypes[43]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetLocationsRequest_AccountGroups) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetLocationsRequest_AccountGroups) ProtoMessage() {}

func (x *GetLocationsRequest_AccountGroups) ProtoReflect() protoreflect.Message {
	mi := &file_atlas_v1_api_proto_msgTypes[43]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetLocationsRequest_AccountGroups.ProtoReflect.Descriptor instead.
func (*GetLocationsRequest_AccountGroups) Descriptor() ([]byte, []int) {
	return file_atlas_v1_api_proto_rawDescGZIP(), []int{30, 0}
}

func (x *GetLocationsRequest_AccountGroups) GetAccountGroupIds() []string {
	if x != nil {
		return x.AccountGroupIds
	}
	return nil
}

type GetLocationsRequest_Groups struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	GroupPaths []string `protobuf:"bytes,1,rep,name=group_paths,json=groupPaths,proto3" json:"group_paths,omitempty"`
}

func (x *GetLocationsRequest_Groups) Reset() {
	*x = GetLocationsRequest_Groups{}
	if protoimpl.UnsafeEnabled {
		mi := &file_atlas_v1_api_proto_msgTypes[44]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetLocationsRequest_Groups) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetLocationsRequest_Groups) ProtoMessage() {}

func (x *GetLocationsRequest_Groups) ProtoReflect() protoreflect.Message {
	mi := &file_atlas_v1_api_proto_msgTypes[44]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetLocationsRequest_Groups.ProtoReflect.Descriptor instead.
func (*GetLocationsRequest_Groups) Descriptor() ([]byte, []int) {
	return file_atlas_v1_api_proto_rawDescGZIP(), []int{30, 1}
}

func (x *GetLocationsRequest_Groups) GetGroupPaths() []string {
	if x != nil {
		return x.GroupPaths
	}
	return nil
}

var File_atlas_v1_api_proto protoreflect.FileDescriptor

var file_atlas_v1_api_proto_rawDesc = []byte{
	0x0a, 0x12, 0x61, 0x74, 0x6c, 0x61, 0x73, 0x2f, 0x76, 0x31, 0x2f, 0x61, 0x70, 0x69, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x12, 0x08, 0x61, 0x74, 0x6c, 0x61, 0x73, 0x2e, 0x76, 0x31, 0x1a, 0x1b,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f,
	0x65, 0x6d, 0x70, 0x74, 0x79, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x20, 0x76, 0x65, 0x6e,
	0x64, 0x61, 0x73, 0x74, 0x61, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x2f, 0x61, 0x6e, 0x6e, 0x6f,
	0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x55, 0x0a,
	0x12, 0x55, 0x73, 0x65, 0x72, 0x4e, 0x61, 0x76, 0x69, 0x67, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x49,
	0x74, 0x65, 0x6d, 0x12, 0x12, 0x0a, 0x04, 0x74, 0x65, 0x78, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x04, 0x74, 0x65, 0x78, 0x74, 0x12, 0x10, 0x0a, 0x03, 0x75, 0x72, 0x6c, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x75, 0x72, 0x6c, 0x12, 0x19, 0x0a, 0x08, 0x72, 0x6f, 0x75,
	0x74, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x72, 0x6f, 0x75,
	0x74, 0x65, 0x49, 0x64, 0x22, 0x64, 0x0a, 0x14, 0x43, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x4e, 0x61,
	0x76, 0x69, 0x67, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x74, 0x65, 0x6d, 0x12, 0x12, 0x0a, 0x04,
	0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65,
	0x12, 0x1b, 0x0a, 0x09, 0x65, 0x6e, 0x74, 0x72, 0x79, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x08, 0x65, 0x6e, 0x74, 0x72, 0x79, 0x55, 0x72, 0x6c, 0x12, 0x1b, 0x0a,
	0x09, 0x63, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x08, 0x63, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x49, 0x64, 0x22, 0xc0, 0x01, 0x0a, 0x0e, 0x47,
	0x65, 0x74, 0x44, 0x61, 0x74, 0x61, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1d, 0x0a,
	0x0a, 0x70, 0x61, 0x72, 0x74, 0x6e, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x09, 0x70, 0x61, 0x72, 0x74, 0x6e, 0x65, 0x72, 0x49, 0x64, 0x12, 0x1b, 0x0a, 0x09,
	0x6d, 0x61, 0x72, 0x6b, 0x65, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x08, 0x6d, 0x61, 0x72, 0x6b, 0x65, 0x74, 0x49, 0x64, 0x12, 0x28, 0x0a, 0x10, 0x61, 0x63, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x47, 0x72, 0x6f, 0x75,
	0x70, 0x49, 0x64, 0x12, 0x29, 0x0a, 0x11, 0x73, 0x69, 0x67, 0x6e, 0x5f, 0x6f, 0x75, 0x74, 0x5f,
	0x6e, 0x65, 0x78, 0x74, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e,
	0x73, 0x69, 0x67, 0x6e, 0x4f, 0x75, 0x74, 0x4e, 0x65, 0x78, 0x74, 0x55, 0x72, 0x6c, 0x12, 0x1d,
	0x0a, 0x0a, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x70, 0x61, 0x74, 0x68, 0x18, 0x05, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x09, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x50, 0x61, 0x74, 0x68, 0x22, 0x4d, 0x0a,
	0x0c, 0x4c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x44, 0x61, 0x74, 0x61, 0x12, 0x23, 0x0a,
	0x0d, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x4e, 0x61,
	0x6d, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x07, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x22, 0x8a, 0x01, 0x0a,
	0x10, 0x55, 0x73, 0x65, 0x72, 0x53, 0x77, 0x69, 0x74, 0x63, 0x68, 0x65, 0x72, 0x44, 0x61, 0x74,
	0x61, 0x12, 0x17, 0x0a, 0x07, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x70, 0x61,
	0x72, 0x74, 0x6e, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09,
	0x70, 0x61, 0x72, 0x74, 0x6e, 0x65, 0x72, 0x49, 0x64, 0x12, 0x21, 0x0a, 0x0c, 0x70, 0x61, 0x72,
	0x74, 0x6e, 0x65, 0x72, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0b, 0x70, 0x61, 0x72, 0x74, 0x6e, 0x65, 0x72, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1b, 0x0a, 0x09,
	0x65, 0x6e, 0x74, 0x72, 0x79, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x08, 0x65, 0x6e, 0x74, 0x72, 0x79, 0x55, 0x72, 0x6c, 0x22, 0xe1, 0x05, 0x0a, 0x0f, 0x47, 0x65,
	0x74, 0x44, 0x61, 0x74, 0x61, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x30, 0x0a,
	0x04, 0x75, 0x73, 0x65, 0x72, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x61, 0x74,
	0x6c, 0x61, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x4e, 0x61, 0x76, 0x69, 0x67,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x74, 0x65, 0x6d, 0x52, 0x04, 0x75, 0x73, 0x65, 0x72, 0x12,
	0x38, 0x0a, 0x07, 0x63, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x1e, 0x2e, 0x61, 0x74, 0x6c, 0x61, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x65, 0x6e, 0x74,
	0x65, 0x72, 0x4e, 0x61, 0x76, 0x69, 0x67, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x74, 0x65, 0x6d,
	0x52, 0x07, 0x63, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x73, 0x12, 0x1a, 0x0a, 0x08, 0x75, 0x73, 0x65,
	0x72, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x75, 0x73, 0x65,
	0x72, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x12, 0x20, 0x0a, 0x0c, 0x73,
	0x69, 0x67, 0x6e, 0x5f, 0x6f, 0x75, 0x74, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x05, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0a, 0x73, 0x69, 0x67, 0x6e, 0x4f, 0x75, 0x74, 0x55, 0x72, 0x6c, 0x12, 0x27, 0x0a,
	0x05, 0x74, 0x68, 0x65, 0x6d, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x11, 0x2e, 0x61,
	0x74, 0x6c, 0x61, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x49, 0x54, 0x68, 0x65, 0x6d, 0x65, 0x52,
	0x05, 0x74, 0x68, 0x65, 0x6d, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x6c, 0x61, 0x6e, 0x67, 0x75, 0x61,
	0x67, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x6c, 0x61, 0x6e, 0x67, 0x75, 0x61,
	0x67, 0x65, 0x12, 0x2f, 0x0a, 0x07, 0x74, 0x68, 0x65, 0x6d, 0x69, 0x6e, 0x67, 0x18, 0x08, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x61, 0x74, 0x6c, 0x61, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x54,
	0x68, 0x65, 0x6d, 0x69, 0x6e, 0x67, 0x42, 0x02, 0x18, 0x01, 0x52, 0x07, 0x74, 0x68, 0x65, 0x6d,
	0x69, 0x6e, 0x67, 0x12, 0x33, 0x0a, 0x15, 0x6e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x73, 0x5f, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x18, 0x09, 0x20, 0x01,
	0x28, 0x08, 0x52, 0x14, 0x6e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x73, 0x45, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x12, 0x17, 0x0a, 0x07, 0x75, 0x73, 0x65, 0x72,
	0x5f, 0x69, 0x64, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49,
	0x64, 0x12, 0x3b, 0x0a, 0x0d, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x64, 0x61,
	0x74, 0x61, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x61, 0x74, 0x6c, 0x61, 0x73,
	0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x44, 0x61, 0x74, 0x61,
	0x52, 0x0c, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x44, 0x61, 0x74, 0x61, 0x12, 0x33,
	0x0a, 0x15, 0x69, 0x6d, 0x70, 0x65, 0x72, 0x73, 0x6f, 0x6e, 0x61, 0x74, 0x65, 0x65, 0x5f, 0x75,
	0x73, 0x65, 0x72, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x09, 0x52, 0x14, 0x69,
	0x6d, 0x70, 0x65, 0x72, 0x73, 0x6f, 0x6e, 0x61, 0x74, 0x65, 0x65, 0x55, 0x73, 0x65, 0x72, 0x6e,
	0x61, 0x6d, 0x65, 0x12, 0x25, 0x0a, 0x0e, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x5f, 0x76, 0x65, 0x72,
	0x69, 0x66, 0x69, 0x65, 0x64, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0d, 0x65, 0x6d, 0x61,
	0x69, 0x6c, 0x56, 0x65, 0x72, 0x69, 0x66, 0x69, 0x65, 0x64, 0x12, 0x48, 0x0a, 0x12, 0x75, 0x73,
	0x65, 0x72, 0x5f, 0x73, 0x77, 0x69, 0x74, 0x63, 0x68, 0x65, 0x72, 0x5f, 0x64, 0x61, 0x74, 0x61,
	0x18, 0x0e, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x61, 0x74, 0x6c, 0x61, 0x73, 0x2e, 0x76,
	0x31, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x53, 0x77, 0x69, 0x74, 0x63, 0x68, 0x65, 0x72, 0x44, 0x61,
	0x74, 0x61, 0x52, 0x10, 0x75, 0x73, 0x65, 0x72, 0x53, 0x77, 0x69, 0x74, 0x63, 0x68, 0x65, 0x72,
	0x44, 0x61, 0x74, 0x61, 0x12, 0x21, 0x0a, 0x0c, 0x70, 0x61, 0x72, 0x74, 0x6e, 0x65, 0x72, 0x5f,
	0x6e, 0x61, 0x6d, 0x65, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x70, 0x61, 0x72, 0x74,
	0x6e, 0x65, 0x72, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x44, 0x0a, 0x15, 0x62, 0x75, 0x73, 0x69, 0x6e,
	0x65, 0x73, 0x73, 0x5f, 0x61, 0x70, 0x70, 0x5f, 0x75, 0x69, 0x5f, 0x74, 0x68, 0x65, 0x6d, 0x65,
	0x18, 0x10, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x11, 0x2e, 0x61, 0x74, 0x6c, 0x61, 0x73, 0x2e, 0x76,
	0x31, 0x2e, 0x55, 0x49, 0x54, 0x68, 0x65, 0x6d, 0x65, 0x52, 0x12, 0x62, 0x75, 0x73, 0x69, 0x6e,
	0x65, 0x73, 0x73, 0x41, 0x70, 0x70, 0x55, 0x69, 0x54, 0x68, 0x65, 0x6d, 0x65, 0x22, 0xdc, 0x01,
	0x0a, 0x18, 0x47, 0x65, 0x74, 0x4e, 0x61, 0x76, 0x69, 0x67, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x44,
	0x61, 0x74, 0x61, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x28, 0x0a, 0x10, 0x61, 0x63,
	0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x47, 0x72, 0x6f,
	0x75, 0x70, 0x49, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x70, 0x61,
	0x74, 0x68, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x50,
	0x61, 0x74, 0x68, 0x12, 0x1d, 0x0a, 0x0a, 0x70, 0x61, 0x72, 0x74, 0x6e, 0x65, 0x72, 0x5f, 0x69,
	0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x70, 0x61, 0x72, 0x74, 0x6e, 0x65, 0x72,
	0x49, 0x64, 0x12, 0x1b, 0x0a, 0x09, 0x6d, 0x61, 0x72, 0x6b, 0x65, 0x74, 0x5f, 0x69, 0x64, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x6d, 0x61, 0x72, 0x6b, 0x65, 0x74, 0x49, 0x64, 0x12,
	0x3b, 0x0a, 0x0d, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x5f, 0x6d, 0x6f, 0x64, 0x65,
	0x18, 0x05, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x16, 0x2e, 0x61, 0x74, 0x6c, 0x61, 0x73, 0x2e, 0x76,
	0x31, 0x2e, 0x50, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x4d, 0x6f, 0x64, 0x65, 0x52, 0x0c,
	0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x4d, 0x6f, 0x64, 0x65, 0x22, 0x5e, 0x0a, 0x13,
	0x47, 0x65, 0x74, 0x53, 0x61, 0x6c, 0x65, 0x73, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x12, 0x28, 0x0a, 0x10, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x67,
	0x72, 0x6f, 0x75, 0x70, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x61,
	0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x49, 0x64, 0x12, 0x1d, 0x0a,
	0x0a, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x70, 0x61, 0x74, 0x68, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x09, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x50, 0x61, 0x74, 0x68, 0x22, 0x4a, 0x0a, 0x14,
	0x47, 0x65, 0x74, 0x53, 0x61, 0x6c, 0x65, 0x73, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x12, 0x32, 0x0a, 0x0a, 0x73, 0x61, 0x6c, 0x65, 0x73, 0x5f, 0x69, 0x6e,
	0x66, 0x6f, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x13, 0x2e, 0x61, 0x74, 0x6c, 0x61, 0x73,
	0x2e, 0x76, 0x31, 0x2e, 0x53, 0x61, 0x6c, 0x65, 0x73, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x09, 0x73,
	0x61, 0x6c, 0x65, 0x73, 0x49, 0x6e, 0x66, 0x6f, 0x22, 0xc9, 0x01, 0x0a, 0x15, 0x53, 0x69, 0x64,
	0x65, 0x4e, 0x61, 0x76, 0x69, 0x67, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x65, 0x63, 0x74, 0x69,
	0x6f, 0x6e, 0x12, 0x25, 0x0a, 0x0e, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x6c, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x74, 0x72, 0x61, 0x6e,
	0x73, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x12, 0x50, 0x0a, 0x15, 0x73, 0x69, 0x64,
	0x65, 0x5f, 0x6e, 0x61, 0x76, 0x69, 0x67, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x74, 0x65,
	0x6d, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x61, 0x74, 0x6c, 0x61, 0x73,
	0x2e, 0x76, 0x31, 0x2e, 0x53, 0x69, 0x64, 0x65, 0x4e, 0x61, 0x76, 0x69, 0x67, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x49, 0x74, 0x65, 0x6d, 0x52, 0x13, 0x73, 0x69, 0x64, 0x65, 0x4e, 0x61, 0x76, 0x69,
	0x67, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x74, 0x65, 0x6d, 0x73, 0x12, 0x14, 0x0a, 0x05, 0x6c,
	0x61, 0x62, 0x65, 0x6c, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x6c, 0x61, 0x62, 0x65,
	0x6c, 0x12, 0x21, 0x0a, 0x0c, 0x63, 0x68, 0x69, 0x70, 0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e,
	0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x63, 0x68, 0x69, 0x70, 0x43, 0x6f, 0x6e,
	0x74, 0x65, 0x6e, 0x74, 0x22, 0xea, 0x02, 0x0a, 0x17, 0x53, 0x69, 0x64, 0x65, 0x4e, 0x61, 0x76,
	0x69, 0x67, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x43, 0x6f, 0x6e, 0x74, 0x61, 0x69, 0x6e, 0x65, 0x72,
	0x12, 0x25, 0x0a, 0x0e, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f,
	0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x6c,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x12, 0x50, 0x0a, 0x15, 0x73, 0x69, 0x64, 0x65, 0x5f,
	0x6e, 0x61, 0x76, 0x69, 0x67, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x74, 0x65, 0x6d, 0x73,
	0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x61, 0x74, 0x6c, 0x61, 0x73, 0x2e, 0x76,
	0x31, 0x2e, 0x53, 0x69, 0x64, 0x65, 0x4e, 0x61, 0x76, 0x69, 0x67, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x49, 0x74, 0x65, 0x6d, 0x52, 0x13, 0x73, 0x69, 0x64, 0x65, 0x4e, 0x61, 0x76, 0x69, 0x67, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x49, 0x74, 0x65, 0x6d, 0x73, 0x12, 0x12, 0x0a, 0x04, 0x69, 0x63, 0x6f,
	0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x69, 0x63, 0x6f, 0x6e, 0x12, 0x19, 0x0a,
	0x08, 0x6c, 0x6f, 0x67, 0x6f, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x07, 0x6c, 0x6f, 0x67, 0x6f, 0x55, 0x72, 0x6c, 0x12, 0x14, 0x0a, 0x05, 0x6c, 0x61, 0x62, 0x65,
	0x6c, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x12, 0x1b,
	0x0a, 0x09, 0x73, 0x68, 0x6f, 0x77, 0x5f, 0x69, 0x63, 0x6f, 0x6e, 0x18, 0x06, 0x20, 0x01, 0x28,
	0x08, 0x52, 0x08, 0x73, 0x68, 0x6f, 0x77, 0x49, 0x63, 0x6f, 0x6e, 0x12, 0x21, 0x0a, 0x0c, 0x63,
	0x68, 0x69, 0x70, 0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x18, 0x07, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0b, 0x63, 0x68, 0x69, 0x70, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x12, 0x10,
	0x0a, 0x03, 0x75, 0x72, 0x6c, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x75, 0x72, 0x6c,
	0x12, 0x1a, 0x0a, 0x08, 0x70, 0x69, 0x6e, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x18, 0x09, 0x20, 0x01,
	0x28, 0x08, 0x52, 0x08, 0x70, 0x69, 0x6e, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x12, 0x23, 0x0a, 0x0d,
	0x6e, 0x61, 0x76, 0x69, 0x67, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x0a, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0c, 0x6e, 0x61, 0x76, 0x69, 0x67, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x49,
	0x64, 0x22, 0xf2, 0x04, 0x0a, 0x12, 0x53, 0x69, 0x64, 0x65, 0x4e, 0x61, 0x76, 0x69, 0x67, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x4c, 0x69, 0x6e, 0x6b, 0x12, 0x23, 0x0a, 0x0d, 0x6e, 0x61, 0x76, 0x69,
	0x67, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0c, 0x6e, 0x61, 0x76, 0x69, 0x67, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x12, 0x10, 0x0a,
	0x03, 0x75, 0x72, 0x6c, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x75, 0x72, 0x6c, 0x12,
	0x12, 0x0a, 0x04, 0x70, 0x61, 0x74, 0x68, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x70,
	0x61, 0x74, 0x68, 0x12, 0x2e, 0x0a, 0x13, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x70,
	0x72, 0x6f, 0x76, 0x69, 0x64, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x11, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x50, 0x72, 0x6f, 0x76, 0x69, 0x64, 0x65,
	0x72, 0x49, 0x64, 0x12, 0x19, 0x0a, 0x08, 0x6c, 0x6f, 0x67, 0x6f, 0x5f, 0x75, 0x72, 0x6c, 0x18,
	0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6c, 0x6f, 0x67, 0x6f, 0x55, 0x72, 0x6c, 0x12, 0x12,
	0x0a, 0x04, 0x69, 0x63, 0x6f, 0x6e, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x69, 0x63,
	0x6f, 0x6e, 0x12, 0x25, 0x0a, 0x0e, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x6c, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x5f, 0x69, 0x64, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x74, 0x72, 0x61, 0x6e,
	0x73, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x12, 0x1a, 0x0a, 0x08, 0x65, 0x78, 0x74,
	0x65, 0x72, 0x6e, 0x61, 0x6c, 0x18, 0x08, 0x20, 0x01, 0x28, 0x08, 0x52, 0x08, 0x65, 0x78, 0x74,
	0x65, 0x72, 0x6e, 0x61, 0x6c, 0x12, 0x14, 0x0a, 0x05, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x18, 0x09,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x12, 0x1b, 0x0a, 0x09, 0x73,
	0x68, 0x6f, 0x77, 0x5f, 0x69, 0x63, 0x6f, 0x6e, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x08, 0x52, 0x08,
	0x73, 0x68, 0x6f, 0x77, 0x49, 0x63, 0x6f, 0x6e, 0x12, 0x1a, 0x0a, 0x08, 0x70, 0x69, 0x6e, 0x6e,
	0x61, 0x62, 0x6c, 0x65, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x08, 0x52, 0x08, 0x70, 0x69, 0x6e, 0x6e,
	0x61, 0x62, 0x6c, 0x65, 0x12, 0x21, 0x0a, 0x0c, 0x63, 0x68, 0x69, 0x70, 0x5f, 0x63, 0x6f, 0x6e,
	0x74, 0x65, 0x6e, 0x74, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x63, 0x68, 0x69, 0x70,
	0x43, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x12, 0x19, 0x0a, 0x08, 0x69, 0x73, 0x5f, 0x74, 0x72,
	0x69, 0x61, 0x6c, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x08, 0x52, 0x07, 0x69, 0x73, 0x54, 0x72, 0x69,
	0x61, 0x6c, 0x12, 0x23, 0x0a, 0x0d, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x72, 0x65, 0x71, 0x75, 0x69,
	0x72, 0x65, 0x64, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0c, 0x75, 0x73, 0x65, 0x72, 0x52,
	0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x12, 0x25, 0x0a, 0x0f, 0x6f, 0x70, 0x65, 0x6e, 0x5f,
	0x69, 0x6e, 0x5f, 0x6e, 0x65, 0x77, 0x5f, 0x74, 0x61, 0x62, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x08,
	0x52, 0x0c, 0x6f, 0x70, 0x65, 0x6e, 0x49, 0x6e, 0x4e, 0x65, 0x77, 0x54, 0x61, 0x62, 0x12, 0x39,
	0x0a, 0x09, 0x73, 0x75, 0x62, 0x5f, 0x6c, 0x69, 0x6e, 0x6b, 0x73, 0x18, 0x10, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x1c, 0x2e, 0x61, 0x74, 0x6c, 0x61, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x69, 0x64,
	0x65, 0x4e, 0x61, 0x76, 0x69, 0x67, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x4c, 0x69, 0x6e, 0x6b, 0x52,
	0x08, 0x73, 0x75, 0x62, 0x4c, 0x69, 0x6e, 0x6b, 0x73, 0x12, 0x1d, 0x0a, 0x0a, 0x6c, 0x61, 0x75,
	0x6e, 0x63, 0x68, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x11, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x6c,
	0x61, 0x75, 0x6e, 0x63, 0x68, 0x55, 0x72, 0x6c, 0x12, 0x3c, 0x0a, 0x1a, 0x64, 0x65, 0x73, 0x63,
	0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x6c, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x12, 0x20, 0x01, 0x28, 0x09, 0x52, 0x18, 0x64, 0x65,
	0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x6c, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x22, 0x71, 0x0a, 0x0c, 0x44, 0x72, 0x6f, 0x70, 0x64, 0x6f,
	0x77, 0x6e, 0x49, 0x74, 0x65, 0x6d, 0x12, 0x10, 0x0a, 0x03, 0x75, 0x72, 0x6c, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x03, 0x75, 0x72, 0x6c, 0x12, 0x12, 0x0a, 0x04, 0x70, 0x61, 0x74, 0x68,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x70, 0x61, 0x74, 0x68, 0x12, 0x25, 0x0a, 0x0e,
	0x74, 0x72, 0x61, 0x6e, 0x73, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x6c, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x49, 0x64, 0x12, 0x14, 0x0a, 0x05, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x05, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x22, 0xaa, 0x02, 0x0a, 0x12, 0x53, 0x69,
	0x64, 0x65, 0x4e, 0x61, 0x76, 0x69, 0x67, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x74, 0x65, 0x6d,
	0x12, 0x59, 0x0a, 0x17, 0x73, 0x69, 0x64, 0x65, 0x5f, 0x6e, 0x61, 0x76, 0x69, 0x67, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x5f, 0x73, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x1f, 0x2e, 0x61, 0x74, 0x6c, 0x61, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x69, 0x64,
	0x65, 0x4e, 0x61, 0x76, 0x69, 0x67, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x65, 0x63, 0x74, 0x69,
	0x6f, 0x6e, 0x48, 0x00, 0x52, 0x15, 0x73, 0x69, 0x64, 0x65, 0x4e, 0x61, 0x76, 0x69, 0x67, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x53, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x5f, 0x0a, 0x19, 0x73,
	0x69, 0x64, 0x65, 0x5f, 0x6e, 0x61, 0x76, 0x69, 0x67, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x63,
	0x6f, 0x6e, 0x74, 0x61, 0x69, 0x6e, 0x65, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21,
	0x2e, 0x61, 0x74, 0x6c, 0x61, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x69, 0x64, 0x65, 0x4e, 0x61,
	0x76, 0x69, 0x67, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x43, 0x6f, 0x6e, 0x74, 0x61, 0x69, 0x6e, 0x65,
	0x72, 0x48, 0x00, 0x52, 0x17, 0x73, 0x69, 0x64, 0x65, 0x4e, 0x61, 0x76, 0x69, 0x67, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x43, 0x6f, 0x6e, 0x74, 0x61, 0x69, 0x6e, 0x65, 0x72, 0x12, 0x50, 0x0a, 0x14,
	0x73, 0x69, 0x64, 0x65, 0x5f, 0x6e, 0x61, 0x76, 0x69, 0x67, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f,
	0x6c, 0x69, 0x6e, 0x6b, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x61, 0x74, 0x6c,
	0x61, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x69, 0x64, 0x65, 0x4e, 0x61, 0x76, 0x69, 0x67, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x4c, 0x69, 0x6e, 0x6b, 0x48, 0x00, 0x52, 0x12, 0x73, 0x69, 0x64, 0x65,
	0x4e, 0x61, 0x76, 0x69, 0x67, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x4c, 0x69, 0x6e, 0x6b, 0x42, 0x06,
	0x0a, 0x04, 0x69, 0x74, 0x65, 0x6d, 0x22, 0x69, 0x0a, 0x09, 0x53, 0x61, 0x6c, 0x65, 0x73, 0x49,
	0x6e, 0x66, 0x6f, 0x12, 0x1f, 0x0a, 0x0b, 0x6d, 0x61, 0x72, 0x6b, 0x65, 0x74, 0x5f, 0x6e, 0x61,
	0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x6d, 0x61, 0x72, 0x6b, 0x65, 0x74,
	0x4e, 0x61, 0x6d, 0x65, 0x12, 0x3b, 0x0a, 0x0d, 0x73, 0x61, 0x6c, 0x65, 0x73, 0x5f, 0x63, 0x6f,
	0x6e, 0x74, 0x61, 0x63, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x61, 0x74,
	0x6c, 0x61, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x61, 0x6c, 0x65, 0x73, 0x43, 0x6f, 0x6e, 0x74,
	0x61, 0x63, 0x74, 0x52, 0x0c, 0x73, 0x61, 0x6c, 0x65, 0x73, 0x43, 0x6f, 0x6e, 0x74, 0x61, 0x63,
	0x74, 0x22, 0xbc, 0x02, 0x0a, 0x0c, 0x53, 0x61, 0x6c, 0x65, 0x73, 0x43, 0x6f, 0x6e, 0x74, 0x61,
	0x63, 0x74, 0x12, 0x26, 0x0a, 0x0f, 0x73, 0x61, 0x6c, 0x65, 0x73, 0x5f, 0x70, 0x65, 0x72, 0x73,
	0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x73, 0x61, 0x6c,
	0x65, 0x73, 0x50, 0x65, 0x72, 0x73, 0x6f, 0x6e, 0x49, 0x64, 0x12, 0x14, 0x0a, 0x05, 0x65, 0x6d,
	0x61, 0x69, 0x6c, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x65, 0x6d, 0x61, 0x69, 0x6c,
	0x12, 0x1d, 0x0a, 0x0a, 0x66, 0x69, 0x72, 0x73, 0x74, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x66, 0x69, 0x72, 0x73, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x12,
	0x1b, 0x0a, 0x09, 0x6c, 0x61, 0x73, 0x74, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x08, 0x6c, 0x61, 0x73, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x21, 0x0a, 0x0c,
	0x70, 0x68, 0x6f, 0x6e, 0x65, 0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x05, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0b, 0x70, 0x68, 0x6f, 0x6e, 0x65, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x12,
	0x28, 0x0a, 0x10, 0x70, 0x68, 0x6f, 0x74, 0x6f, 0x5f, 0x75, 0x72, 0x6c, 0x5f, 0x73, 0x65, 0x63,
	0x75, 0x72, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x70, 0x68, 0x6f, 0x74, 0x6f,
	0x55, 0x72, 0x6c, 0x53, 0x65, 0x63, 0x75, 0x72, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x6a, 0x6f, 0x62,
	0x5f, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x6a, 0x6f,
	0x62, 0x54, 0x69, 0x74, 0x6c, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x72,
	0x79, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x72, 0x79,
	0x12, 0x2e, 0x0a, 0x13, 0x6d, 0x65, 0x65, 0x74, 0x69, 0x6e, 0x67, 0x5f, 0x62, 0x6f, 0x6f, 0x6b,
	0x69, 0x6e, 0x67, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x11, 0x6d,
	0x65, 0x65, 0x74, 0x69, 0x6e, 0x67, 0x42, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x55, 0x72, 0x6c,
	0x22, 0x6b, 0x0a, 0x05, 0x42, 0x72, 0x61, 0x6e, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d,
	0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x1d, 0x0a,
	0x0a, 0x70, 0x61, 0x74, 0x68, 0x5f, 0x6e, 0x6f, 0x64, 0x65, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28,
	0x09, 0x52, 0x09, 0x70, 0x61, 0x74, 0x68, 0x4e, 0x6f, 0x64, 0x65, 0x73, 0x12, 0x1d, 0x0a, 0x0a,
	0x68, 0x61, 0x73, 0x5f, 0x61, 0x63, 0x63, 0x65, 0x73, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x08,
	0x52, 0x09, 0x68, 0x61, 0x73, 0x41, 0x63, 0x63, 0x65, 0x73, 0x73, 0x12, 0x10, 0x0a, 0x03, 0x75,
	0x72, 0x6c, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x75, 0x72, 0x6c, 0x22, 0x31, 0x0a,
	0x0a, 0x50, 0x69, 0x6e, 0x6e, 0x65, 0x64, 0x49, 0x74, 0x65, 0x6d, 0x12, 0x23, 0x0a, 0x0d, 0x6e,
	0x61, 0x76, 0x69, 0x67, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0c, 0x6e, 0x61, 0x76, 0x69, 0x67, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64,
	0x22, 0x61, 0x0a, 0x15, 0x45, 0x78, 0x69, 0x74, 0x4c, 0x69, 0x6e, 0x6b, 0x43, 0x6f, 0x6e, 0x66,
	0x69, 0x67, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x24, 0x0a, 0x0e, 0x65, 0x78, 0x69,
	0x74, 0x5f, 0x6c, 0x69, 0x6e, 0x6b, 0x5f, 0x74, 0x65, 0x78, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0c, 0x65, 0x78, 0x69, 0x74, 0x4c, 0x69, 0x6e, 0x6b, 0x54, 0x65, 0x78, 0x74, 0x12,
	0x22, 0x0a, 0x0d, 0x65, 0x78, 0x69, 0x74, 0x5f, 0x6c, 0x69, 0x6e, 0x6b, 0x5f, 0x75, 0x72, 0x6c,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x65, 0x78, 0x69, 0x74, 0x4c, 0x69, 0x6e, 0x6b,
	0x55, 0x72, 0x6c, 0x22, 0xe0, 0x03, 0x0a, 0x08, 0x42, 0x72, 0x61, 0x6e, 0x64, 0x69, 0x6e, 0x67,
	0x12, 0x27, 0x0a, 0x05, 0x74, 0x68, 0x65, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32,
	0x11, 0x2e, 0x61, 0x74, 0x6c, 0x61, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x49, 0x54, 0x68, 0x65,
	0x6d, 0x65, 0x52, 0x05, 0x74, 0x68, 0x65, 0x6d, 0x65, 0x12, 0x19, 0x0a, 0x08, 0x6c, 0x6f, 0x67,
	0x6f, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6c, 0x6f, 0x67,
	0x6f, 0x55, 0x72, 0x6c, 0x12, 0x21, 0x0a, 0x0c, 0x70, 0x61, 0x72, 0x74, 0x6e, 0x65, 0x72, 0x5f,
	0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x70, 0x61, 0x72, 0x74,
	0x6e, 0x65, 0x72, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x63, 0x65, 0x6e, 0x74, 0x65,
	0x72, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x63, 0x65,
	0x6e, 0x74, 0x65, 0x72, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x2f, 0x0a, 0x07, 0x74, 0x68, 0x65, 0x6d,
	0x69, 0x6e, 0x67, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x61, 0x74, 0x6c, 0x61,
	0x73, 0x2e, 0x76, 0x31, 0x2e, 0x54, 0x68, 0x65, 0x6d, 0x69, 0x6e, 0x67, 0x42, 0x02, 0x18, 0x01,
	0x52, 0x07, 0x74, 0x68, 0x65, 0x6d, 0x69, 0x6e, 0x67, 0x12, 0x2e, 0x0a, 0x13, 0x63, 0x6f, 0x62,
	0x72, 0x61, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x5f, 0x6c, 0x6f, 0x67, 0x6f, 0x5f, 0x75, 0x72, 0x6c,
	0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x11, 0x63, 0x6f, 0x62, 0x72, 0x61, 0x6e, 0x64, 0x69,
	0x6e, 0x67, 0x4c, 0x6f, 0x67, 0x6f, 0x55, 0x72, 0x6c, 0x12, 0x1f, 0x0a, 0x0b, 0x6d, 0x61, 0x72,
	0x6b, 0x65, 0x74, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a,
	0x6d, 0x61, 0x72, 0x6b, 0x65, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x2b, 0x0a, 0x12, 0x64, 0x61,
	0x72, 0x6b, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x5f, 0x6c, 0x6f, 0x67, 0x6f, 0x5f, 0x75, 0x72, 0x6c,
	0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x64, 0x61, 0x72, 0x6b, 0x4d, 0x6f, 0x64, 0x65,
	0x4c, 0x6f, 0x67, 0x6f, 0x55, 0x72, 0x6c, 0x12, 0x44, 0x0a, 0x15, 0x62, 0x75, 0x73, 0x69, 0x6e,
	0x65, 0x73, 0x73, 0x5f, 0x61, 0x70, 0x70, 0x5f, 0x75, 0x69, 0x5f, 0x74, 0x68, 0x65, 0x6d, 0x65,
	0x18, 0x09, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x11, 0x2e, 0x61, 0x74, 0x6c, 0x61, 0x73, 0x2e, 0x76,
	0x31, 0x2e, 0x55, 0x49, 0x54, 0x68, 0x65, 0x6d, 0x65, 0x52, 0x12, 0x62, 0x75, 0x73, 0x69, 0x6e,
	0x65, 0x73, 0x73, 0x41, 0x70, 0x70, 0x55, 0x69, 0x54, 0x68, 0x65, 0x6d, 0x65, 0x12, 0x57, 0x0a,
	0x17, 0x65, 0x78, 0x69, 0x74, 0x5f, 0x6c, 0x69, 0x6e, 0x6b, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69,
	0x67, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1f,
	0x2e, 0x61, 0x74, 0x6c, 0x61, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x45, 0x78, 0x69, 0x74, 0x4c, 0x69,
	0x6e, 0x6b, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52,
	0x15, 0x65, 0x78, 0x69, 0x74, 0x4c, 0x69, 0x6e, 0x6b, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x75,
	0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x22, 0xac, 0x01, 0x0a, 0x0c, 0x41, 0x63, 0x63, 0x6f, 0x75,
	0x6e, 0x74, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x12, 0x28, 0x0a, 0x10, 0x61, 0x63, 0x63, 0x6f, 0x75,
	0x6e, 0x74, 0x5f, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x49,
	0x64, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x12,
	0x32, 0x0a, 0x15, 0x61, 0x63, 0x74, 0x69, 0x76, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x70, 0x72, 0x6f,
	0x64, 0x75, 0x63, 0x74, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x09, 0x52, 0x13,
	0x61, 0x63, 0x74, 0x69, 0x76, 0x61, 0x74, 0x65, 0x64, 0x50, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74,
	0x49, 0x64, 0x73, 0x12, 0x10, 0x0a, 0x03, 0x75, 0x72, 0x6c, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x03, 0x75, 0x72, 0x6c, 0x22, 0x7e, 0x0a, 0x08, 0x4c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x12, 0x3d, 0x0a, 0x0d, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x67, 0x72, 0x6f,
	0x75, 0x70, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x61, 0x74, 0x6c, 0x61, 0x73,
	0x2e, 0x76, 0x31, 0x2e, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x47, 0x72, 0x6f, 0x75, 0x70,
	0x48, 0x00, 0x52, 0x0c, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x47, 0x72, 0x6f, 0x75, 0x70,
	0x12, 0x27, 0x0a, 0x05, 0x62, 0x72, 0x61, 0x6e, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x0f, 0x2e, 0x61, 0x74, 0x6c, 0x61, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x42, 0x72, 0x61, 0x6e, 0x64,
	0x48, 0x00, 0x52, 0x05, 0x62, 0x72, 0x61, 0x6e, 0x64, 0x42, 0x0a, 0x0a, 0x08, 0x6c, 0x6f, 0x63,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x22, 0x64, 0x0a, 0x15, 0x41, 0x73, 0x73, 0x6f, 0x63, 0x69, 0x61,
	0x74, 0x65, 0x64, 0x4c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x44, 0x73, 0x12, 0x2a,
	0x0a, 0x11, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f,
	0x69, 0x64, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0f, 0x61, 0x63, 0x63, 0x6f, 0x75,
	0x6e, 0x74, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x49, 0x64, 0x73, 0x12, 0x1f, 0x0a, 0x0b, 0x67, 0x72,
	0x6f, 0x75, 0x70, 0x5f, 0x70, 0x61, 0x74, 0x68, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x09, 0x52,
	0x0a, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x50, 0x61, 0x74, 0x68, 0x73, 0x22, 0x8d, 0x04, 0x0a, 0x07,
	0x54, 0x68, 0x65, 0x6d, 0x69, 0x6e, 0x67, 0x12, 0x23, 0x0a, 0x0d, 0x70, 0x72, 0x69, 0x6d, 0x61,
	0x72, 0x79, 0x5f, 0x63, 0x6f, 0x6c, 0x6f, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c,
	0x70, 0x72, 0x69, 0x6d, 0x61, 0x72, 0x79, 0x43, 0x6f, 0x6c, 0x6f, 0x72, 0x12, 0x2e, 0x0a, 0x13,
	0x70, 0x72, 0x69, 0x6d, 0x61, 0x72, 0x79, 0x5f, 0x68, 0x6f, 0x76, 0x65, 0x72, 0x5f, 0x63, 0x6f,
	0x6c, 0x6f, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x11, 0x70, 0x72, 0x69, 0x6d, 0x61,
	0x72, 0x79, 0x48, 0x6f, 0x76, 0x65, 0x72, 0x43, 0x6f, 0x6c, 0x6f, 0x72, 0x12, 0x30, 0x0a, 0x14,
	0x70, 0x72, 0x69, 0x6d, 0x61, 0x72, 0x79, 0x5f, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x5f, 0x63,
	0x6f, 0x6c, 0x6f, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x12, 0x70, 0x72, 0x69, 0x6d,
	0x61, 0x72, 0x79, 0x41, 0x63, 0x74, 0x69, 0x76, 0x65, 0x43, 0x6f, 0x6c, 0x6f, 0x72, 0x12, 0x27,
	0x0a, 0x0f, 0x73, 0x65, 0x63, 0x6f, 0x6e, 0x64, 0x61, 0x72, 0x79, 0x5f, 0x63, 0x6f, 0x6c, 0x6f,
	0x72, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x73, 0x65, 0x63, 0x6f, 0x6e, 0x64, 0x61,
	0x72, 0x79, 0x43, 0x6f, 0x6c, 0x6f, 0x72, 0x12, 0x32, 0x0a, 0x15, 0x73, 0x65, 0x63, 0x6f, 0x6e,
	0x64, 0x61, 0x72, 0x79, 0x5f, 0x68, 0x6f, 0x76, 0x65, 0x72, 0x5f, 0x63, 0x6f, 0x6c, 0x6f, 0x72,
	0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x13, 0x73, 0x65, 0x63, 0x6f, 0x6e, 0x64, 0x61, 0x72,
	0x79, 0x48, 0x6f, 0x76, 0x65, 0x72, 0x43, 0x6f, 0x6c, 0x6f, 0x72, 0x12, 0x34, 0x0a, 0x16, 0x73,
	0x65, 0x63, 0x6f, 0x6e, 0x64, 0x61, 0x72, 0x79, 0x5f, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x5f,
	0x63, 0x6f, 0x6c, 0x6f, 0x72, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x14, 0x73, 0x65, 0x63,
	0x6f, 0x6e, 0x64, 0x61, 0x72, 0x79, 0x41, 0x63, 0x74, 0x69, 0x76, 0x65, 0x43, 0x6f, 0x6c, 0x6f,
	0x72, 0x12, 0x1d, 0x0a, 0x0a, 0x66, 0x6f, 0x6e, 0x74, 0x5f, 0x63, 0x6f, 0x6c, 0x6f, 0x72, 0x18,
	0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x66, 0x6f, 0x6e, 0x74, 0x43, 0x6f, 0x6c, 0x6f, 0x72,
	0x12, 0x2e, 0x0a, 0x13, 0x66, 0x6f, 0x6e, 0x74, 0x5f, 0x64, 0x69, 0x73, 0x61, 0x62, 0x6c, 0x65,
	0x64, 0x5f, 0x63, 0x6f, 0x6c, 0x6f, 0x72, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x11, 0x66,
	0x6f, 0x6e, 0x74, 0x44, 0x69, 0x73, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x43, 0x6f, 0x6c, 0x6f, 0x72,
	0x12, 0x23, 0x0a, 0x0d, 0x61, 0x63, 0x63, 0x65, 0x6e, 0x74, 0x73, 0x5f, 0x63, 0x6f, 0x6c, 0x6f,
	0x72, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x61, 0x63, 0x63, 0x65, 0x6e, 0x74, 0x73,
	0x43, 0x6f, 0x6c, 0x6f, 0x72, 0x12, 0x30, 0x0a, 0x14, 0x61, 0x63, 0x63, 0x65, 0x6e, 0x74, 0x73,
	0x5f, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x5f, 0x63, 0x6f, 0x6c, 0x6f, 0x72, 0x18, 0x0a, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x12, 0x61, 0x63, 0x63, 0x65, 0x6e, 0x74, 0x73, 0x41, 0x63, 0x74, 0x69,
	0x76, 0x65, 0x43, 0x6f, 0x6c, 0x6f, 0x72, 0x12, 0x1f, 0x0a, 0x0b, 0x66, 0x6f, 0x63, 0x75, 0x73,
	0x5f, 0x63, 0x6f, 0x6c, 0x6f, 0x72, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x66, 0x6f,
	0x63, 0x75, 0x73, 0x43, 0x6f, 0x6c, 0x6f, 0x72, 0x12, 0x21, 0x0a, 0x0c, 0x62, 0x6f, 0x72, 0x64,
	0x65, 0x72, 0x5f, 0x63, 0x6f, 0x6c, 0x6f, 0x72, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b,
	0x62, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x43, 0x6f, 0x6c, 0x6f, 0x72, 0x22, 0x59, 0x0a, 0x0f, 0x52,
	0x65, 0x74, 0x65, 0x6e, 0x74, 0x69, 0x6f, 0x6e, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x46,
	0x0a, 0x1f, 0x63, 0x61, 0x6e, 0x63, 0x65, 0x6c, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6e,
	0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x65, 0x6d, 0x61, 0x69,
	0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x1d, 0x63, 0x61, 0x6e, 0x63, 0x65, 0x6c, 0x6c,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x4e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x45, 0x6d, 0x61, 0x69, 0x6c, 0x22, 0x44, 0x0a, 0x0e, 0x54, 0x6f, 0x74, 0x61, 0x6c, 0x4c,
	0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x1a, 0x0a, 0x08, 0x61, 0x63, 0x63, 0x6f,
	0x75, 0x6e, 0x74, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x08, 0x61, 0x63, 0x63, 0x6f,
	0x75, 0x6e, 0x74, 0x73, 0x12, 0x16, 0x0a, 0x06, 0x62, 0x72, 0x61, 0x6e, 0x64, 0x73, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x06, 0x62, 0x72, 0x61, 0x6e, 0x64, 0x73, 0x22, 0x91, 0x07, 0x0a,
	0x19, 0x47, 0x65, 0x74, 0x4e, 0x61, 0x76, 0x69, 0x67, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x44, 0x61,
	0x74, 0x61, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x2e, 0x0a, 0x08, 0x62, 0x72,
	0x61, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x61,
	0x74, 0x6c, 0x61, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x42, 0x72, 0x61, 0x6e, 0x64, 0x69, 0x6e, 0x67,
	0x52, 0x08, 0x62, 0x72, 0x61, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x12, 0x32, 0x0a, 0x0a, 0x73, 0x61,
	0x6c, 0x65, 0x73, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x13,
	0x2e, 0x61, 0x74, 0x6c, 0x61, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x61, 0x6c, 0x65, 0x73, 0x49,
	0x6e, 0x66, 0x6f, 0x52, 0x09, 0x73, 0x61, 0x6c, 0x65, 0x73, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x37,
	0x0a, 0x0c, 0x70, 0x69, 0x6e, 0x6e, 0x65, 0x64, 0x5f, 0x69, 0x74, 0x65, 0x6d, 0x73, 0x18, 0x04,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x61, 0x74, 0x6c, 0x61, 0x73, 0x2e, 0x76, 0x31, 0x2e,
	0x50, 0x69, 0x6e, 0x6e, 0x65, 0x64, 0x49, 0x74, 0x65, 0x6d, 0x52, 0x0b, 0x70, 0x69, 0x6e, 0x6e,
	0x65, 0x64, 0x49, 0x74, 0x65, 0x6d, 0x73, 0x12, 0x57, 0x0a, 0x17, 0x61, 0x73, 0x73, 0x6f, 0x63,
	0x69, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x69,
	0x64, 0x73, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x61, 0x74, 0x6c, 0x61, 0x73,
	0x2e, 0x76, 0x31, 0x2e, 0x41, 0x73, 0x73, 0x6f, 0x63, 0x69, 0x61, 0x74, 0x65, 0x64, 0x4c, 0x6f,
	0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x44, 0x73, 0x52, 0x15, 0x61, 0x73, 0x73, 0x6f, 0x63,
	0x69, 0x61, 0x74, 0x65, 0x64, 0x4c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x73,
	0x12, 0x29, 0x0a, 0x10, 0x64, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x5f, 0x6c, 0x6f, 0x63, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x64, 0x65, 0x66, 0x61,
	0x75, 0x6c, 0x74, 0x4c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x1a, 0x0a, 0x08, 0x6c,
	0x61, 0x6e, 0x67, 0x75, 0x61, 0x67, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x6c,
	0x61, 0x6e, 0x67, 0x75, 0x61, 0x67, 0x65, 0x12, 0x3d, 0x0a, 0x0e, 0x64, 0x72, 0x6f, 0x70, 0x64,
	0x6f, 0x77, 0x6e, 0x5f, 0x69, 0x74, 0x65, 0x6d, 0x73, 0x18, 0x09, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x16, 0x2e, 0x61, 0x74, 0x6c, 0x61, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x44, 0x72, 0x6f, 0x70, 0x64,
	0x6f, 0x77, 0x6e, 0x49, 0x74, 0x65, 0x6d, 0x52, 0x0d, 0x64, 0x72, 0x6f, 0x70, 0x64, 0x6f, 0x77,
	0x6e, 0x49, 0x74, 0x65, 0x6d, 0x73, 0x12, 0x2c, 0x0a, 0x12, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e,
	0x74, 0x5f, 0x62, 0x72, 0x61, 0x6e, 0x64, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x0a, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x10, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x42, 0x72, 0x61, 0x6e, 0x64,
	0x4e, 0x61, 0x6d, 0x65, 0x12, 0x33, 0x0a, 0x09, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x76, 0x69, 0x65,
	0x77, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x16, 0x2e, 0x61, 0x74, 0x6c, 0x61, 0x73, 0x2e,
	0x76, 0x31, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x56, 0x69, 0x65, 0x77, 0x54, 0x79, 0x70, 0x65, 0x52,
	0x08, 0x75, 0x73, 0x65, 0x72, 0x56, 0x69, 0x65, 0x77, 0x12, 0x44, 0x0a, 0x10, 0x72, 0x65, 0x74,
	0x65, 0x6e, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x18, 0x0c, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x61, 0x74, 0x6c, 0x61, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x52,
	0x65, 0x74, 0x65, 0x6e, 0x74, 0x69, 0x6f, 0x6e, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x0f,
	0x72, 0x65, 0x74, 0x65, 0x6e, 0x74, 0x69, 0x6f, 0x6e, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12,
	0x41, 0x0a, 0x0f, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x5f, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x73, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x61, 0x74, 0x6c, 0x61, 0x73,
	0x2e, 0x76, 0x31, 0x2e, 0x54, 0x6f, 0x74, 0x61, 0x6c, 0x4c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x73, 0x52, 0x0e, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x4c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x73, 0x12, 0x17, 0x0a, 0x07, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x0e, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x12, 0x32, 0x0a, 0x15, 0x62,
	0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x61, 0x70, 0x70, 0x5f, 0x62, 0x72, 0x61, 0x6e,
	0x64, 0x69, 0x6e, 0x67, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x08, 0x52, 0x13, 0x62, 0x75, 0x73, 0x69,
	0x6e, 0x65, 0x73, 0x73, 0x41, 0x70, 0x70, 0x42, 0x72, 0x61, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x12,
	0x30, 0x0a, 0x14, 0x64, 0x69, 0x73, 0x61, 0x62, 0x6c, 0x65, 0x5f, 0x62, 0x75, 0x73, 0x69, 0x6e,
	0x65, 0x73, 0x73, 0x5f, 0x6e, 0x61, 0x76, 0x18, 0x10, 0x20, 0x01, 0x28, 0x08, 0x52, 0x12, 0x64,
	0x69, 0x73, 0x61, 0x62, 0x6c, 0x65, 0x42, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x4e, 0x61,
	0x76, 0x12, 0x47, 0x0a, 0x10, 0x6e, 0x61, 0x76, 0x69, 0x67, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f,
	0x69, 0x74, 0x65, 0x6d, 0x73, 0x18, 0x11, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x61, 0x74,
	0x6c, 0x61, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x69, 0x64, 0x65, 0x4e, 0x61, 0x76, 0x69, 0x67,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x74, 0x65, 0x6d, 0x52, 0x0f, 0x6e, 0x61, 0x76, 0x69, 0x67,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x74, 0x65, 0x6d, 0x73, 0x12, 0x38, 0x0a, 0x18, 0x64, 0x69,
	0x73, 0x61, 0x62, 0x6c, 0x65, 0x5f, 0x70, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x5f, 0x73, 0x77,
	0x69, 0x74, 0x63, 0x68, 0x65, 0x72, 0x18, 0x12, 0x20, 0x01, 0x28, 0x08, 0x52, 0x16, 0x64, 0x69,
	0x73, 0x61, 0x62, 0x6c, 0x65, 0x50, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x53, 0x77, 0x69, 0x74,
	0x63, 0x68, 0x65, 0x72, 0x4a, 0x04, 0x08, 0x01, 0x10, 0x02, 0x4a, 0x04, 0x08, 0x07, 0x10, 0x08,
	0x22, 0x5c, 0x0a, 0x0e, 0x53, 0x65, 0x74, 0x50, 0x69, 0x6e, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x12, 0x1e, 0x0a, 0x0a, 0x69, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x66, 0x69, 0x65, 0x72,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x69, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x66, 0x69,
	0x65, 0x72, 0x12, 0x2a, 0x0a, 0x05, 0x69, 0x74, 0x65, 0x6d, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x14, 0x2e, 0x61, 0x74, 0x6c, 0x61, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x69, 0x6e,
	0x6e, 0x65, 0x64, 0x49, 0x74, 0x65, 0x6d, 0x52, 0x05, 0x69, 0x74, 0x65, 0x6d, 0x73, 0x22, 0x30,
	0x0a, 0x0e, 0x47, 0x65, 0x74, 0x50, 0x69, 0x6e, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x12, 0x1e, 0x0a, 0x0a, 0x69, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x66, 0x69, 0x65, 0x72, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x69, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x66, 0x69, 0x65, 0x72,
	0x22, 0x3d, 0x0a, 0x0f, 0x47, 0x65, 0x74, 0x50, 0x69, 0x6e, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x12, 0x2a, 0x0a, 0x05, 0x69, 0x74, 0x65, 0x6d, 0x73, 0x18, 0x01, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x14, 0x2e, 0x61, 0x74, 0x6c, 0x61, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x69,
	0x6e, 0x6e, 0x65, 0x64, 0x49, 0x74, 0x65, 0x6d, 0x52, 0x05, 0x69, 0x74, 0x65, 0x6d, 0x73, 0x22,
	0xa1, 0x02, 0x0a, 0x13, 0x47, 0x65, 0x74, 0x4c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x54, 0x0a, 0x0e, 0x61, 0x63, 0x63, 0x6f, 0x75,
	0x6e, 0x74, 0x5f, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x2b, 0x2e, 0x61, 0x74, 0x6c, 0x61, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x4c, 0x6f,
	0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x41,
	0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x73, 0x48, 0x00, 0x52, 0x0d,
	0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x73, 0x12, 0x3e, 0x0a,
	0x06, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x24, 0x2e,
	0x61, 0x74, 0x6c, 0x61, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x4c, 0x6f, 0x63, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x47, 0x72, 0x6f,
	0x75, 0x70, 0x73, 0x48, 0x00, 0x52, 0x06, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x73, 0x1a, 0x3b, 0x0a,
	0x0d, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x73, 0x12, 0x2a,
	0x0a, 0x11, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f,
	0x69, 0x64, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0f, 0x61, 0x63, 0x63, 0x6f, 0x75,
	0x6e, 0x74, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x49, 0x64, 0x73, 0x1a, 0x29, 0x0a, 0x06, 0x47, 0x72,
	0x6f, 0x75, 0x70, 0x73, 0x12, 0x1f, 0x0a, 0x0b, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x70, 0x61,
	0x74, 0x68, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0a, 0x67, 0x72, 0x6f, 0x75, 0x70,
	0x50, 0x61, 0x74, 0x68, 0x73, 0x42, 0x0c, 0x0a, 0x0a, 0x69, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x66,
	0x69, 0x65, 0x72, 0x22, 0x48, 0x0a, 0x14, 0x47, 0x65, 0x74, 0x4c, 0x6f, 0x63, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x30, 0x0a, 0x09, 0x6c,
	0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x12,
	0x2e, 0x61, 0x74, 0x6c, 0x61, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x6f, 0x63, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x52, 0x09, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x22, 0xd5, 0x01,
	0x0a, 0x1c, 0x4c, 0x69, 0x73, 0x74, 0x45, 0x6c, 0x65, 0x76, 0x61, 0x74, 0x65, 0x64, 0x4c, 0x6f,
	0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1d,
	0x0a, 0x0a, 0x70, 0x61, 0x72, 0x74, 0x6e, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x09, 0x70, 0x61, 0x72, 0x74, 0x6e, 0x65, 0x72, 0x49, 0x64, 0x12, 0x16, 0x0a,
	0x06, 0x63, 0x75, 0x72, 0x73, 0x6f, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x63,
	0x75, 0x72, 0x73, 0x6f, 0x72, 0x12, 0x1b, 0x0a, 0x09, 0x70, 0x61, 0x67, 0x65, 0x5f, 0x73, 0x69,
	0x7a, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x08, 0x70, 0x61, 0x67, 0x65, 0x53, 0x69,
	0x7a, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x65, 0x61, 0x72, 0x63, 0x68, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x06, 0x73, 0x65, 0x61, 0x72, 0x63, 0x68, 0x12, 0x27, 0x0a, 0x0e, 0x61, 0x63,
	0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x73, 0x18, 0x05, 0x20, 0x01,
	0x28, 0x08, 0x48, 0x00, 0x52, 0x0d, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x47, 0x72, 0x6f,
	0x75, 0x70, 0x73, 0x12, 0x18, 0x0a, 0x06, 0x62, 0x72, 0x61, 0x6e, 0x64, 0x73, 0x18, 0x06, 0x20,
	0x01, 0x28, 0x08, 0x48, 0x00, 0x52, 0x06, 0x62, 0x72, 0x61, 0x6e, 0x64, 0x73, 0x42, 0x06, 0x0a,
	0x04, 0x74, 0x79, 0x70, 0x65, 0x22, 0x84, 0x01, 0x0a, 0x1d, 0x4c, 0x69, 0x73, 0x74, 0x45, 0x6c,
	0x65, 0x76, 0x61, 0x74, 0x65, 0x64, 0x4c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x30, 0x0a, 0x09, 0x6c, 0x6f, 0x63, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x61, 0x74, 0x6c,
	0x61, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x09,
	0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x16, 0x0a, 0x06, 0x63, 0x75, 0x72,
	0x73, 0x6f, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x63, 0x75, 0x72, 0x73, 0x6f,
	0x72, 0x12, 0x19, 0x0a, 0x08, 0x68, 0x61, 0x73, 0x5f, 0x6d, 0x6f, 0x72, 0x65, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x08, 0x52, 0x07, 0x68, 0x61, 0x73, 0x4d, 0x6f, 0x72, 0x65, 0x22, 0xdf, 0x01, 0x0a,
	0x14, 0x4c, 0x69, 0x73, 0x74, 0x4c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1d, 0x0a, 0x0a, 0x70, 0x61, 0x72, 0x74, 0x6e, 0x65, 0x72,
	0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x70, 0x61, 0x72, 0x74, 0x6e,
	0x65, 0x72, 0x49, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x63, 0x75, 0x72, 0x73, 0x6f, 0x72, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x63, 0x75, 0x72, 0x73, 0x6f, 0x72, 0x12, 0x1b, 0x0a, 0x09,
	0x70, 0x61, 0x67, 0x65, 0x5f, 0x73, 0x69, 0x7a, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x08, 0x70, 0x61, 0x67, 0x65, 0x53, 0x69, 0x7a, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x65, 0x61,
	0x72, 0x63, 0x68, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x73, 0x65, 0x61, 0x72, 0x63,
	0x68, 0x12, 0x34, 0x0a, 0x16, 0x69, 0x6e, 0x63, 0x6c, 0x75, 0x64, 0x65, 0x5f, 0x61, 0x63, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x73, 0x18, 0x06, 0x20, 0x01, 0x28,
	0x08, 0x52, 0x14, 0x69, 0x6e, 0x63, 0x6c, 0x75, 0x64, 0x65, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e,
	0x74, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x73, 0x12, 0x25, 0x0a, 0x0e, 0x69, 0x6e, 0x63, 0x6c, 0x75,
	0x64, 0x65, 0x5f, 0x62, 0x72, 0x61, 0x6e, 0x64, 0x73, 0x18, 0x07, 0x20, 0x01, 0x28, 0x08, 0x52,
	0x0d, 0x69, 0x6e, 0x63, 0x6c, 0x75, 0x64, 0x65, 0x42, 0x72, 0x61, 0x6e, 0x64, 0x73, 0x22, 0x7c,
	0x0a, 0x15, 0x4c, 0x69, 0x73, 0x74, 0x4c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x30, 0x0a, 0x09, 0x6c, 0x6f, 0x63, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x61, 0x74, 0x6c,
	0x61, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x09,
	0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x16, 0x0a, 0x06, 0x63, 0x75, 0x72,
	0x73, 0x6f, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x63, 0x75, 0x72, 0x73, 0x6f,
	0x72, 0x12, 0x19, 0x0a, 0x08, 0x68, 0x61, 0x73, 0x5f, 0x6d, 0x6f, 0x72, 0x65, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x08, 0x52, 0x07, 0x68, 0x61, 0x73, 0x4d, 0x6f, 0x72, 0x65, 0x22, 0x30, 0x0a, 0x12,
	0x53, 0x65, 0x74, 0x4c, 0x61, 0x6e, 0x67, 0x75, 0x61, 0x67, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x12, 0x1a, 0x0a, 0x08, 0x6c, 0x61, 0x6e, 0x67, 0x75, 0x61, 0x67, 0x65, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x6c, 0x61, 0x6e, 0x67, 0x75, 0x61, 0x67, 0x65, 0x22, 0x14,
	0x0a, 0x12, 0x47, 0x65, 0x74, 0x4c, 0x61, 0x6e, 0x67, 0x75, 0x61, 0x67, 0x65, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x22, 0x31, 0x0a, 0x13, 0x47, 0x65, 0x74, 0x4c, 0x61, 0x6e, 0x67, 0x75,
	0x61, 0x67, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x6c,
	0x61, 0x6e, 0x67, 0x75, 0x61, 0x67, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x6c,
	0x61, 0x6e, 0x67, 0x75, 0x61, 0x67, 0x65, 0x22, 0x56, 0x0a, 0x10, 0x43, 0x6f, 0x6e, 0x74, 0x61,
	0x63, 0x74, 0x55, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x28, 0x0a, 0x10, 0x61,
	0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x69, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x47, 0x72,
	0x6f, 0x75, 0x70, 0x49, 0x64, 0x12, 0x18, 0x0a, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x22,
	0x8f, 0x01, 0x0a, 0x19, 0x53, 0x65, 0x74, 0x44, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x4c, 0x6f,
	0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1d, 0x0a,
	0x0a, 0x70, 0x61, 0x72, 0x74, 0x6e, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x09, 0x70, 0x61, 0x72, 0x74, 0x6e, 0x65, 0x72, 0x49, 0x64, 0x12, 0x2a, 0x0a, 0x10,
	0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x48, 0x00, 0x52, 0x0e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e,
	0x74, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x49, 0x64, 0x12, 0x1b, 0x0a, 0x08, 0x67, 0x72, 0x6f, 0x75,
	0x70, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x48, 0x00, 0x52, 0x07, 0x67, 0x72,
	0x6f, 0x75, 0x70, 0x49, 0x64, 0x42, 0x0a, 0x0a, 0x08, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x22, 0x3a, 0x0a, 0x19, 0x47, 0x65, 0x74, 0x44, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x4c,
	0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1d,
	0x0a, 0x0a, 0x70, 0x61, 0x72, 0x74, 0x6e, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x09, 0x70, 0x61, 0x72, 0x74, 0x6e, 0x65, 0x72, 0x49, 0x64, 0x22, 0x71, 0x0a,
	0x1a, 0x47, 0x65, 0x74, 0x44, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x4c, 0x6f, 0x63, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x2a, 0x0a, 0x10, 0x61,
	0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x69, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x48, 0x00, 0x52, 0x0e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74,
	0x47, 0x72, 0x6f, 0x75, 0x70, 0x49, 0x64, 0x12, 0x1b, 0x0a, 0x08, 0x67, 0x72, 0x6f, 0x75, 0x70,
	0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x48, 0x00, 0x52, 0x07, 0x67, 0x72, 0x6f,
	0x75, 0x70, 0x49, 0x64, 0x42, 0x0a, 0x0a, 0x08, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x2a, 0x4e, 0x0a, 0x07, 0x55, 0x49, 0x54, 0x68, 0x65, 0x6d, 0x65, 0x12, 0x11, 0x0a, 0x0d, 0x55,
	0x49, 0x5f, 0x54, 0x48, 0x45, 0x4d, 0x45, 0x5f, 0x44, 0x41, 0x52, 0x4b, 0x10, 0x00, 0x12, 0x12,
	0x0a, 0x0e, 0x55, 0x49, 0x5f, 0x54, 0x48, 0x45, 0x4d, 0x45, 0x5f, 0x4c, 0x49, 0x47, 0x48, 0x54,
	0x10, 0x01, 0x12, 0x1c, 0x0a, 0x18, 0x55, 0x49, 0x5f, 0x54, 0x48, 0x45, 0x4d, 0x45, 0x5f, 0x55,
	0x53, 0x45, 0x52, 0x5f, 0x50, 0x52, 0x45, 0x46, 0x45, 0x52, 0x45, 0x4e, 0x43, 0x45, 0x10, 0x02,
	0x2a, 0x40, 0x0a, 0x0c, 0x55, 0x73, 0x65, 0x72, 0x56, 0x69, 0x65, 0x77, 0x54, 0x79, 0x70, 0x65,
	0x12, 0x16, 0x0a, 0x12, 0x55, 0x53, 0x45, 0x52, 0x5f, 0x56, 0x49, 0x45, 0x57, 0x5f, 0x54, 0x59,
	0x50, 0x45, 0x5f, 0x53, 0x4d, 0x42, 0x10, 0x00, 0x12, 0x18, 0x0a, 0x14, 0x55, 0x53, 0x45, 0x52,
	0x5f, 0x56, 0x49, 0x45, 0x57, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x41, 0x44, 0x4d, 0x49, 0x4e,
	0x10, 0x01, 0x2a, 0x23, 0x0a, 0x0c, 0x50, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x4d, 0x6f,
	0x64, 0x65, 0x12, 0x07, 0x0a, 0x03, 0x57, 0x45, 0x42, 0x10, 0x00, 0x12, 0x0a, 0x0a, 0x06, 0x4d,
	0x4f, 0x42, 0x49, 0x4c, 0x45, 0x10, 0x01, 0x32, 0xe8, 0x07, 0x0a, 0x05, 0x41, 0x74, 0x6c, 0x61,
	0x73, 0x12, 0x59, 0x0a, 0x07, 0x47, 0x65, 0x74, 0x44, 0x61, 0x74, 0x61, 0x12, 0x18, 0x2e, 0x61,
	0x74, 0x6c, 0x61, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x44, 0x61, 0x74, 0x61, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x19, 0x2e, 0x61, 0x74, 0x6c, 0x61, 0x73, 0x2e, 0x76,
	0x31, 0x2e, 0x47, 0x65, 0x74, 0x44, 0x61, 0x74, 0x61, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x22, 0x19, 0x82, 0xb5, 0x18, 0x15, 0x0a, 0x05, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x0a, 0x0c,
	0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x2d, 0x61, 0x70, 0x70, 0x12, 0x77, 0x0a, 0x11,
	0x47, 0x65, 0x74, 0x4e, 0x61, 0x76, 0x69, 0x67, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x44, 0x61, 0x74,
	0x61, 0x12, 0x22, 0x2e, 0x61, 0x74, 0x6c, 0x61, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74,
	0x4e, 0x61, 0x76, 0x69, 0x67, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x44, 0x61, 0x74, 0x61, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x23, 0x2e, 0x61, 0x74, 0x6c, 0x61, 0x73, 0x2e, 0x76, 0x31,
	0x2e, 0x47, 0x65, 0x74, 0x4e, 0x61, 0x76, 0x69, 0x67, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x44, 0x61,
	0x74, 0x61, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x19, 0x82, 0xb5, 0x18, 0x15,
	0x0a, 0x05, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x0a, 0x0c, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73,
	0x73, 0x2d, 0x61, 0x70, 0x70, 0x12, 0x68, 0x0a, 0x0c, 0x47, 0x65, 0x74, 0x53, 0x61, 0x6c, 0x65,
	0x73, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x1d, 0x2e, 0x61, 0x74, 0x6c, 0x61, 0x73, 0x2e, 0x76, 0x31,
	0x2e, 0x47, 0x65, 0x74, 0x53, 0x61, 0x6c, 0x65, 0x73, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x1a, 0x1e, 0x2e, 0x61, 0x74, 0x6c, 0x61, 0x73, 0x2e, 0x76, 0x31, 0x2e,
	0x47, 0x65, 0x74, 0x53, 0x61, 0x6c, 0x65, 0x73, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x22, 0x19, 0x82, 0xb5, 0x18, 0x15, 0x0a, 0x05, 0x61, 0x64, 0x6d, 0x69,
	0x6e, 0x0a, 0x0c, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x2d, 0x61, 0x70, 0x70, 0x12,
	0x68, 0x0a, 0x0c, 0x47, 0x65, 0x74, 0x4c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12,
	0x1d, 0x2e, 0x61, 0x74, 0x6c, 0x61, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x4c, 0x6f,
	0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1e,
	0x2e, 0x61, 0x74, 0x6c, 0x61, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x4c, 0x6f, 0x63,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x19,
	0x82, 0xb5, 0x18, 0x15, 0x0a, 0x05, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x0a, 0x0c, 0x62, 0x75, 0x73,
	0x69, 0x6e, 0x65, 0x73, 0x73, 0x2d, 0x61, 0x70, 0x70, 0x12, 0x83, 0x01, 0x0a, 0x15, 0x4c, 0x69,
	0x73, 0x74, 0x45, 0x6c, 0x65, 0x76, 0x61, 0x74, 0x65, 0x64, 0x4c, 0x6f, 0x63, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x73, 0x12, 0x26, 0x2e, 0x61, 0x74, 0x6c, 0x61, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x4c,
	0x69, 0x73, 0x74, 0x45, 0x6c, 0x65, 0x76, 0x61, 0x74, 0x65, 0x64, 0x4c, 0x6f, 0x63, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x27, 0x2e, 0x61, 0x74,
	0x6c, 0x61, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x45, 0x6c, 0x65, 0x76, 0x61,
	0x74, 0x65, 0x64, 0x4c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x22, 0x19, 0x82, 0xb5, 0x18, 0x15, 0x0a, 0x05, 0x61, 0x64, 0x6d, 0x69,
	0x6e, 0x0a, 0x0c, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x2d, 0x61, 0x70, 0x70, 0x12,
	0x6b, 0x0a, 0x0d, 0x4c, 0x69, 0x73, 0x74, 0x4c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73,
	0x12, 0x1e, 0x2e, 0x61, 0x74, 0x6c, 0x61, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74,
	0x4c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x1a, 0x1f, 0x2e, 0x61, 0x74, 0x6c, 0x61, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74,
	0x4c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x22, 0x19, 0x82, 0xb5, 0x18, 0x15, 0x0a, 0x05, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x0a, 0x0c,
	0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x2d, 0x61, 0x70, 0x70, 0x12, 0x5a, 0x0a, 0x09,
	0x43, 0x6f, 0x6e, 0x74, 0x61, 0x63, 0x74, 0x55, 0x73, 0x12, 0x1a, 0x2e, 0x61, 0x74, 0x6c, 0x61,
	0x73, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x6f, 0x6e, 0x74, 0x61, 0x63, 0x74, 0x55, 0x73, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x22, 0x19, 0x82,
	0xb5, 0x18, 0x15, 0x0a, 0x05, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x0a, 0x0c, 0x62, 0x75, 0x73, 0x69,
	0x6e, 0x65, 0x73, 0x73, 0x2d, 0x61, 0x70, 0x70, 0x12, 0x6c, 0x0a, 0x12, 0x53, 0x65, 0x74, 0x44,
	0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x4c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x23,
	0x2e, 0x61, 0x74, 0x6c, 0x61, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65, 0x74, 0x44, 0x65, 0x66,
	0x61, 0x75, 0x6c, 0x74, 0x4c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x1a, 0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x22, 0x19, 0x82, 0xb5, 0x18,
	0x15, 0x0a, 0x05, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x0a, 0x0c, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65,
	0x73, 0x73, 0x2d, 0x61, 0x70, 0x70, 0x12, 0x7a, 0x0a, 0x12, 0x47, 0x65, 0x74, 0x44, 0x65, 0x66,
	0x61, 0x75, 0x6c, 0x74, 0x4c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x23, 0x2e, 0x61,
	0x74, 0x6c, 0x61, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x44, 0x65, 0x66, 0x61, 0x75,
	0x6c, 0x74, 0x4c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x1a, 0x24, 0x2e, 0x61, 0x74, 0x6c, 0x61, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74,
	0x44, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x4c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x19, 0x82, 0xb5, 0x18, 0x15, 0x0a, 0x05, 0x61,
	0x64, 0x6d, 0x69, 0x6e, 0x0a, 0x0c, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x2d, 0x61,
	0x70, 0x70, 0x32, 0xb9, 0x01, 0x0a, 0x04, 0x50, 0x69, 0x6e, 0x73, 0x12, 0x56, 0x0a, 0x07, 0x53,
	0x65, 0x74, 0x50, 0x69, 0x6e, 0x73, 0x12, 0x18, 0x2e, 0x61, 0x74, 0x6c, 0x61, 0x73, 0x2e, 0x76,
	0x31, 0x2e, 0x53, 0x65, 0x74, 0x50, 0x69, 0x6e, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x1a, 0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62,
	0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x22, 0x19, 0x82, 0xb5, 0x18, 0x15, 0x0a, 0x05,
	0x61, 0x64, 0x6d, 0x69, 0x6e, 0x0a, 0x0c, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x2d,
	0x61, 0x70, 0x70, 0x12, 0x59, 0x0a, 0x07, 0x47, 0x65, 0x74, 0x50, 0x69, 0x6e, 0x73, 0x12, 0x18,
	0x2e, 0x61, 0x74, 0x6c, 0x61, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x50, 0x69, 0x6e,
	0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x19, 0x2e, 0x61, 0x74, 0x6c, 0x61, 0x73,
	0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x50, 0x69, 0x6e, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x22, 0x19, 0x82, 0xb5, 0x18, 0x15, 0x0a, 0x05, 0x61, 0x64, 0x6d, 0x69, 0x6e,
	0x0a, 0x0c, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x2d, 0x61, 0x70, 0x70, 0x32, 0xd2,
	0x01, 0x0a, 0x09, 0x4c, 0x61, 0x6e, 0x67, 0x75, 0x61, 0x67, 0x65, 0x73, 0x12, 0x5e, 0x0a, 0x0b,
	0x53, 0x65, 0x74, 0x4c, 0x61, 0x6e, 0x67, 0x75, 0x61, 0x67, 0x65, 0x12, 0x1c, 0x2e, 0x61, 0x74,
	0x6c, 0x61, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65, 0x74, 0x4c, 0x61, 0x6e, 0x67, 0x75, 0x61,
	0x67, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74,
	0x79, 0x22, 0x19, 0x82, 0xb5, 0x18, 0x15, 0x0a, 0x05, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x0a, 0x0c,
	0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x2d, 0x61, 0x70, 0x70, 0x12, 0x65, 0x0a, 0x0b,
	0x47, 0x65, 0x74, 0x4c, 0x61, 0x6e, 0x67, 0x75, 0x61, 0x67, 0x65, 0x12, 0x1c, 0x2e, 0x61, 0x74,
	0x6c, 0x61, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x4c, 0x61, 0x6e, 0x67, 0x75, 0x61,
	0x67, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1d, 0x2e, 0x61, 0x74, 0x6c, 0x61,
	0x73, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x4c, 0x61, 0x6e, 0x67, 0x75, 0x61, 0x67, 0x65,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x19, 0x82, 0xb5, 0x18, 0x15, 0x0a, 0x05,
	0x61, 0x64, 0x6d, 0x69, 0x6e, 0x0a, 0x0c, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x2d,
	0x61, 0x70, 0x70, 0x42, 0x66, 0x0a, 0x1f, 0x63, 0x6f, 0x6d, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x61,
	0x73, 0x74, 0x61, 0x2e, 0x61, 0x74, 0x6c, 0x61, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x67, 0x65, 0x6e,
	0x65, 0x72, 0x61, 0x74, 0x65, 0x64, 0x42, 0x08, 0x41, 0x70, 0x69, 0x50, 0x72, 0x6f, 0x74, 0x6f,
	0x5a, 0x39, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x76, 0x65, 0x6e,
	0x64, 0x61, 0x73, 0x74, 0x61, 0x2f, 0x67, 0x65, 0x6e, 0x65, 0x72, 0x61, 0x74, 0x65, 0x64, 0x2d,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x73, 0x2d, 0x67, 0x6f, 0x2f, 0x61, 0x74, 0x6c, 0x61, 0x73, 0x2f,
	0x76, 0x31, 0x3b, 0x61, 0x74, 0x6c, 0x61, 0x73, 0x5f, 0x76, 0x31, 0x62, 0x06, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x33,
}

var (
	file_atlas_v1_api_proto_rawDescOnce sync.Once
	file_atlas_v1_api_proto_rawDescData = file_atlas_v1_api_proto_rawDesc
)

func file_atlas_v1_api_proto_rawDescGZIP() []byte {
	file_atlas_v1_api_proto_rawDescOnce.Do(func() {
		file_atlas_v1_api_proto_rawDescData = protoimpl.X.CompressGZIP(file_atlas_v1_api_proto_rawDescData)
	})
	return file_atlas_v1_api_proto_rawDescData
}

var file_atlas_v1_api_proto_enumTypes = make([]protoimpl.EnumInfo, 3)
var file_atlas_v1_api_proto_msgTypes = make([]protoimpl.MessageInfo, 45)
var file_atlas_v1_api_proto_goTypes = []interface{}{
	(UITheme)(0),                              // 0: atlas.v1.UITheme
	(UserViewType)(0),                         // 1: atlas.v1.UserViewType
	(PlatformMode)(0),                         // 2: atlas.v1.PlatformMode
	(*UserNavigationItem)(nil),                // 3: atlas.v1.UserNavigationItem
	(*CenterNavigationItem)(nil),              // 4: atlas.v1.CenterNavigationItem
	(*GetDataRequest)(nil),                    // 5: atlas.v1.GetDataRequest
	(*LocationData)(nil),                      // 6: atlas.v1.LocationData
	(*UserSwitcherData)(nil),                  // 7: atlas.v1.UserSwitcherData
	(*GetDataResponse)(nil),                   // 8: atlas.v1.GetDataResponse
	(*GetNavigationDataRequest)(nil),          // 9: atlas.v1.GetNavigationDataRequest
	(*GetSalesInfoRequest)(nil),               // 10: atlas.v1.GetSalesInfoRequest
	(*GetSalesInfoResponse)(nil),              // 11: atlas.v1.GetSalesInfoResponse
	(*SideNavigationSection)(nil),             // 12: atlas.v1.SideNavigationSection
	(*SideNavigationContainer)(nil),           // 13: atlas.v1.SideNavigationContainer
	(*SideNavigationLink)(nil),                // 14: atlas.v1.SideNavigationLink
	(*DropdownItem)(nil),                      // 15: atlas.v1.DropdownItem
	(*SideNavigationItem)(nil),                // 16: atlas.v1.SideNavigationItem
	(*SalesInfo)(nil),                         // 17: atlas.v1.SalesInfo
	(*SalesContact)(nil),                      // 18: atlas.v1.SalesContact
	(*Brand)(nil),                             // 19: atlas.v1.Brand
	(*PinnedItem)(nil),                        // 20: atlas.v1.PinnedItem
	(*ExitLinkConfiguration)(nil),             // 21: atlas.v1.ExitLinkConfiguration
	(*Branding)(nil),                          // 22: atlas.v1.Branding
	(*AccountGroup)(nil),                      // 23: atlas.v1.AccountGroup
	(*Location)(nil),                          // 24: atlas.v1.Location
	(*AssociatedLocationIDs)(nil),             // 25: atlas.v1.AssociatedLocationIDs
	(*Theming)(nil),                           // 26: atlas.v1.Theming
	(*RetentionConfig)(nil),                   // 27: atlas.v1.RetentionConfig
	(*TotalLocations)(nil),                    // 28: atlas.v1.TotalLocations
	(*GetNavigationDataResponse)(nil),         // 29: atlas.v1.GetNavigationDataResponse
	(*SetPinsRequest)(nil),                    // 30: atlas.v1.SetPinsRequest
	(*GetPinsRequest)(nil),                    // 31: atlas.v1.GetPinsRequest
	(*GetPinsResponse)(nil),                   // 32: atlas.v1.GetPinsResponse
	(*GetLocationsRequest)(nil),               // 33: atlas.v1.GetLocationsRequest
	(*GetLocationsResponse)(nil),              // 34: atlas.v1.GetLocationsResponse
	(*ListElevatedLocationsRequest)(nil),      // 35: atlas.v1.ListElevatedLocationsRequest
	(*ListElevatedLocationsResponse)(nil),     // 36: atlas.v1.ListElevatedLocationsResponse
	(*ListLocationsRequest)(nil),              // 37: atlas.v1.ListLocationsRequest
	(*ListLocationsResponse)(nil),             // 38: atlas.v1.ListLocationsResponse
	(*SetLanguageRequest)(nil),                // 39: atlas.v1.SetLanguageRequest
	(*GetLanguageRequest)(nil),                // 40: atlas.v1.GetLanguageRequest
	(*GetLanguageResponse)(nil),               // 41: atlas.v1.GetLanguageResponse
	(*ContactUsRequest)(nil),                  // 42: atlas.v1.ContactUsRequest
	(*SetDefaultLocationRequest)(nil),         // 43: atlas.v1.SetDefaultLocationRequest
	(*GetDefaultLocationRequest)(nil),         // 44: atlas.v1.GetDefaultLocationRequest
	(*GetDefaultLocationResponse)(nil),        // 45: atlas.v1.GetDefaultLocationResponse
	(*GetLocationsRequest_AccountGroups)(nil), // 46: atlas.v1.GetLocationsRequest.AccountGroups
	(*GetLocationsRequest_Groups)(nil),        // 47: atlas.v1.GetLocationsRequest.Groups
	(*empty.Empty)(nil),                       // 48: google.protobuf.Empty
}
var file_atlas_v1_api_proto_depIdxs = []int32{
	3,  // 0: atlas.v1.GetDataResponse.user:type_name -> atlas.v1.UserNavigationItem
	4,  // 1: atlas.v1.GetDataResponse.centers:type_name -> atlas.v1.CenterNavigationItem
	0,  // 2: atlas.v1.GetDataResponse.theme:type_name -> atlas.v1.UITheme
	26, // 3: atlas.v1.GetDataResponse.theming:type_name -> atlas.v1.Theming
	6,  // 4: atlas.v1.GetDataResponse.location_data:type_name -> atlas.v1.LocationData
	7,  // 5: atlas.v1.GetDataResponse.user_switcher_data:type_name -> atlas.v1.UserSwitcherData
	0,  // 6: atlas.v1.GetDataResponse.business_app_ui_theme:type_name -> atlas.v1.UITheme
	2,  // 7: atlas.v1.GetNavigationDataRequest.platform_mode:type_name -> atlas.v1.PlatformMode
	17, // 8: atlas.v1.GetSalesInfoResponse.sales_info:type_name -> atlas.v1.SalesInfo
	16, // 9: atlas.v1.SideNavigationSection.side_navigation_items:type_name -> atlas.v1.SideNavigationItem
	16, // 10: atlas.v1.SideNavigationContainer.side_navigation_items:type_name -> atlas.v1.SideNavigationItem
	14, // 11: atlas.v1.SideNavigationLink.sub_links:type_name -> atlas.v1.SideNavigationLink
	12, // 12: atlas.v1.SideNavigationItem.side_navigation_section:type_name -> atlas.v1.SideNavigationSection
	13, // 13: atlas.v1.SideNavigationItem.side_navigation_container:type_name -> atlas.v1.SideNavigationContainer
	14, // 14: atlas.v1.SideNavigationItem.side_navigation_link:type_name -> atlas.v1.SideNavigationLink
	18, // 15: atlas.v1.SalesInfo.sales_contact:type_name -> atlas.v1.SalesContact
	0,  // 16: atlas.v1.Branding.theme:type_name -> atlas.v1.UITheme
	26, // 17: atlas.v1.Branding.theming:type_name -> atlas.v1.Theming
	0,  // 18: atlas.v1.Branding.business_app_ui_theme:type_name -> atlas.v1.UITheme
	21, // 19: atlas.v1.Branding.exit_link_configuration:type_name -> atlas.v1.ExitLinkConfiguration
	23, // 20: atlas.v1.Location.account_group:type_name -> atlas.v1.AccountGroup
	19, // 21: atlas.v1.Location.brand:type_name -> atlas.v1.Brand
	22, // 22: atlas.v1.GetNavigationDataResponse.branding:type_name -> atlas.v1.Branding
	17, // 23: atlas.v1.GetNavigationDataResponse.sales_info:type_name -> atlas.v1.SalesInfo
	20, // 24: atlas.v1.GetNavigationDataResponse.pinned_items:type_name -> atlas.v1.PinnedItem
	25, // 25: atlas.v1.GetNavigationDataResponse.associated_location_ids:type_name -> atlas.v1.AssociatedLocationIDs
	15, // 26: atlas.v1.GetNavigationDataResponse.dropdown_items:type_name -> atlas.v1.DropdownItem
	1,  // 27: atlas.v1.GetNavigationDataResponse.user_view:type_name -> atlas.v1.UserViewType
	27, // 28: atlas.v1.GetNavigationDataResponse.retention_config:type_name -> atlas.v1.RetentionConfig
	28, // 29: atlas.v1.GetNavigationDataResponse.total_locations:type_name -> atlas.v1.TotalLocations
	16, // 30: atlas.v1.GetNavigationDataResponse.navigation_items:type_name -> atlas.v1.SideNavigationItem
	20, // 31: atlas.v1.SetPinsRequest.items:type_name -> atlas.v1.PinnedItem
	20, // 32: atlas.v1.GetPinsResponse.items:type_name -> atlas.v1.PinnedItem
	46, // 33: atlas.v1.GetLocationsRequest.account_groups:type_name -> atlas.v1.GetLocationsRequest.AccountGroups
	47, // 34: atlas.v1.GetLocationsRequest.groups:type_name -> atlas.v1.GetLocationsRequest.Groups
	24, // 35: atlas.v1.GetLocationsResponse.locations:type_name -> atlas.v1.Location
	24, // 36: atlas.v1.ListElevatedLocationsResponse.locations:type_name -> atlas.v1.Location
	24, // 37: atlas.v1.ListLocationsResponse.locations:type_name -> atlas.v1.Location
	5,  // 38: atlas.v1.Atlas.GetData:input_type -> atlas.v1.GetDataRequest
	9,  // 39: atlas.v1.Atlas.GetNavigationData:input_type -> atlas.v1.GetNavigationDataRequest
	10, // 40: atlas.v1.Atlas.GetSalesInfo:input_type -> atlas.v1.GetSalesInfoRequest
	33, // 41: atlas.v1.Atlas.GetLocations:input_type -> atlas.v1.GetLocationsRequest
	35, // 42: atlas.v1.Atlas.ListElevatedLocations:input_type -> atlas.v1.ListElevatedLocationsRequest
	37, // 43: atlas.v1.Atlas.ListLocations:input_type -> atlas.v1.ListLocationsRequest
	42, // 44: atlas.v1.Atlas.ContactUs:input_type -> atlas.v1.ContactUsRequest
	43, // 45: atlas.v1.Atlas.SetDefaultLocation:input_type -> atlas.v1.SetDefaultLocationRequest
	44, // 46: atlas.v1.Atlas.GetDefaultLocation:input_type -> atlas.v1.GetDefaultLocationRequest
	30, // 47: atlas.v1.Pins.SetPins:input_type -> atlas.v1.SetPinsRequest
	31, // 48: atlas.v1.Pins.GetPins:input_type -> atlas.v1.GetPinsRequest
	39, // 49: atlas.v1.Languages.SetLanguage:input_type -> atlas.v1.SetLanguageRequest
	40, // 50: atlas.v1.Languages.GetLanguage:input_type -> atlas.v1.GetLanguageRequest
	8,  // 51: atlas.v1.Atlas.GetData:output_type -> atlas.v1.GetDataResponse
	29, // 52: atlas.v1.Atlas.GetNavigationData:output_type -> atlas.v1.GetNavigationDataResponse
	11, // 53: atlas.v1.Atlas.GetSalesInfo:output_type -> atlas.v1.GetSalesInfoResponse
	34, // 54: atlas.v1.Atlas.GetLocations:output_type -> atlas.v1.GetLocationsResponse
	36, // 55: atlas.v1.Atlas.ListElevatedLocations:output_type -> atlas.v1.ListElevatedLocationsResponse
	38, // 56: atlas.v1.Atlas.ListLocations:output_type -> atlas.v1.ListLocationsResponse
	48, // 57: atlas.v1.Atlas.ContactUs:output_type -> google.protobuf.Empty
	48, // 58: atlas.v1.Atlas.SetDefaultLocation:output_type -> google.protobuf.Empty
	45, // 59: atlas.v1.Atlas.GetDefaultLocation:output_type -> atlas.v1.GetDefaultLocationResponse
	48, // 60: atlas.v1.Pins.SetPins:output_type -> google.protobuf.Empty
	32, // 61: atlas.v1.Pins.GetPins:output_type -> atlas.v1.GetPinsResponse
	48, // 62: atlas.v1.Languages.SetLanguage:output_type -> google.protobuf.Empty
	41, // 63: atlas.v1.Languages.GetLanguage:output_type -> atlas.v1.GetLanguageResponse
	51, // [51:64] is the sub-list for method output_type
	38, // [38:51] is the sub-list for method input_type
	38, // [38:38] is the sub-list for extension type_name
	38, // [38:38] is the sub-list for extension extendee
	0,  // [0:38] is the sub-list for field type_name
}

func init() { file_atlas_v1_api_proto_init() }
func file_atlas_v1_api_proto_init() {
	if File_atlas_v1_api_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_atlas_v1_api_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UserNavigationItem); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_atlas_v1_api_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CenterNavigationItem); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_atlas_v1_api_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetDataRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_atlas_v1_api_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*LocationData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_atlas_v1_api_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UserSwitcherData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_atlas_v1_api_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetDataResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_atlas_v1_api_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetNavigationDataRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_atlas_v1_api_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetSalesInfoRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_atlas_v1_api_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetSalesInfoResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_atlas_v1_api_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SideNavigationSection); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_atlas_v1_api_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SideNavigationContainer); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_atlas_v1_api_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SideNavigationLink); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_atlas_v1_api_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DropdownItem); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_atlas_v1_api_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SideNavigationItem); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_atlas_v1_api_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SalesInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_atlas_v1_api_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SalesContact); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_atlas_v1_api_proto_msgTypes[16].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Brand); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_atlas_v1_api_proto_msgTypes[17].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PinnedItem); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_atlas_v1_api_proto_msgTypes[18].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ExitLinkConfiguration); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_atlas_v1_api_proto_msgTypes[19].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Branding); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_atlas_v1_api_proto_msgTypes[20].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AccountGroup); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_atlas_v1_api_proto_msgTypes[21].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Location); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_atlas_v1_api_proto_msgTypes[22].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AssociatedLocationIDs); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_atlas_v1_api_proto_msgTypes[23].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Theming); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_atlas_v1_api_proto_msgTypes[24].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RetentionConfig); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_atlas_v1_api_proto_msgTypes[25].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TotalLocations); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_atlas_v1_api_proto_msgTypes[26].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetNavigationDataResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_atlas_v1_api_proto_msgTypes[27].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SetPinsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_atlas_v1_api_proto_msgTypes[28].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetPinsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_atlas_v1_api_proto_msgTypes[29].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetPinsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_atlas_v1_api_proto_msgTypes[30].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetLocationsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_atlas_v1_api_proto_msgTypes[31].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetLocationsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_atlas_v1_api_proto_msgTypes[32].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListElevatedLocationsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_atlas_v1_api_proto_msgTypes[33].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListElevatedLocationsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_atlas_v1_api_proto_msgTypes[34].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListLocationsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_atlas_v1_api_proto_msgTypes[35].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListLocationsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_atlas_v1_api_proto_msgTypes[36].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SetLanguageRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_atlas_v1_api_proto_msgTypes[37].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetLanguageRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_atlas_v1_api_proto_msgTypes[38].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetLanguageResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_atlas_v1_api_proto_msgTypes[39].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ContactUsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_atlas_v1_api_proto_msgTypes[40].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SetDefaultLocationRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_atlas_v1_api_proto_msgTypes[41].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetDefaultLocationRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_atlas_v1_api_proto_msgTypes[42].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetDefaultLocationResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_atlas_v1_api_proto_msgTypes[43].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetLocationsRequest_AccountGroups); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_atlas_v1_api_proto_msgTypes[44].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetLocationsRequest_Groups); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_atlas_v1_api_proto_msgTypes[13].OneofWrappers = []interface{}{
		(*SideNavigationItem_SideNavigationSection)(nil),
		(*SideNavigationItem_SideNavigationContainer)(nil),
		(*SideNavigationItem_SideNavigationLink)(nil),
	}
	file_atlas_v1_api_proto_msgTypes[21].OneofWrappers = []interface{}{
		(*Location_AccountGroup)(nil),
		(*Location_Brand)(nil),
	}
	file_atlas_v1_api_proto_msgTypes[30].OneofWrappers = []interface{}{
		(*GetLocationsRequest_AccountGroups_)(nil),
		(*GetLocationsRequest_Groups_)(nil),
	}
	file_atlas_v1_api_proto_msgTypes[32].OneofWrappers = []interface{}{
		(*ListElevatedLocationsRequest_AccountGroups)(nil),
		(*ListElevatedLocationsRequest_Brands)(nil),
	}
	file_atlas_v1_api_proto_msgTypes[40].OneofWrappers = []interface{}{
		(*SetDefaultLocationRequest_AccountGroupId)(nil),
		(*SetDefaultLocationRequest_GroupId)(nil),
	}
	file_atlas_v1_api_proto_msgTypes[42].OneofWrappers = []interface{}{
		(*GetDefaultLocationResponse_AccountGroupId)(nil),
		(*GetDefaultLocationResponse_GroupId)(nil),
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_atlas_v1_api_proto_rawDesc,
			NumEnums:      3,
			NumMessages:   45,
			NumExtensions: 0,
			NumServices:   3,
		},
		GoTypes:           file_atlas_v1_api_proto_goTypes,
		DependencyIndexes: file_atlas_v1_api_proto_depIdxs,
		EnumInfos:         file_atlas_v1_api_proto_enumTypes,
		MessageInfos:      file_atlas_v1_api_proto_msgTypes,
	}.Build()
	File_atlas_v1_api_proto = out.File
	file_atlas_v1_api_proto_rawDesc = nil
	file_atlas_v1_api_proto_goTypes = nil
	file_atlas_v1_api_proto_depIdxs = nil
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConnInterface

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion6

// AtlasClient is the client API for Atlas service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type AtlasClient interface {
	// GetData gets the data that populates the Atlas navbar
	GetData(ctx context.Context, in *GetDataRequest, opts ...grpc.CallOption) (*GetDataResponse, error)
	// GetNavigationData gets the navigation data that populates the sidenav
	GetNavigationData(ctx context.Context, in *GetNavigationDataRequest, opts ...grpc.CallOption) (*GetNavigationDataResponse, error)
	// GetSalesInfo gets the sales info that populates the sidenav
	GetSalesInfo(ctx context.Context, in *GetSalesInfoRequest, opts ...grpc.CallOption) (*GetSalesInfoResponse, error)
	// Deprecated (use ListLocations): GetLocations returns associated account groups or groups
	GetLocations(ctx context.Context, in *GetLocationsRequest, opts ...grpc.CallOption) (*GetLocationsResponse, error)
	// Deprecated (use ListLocations): ListElevatedLocations returns a paged list of locations available to an elevated user.
	ListElevatedLocations(ctx context.Context, in *ListElevatedLocationsRequest, opts ...grpc.CallOption) (*ListElevatedLocationsResponse, error)
	// ListLocations returns a paged list of locations available to a user.
	ListLocations(ctx context.Context, in *ListLocationsRequest, opts ...grpc.CallOption) (*ListLocationsResponse, error)
	// ContactUs notifies the platform that the user is interested in contacting the partner
	ContactUs(ctx context.Context, in *ContactUsRequest, opts ...grpc.CallOption) (*empty.Empty, error)
	// SetDefaultLocationRequest sets a user's default location for the given partner.
	SetDefaultLocation(ctx context.Context, in *SetDefaultLocationRequest, opts ...grpc.CallOption) (*empty.Empty, error)
	// GetDefaultLocation` gets a user's default location that they have set for the given partner.`
	GetDefaultLocation(ctx context.Context, in *GetDefaultLocationRequest, opts ...grpc.CallOption) (*GetDefaultLocationResponse, error)
}

type atlasClient struct {
	cc grpc.ClientConnInterface
}

func NewAtlasClient(cc grpc.ClientConnInterface) AtlasClient {
	return &atlasClient{cc}
}

func (c *atlasClient) GetData(ctx context.Context, in *GetDataRequest, opts ...grpc.CallOption) (*GetDataResponse, error) {
	out := new(GetDataResponse)
	err := c.cc.Invoke(ctx, "/atlas.v1.Atlas/GetData", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *atlasClient) GetNavigationData(ctx context.Context, in *GetNavigationDataRequest, opts ...grpc.CallOption) (*GetNavigationDataResponse, error) {
	out := new(GetNavigationDataResponse)
	err := c.cc.Invoke(ctx, "/atlas.v1.Atlas/GetNavigationData", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *atlasClient) GetSalesInfo(ctx context.Context, in *GetSalesInfoRequest, opts ...grpc.CallOption) (*GetSalesInfoResponse, error) {
	out := new(GetSalesInfoResponse)
	err := c.cc.Invoke(ctx, "/atlas.v1.Atlas/GetSalesInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *atlasClient) GetLocations(ctx context.Context, in *GetLocationsRequest, opts ...grpc.CallOption) (*GetLocationsResponse, error) {
	out := new(GetLocationsResponse)
	err := c.cc.Invoke(ctx, "/atlas.v1.Atlas/GetLocations", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *atlasClient) ListElevatedLocations(ctx context.Context, in *ListElevatedLocationsRequest, opts ...grpc.CallOption) (*ListElevatedLocationsResponse, error) {
	out := new(ListElevatedLocationsResponse)
	err := c.cc.Invoke(ctx, "/atlas.v1.Atlas/ListElevatedLocations", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *atlasClient) ListLocations(ctx context.Context, in *ListLocationsRequest, opts ...grpc.CallOption) (*ListLocationsResponse, error) {
	out := new(ListLocationsResponse)
	err := c.cc.Invoke(ctx, "/atlas.v1.Atlas/ListLocations", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *atlasClient) ContactUs(ctx context.Context, in *ContactUsRequest, opts ...grpc.CallOption) (*empty.Empty, error) {
	out := new(empty.Empty)
	err := c.cc.Invoke(ctx, "/atlas.v1.Atlas/ContactUs", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *atlasClient) SetDefaultLocation(ctx context.Context, in *SetDefaultLocationRequest, opts ...grpc.CallOption) (*empty.Empty, error) {
	out := new(empty.Empty)
	err := c.cc.Invoke(ctx, "/atlas.v1.Atlas/SetDefaultLocation", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *atlasClient) GetDefaultLocation(ctx context.Context, in *GetDefaultLocationRequest, opts ...grpc.CallOption) (*GetDefaultLocationResponse, error) {
	out := new(GetDefaultLocationResponse)
	err := c.cc.Invoke(ctx, "/atlas.v1.Atlas/GetDefaultLocation", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// AtlasServer is the server API for Atlas service.
type AtlasServer interface {
	// GetData gets the data that populates the Atlas navbar
	GetData(context.Context, *GetDataRequest) (*GetDataResponse, error)
	// GetNavigationData gets the navigation data that populates the sidenav
	GetNavigationData(context.Context, *GetNavigationDataRequest) (*GetNavigationDataResponse, error)
	// GetSalesInfo gets the sales info that populates the sidenav
	GetSalesInfo(context.Context, *GetSalesInfoRequest) (*GetSalesInfoResponse, error)
	// Deprecated (use ListLocations): GetLocations returns associated account groups or groups
	GetLocations(context.Context, *GetLocationsRequest) (*GetLocationsResponse, error)
	// Deprecated (use ListLocations): ListElevatedLocations returns a paged list of locations available to an elevated user.
	ListElevatedLocations(context.Context, *ListElevatedLocationsRequest) (*ListElevatedLocationsResponse, error)
	// ListLocations returns a paged list of locations available to a user.
	ListLocations(context.Context, *ListLocationsRequest) (*ListLocationsResponse, error)
	// ContactUs notifies the platform that the user is interested in contacting the partner
	ContactUs(context.Context, *ContactUsRequest) (*empty.Empty, error)
	// SetDefaultLocationRequest sets a user's default location for the given partner.
	SetDefaultLocation(context.Context, *SetDefaultLocationRequest) (*empty.Empty, error)
	// GetDefaultLocation` gets a user's default location that they have set for the given partner.`
	GetDefaultLocation(context.Context, *GetDefaultLocationRequest) (*GetDefaultLocationResponse, error)
}

// UnimplementedAtlasServer can be embedded to have forward compatible implementations.
type UnimplementedAtlasServer struct {
}

func (*UnimplementedAtlasServer) GetData(context.Context, *GetDataRequest) (*GetDataResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetData not implemented")
}
func (*UnimplementedAtlasServer) GetNavigationData(context.Context, *GetNavigationDataRequest) (*GetNavigationDataResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetNavigationData not implemented")
}
func (*UnimplementedAtlasServer) GetSalesInfo(context.Context, *GetSalesInfoRequest) (*GetSalesInfoResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetSalesInfo not implemented")
}
func (*UnimplementedAtlasServer) GetLocations(context.Context, *GetLocationsRequest) (*GetLocationsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetLocations not implemented")
}
func (*UnimplementedAtlasServer) ListElevatedLocations(context.Context, *ListElevatedLocationsRequest) (*ListElevatedLocationsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListElevatedLocations not implemented")
}
func (*UnimplementedAtlasServer) ListLocations(context.Context, *ListLocationsRequest) (*ListLocationsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListLocations not implemented")
}
func (*UnimplementedAtlasServer) ContactUs(context.Context, *ContactUsRequest) (*empty.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ContactUs not implemented")
}
func (*UnimplementedAtlasServer) SetDefaultLocation(context.Context, *SetDefaultLocationRequest) (*empty.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SetDefaultLocation not implemented")
}
func (*UnimplementedAtlasServer) GetDefaultLocation(context.Context, *GetDefaultLocationRequest) (*GetDefaultLocationResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetDefaultLocation not implemented")
}

func RegisterAtlasServer(s *grpc.Server, srv AtlasServer) {
	s.RegisterService(&_Atlas_serviceDesc, srv)
}

func _Atlas_GetData_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetDataRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AtlasServer).GetData(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/atlas.v1.Atlas/GetData",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AtlasServer).GetData(ctx, req.(*GetDataRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Atlas_GetNavigationData_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetNavigationDataRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AtlasServer).GetNavigationData(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/atlas.v1.Atlas/GetNavigationData",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AtlasServer).GetNavigationData(ctx, req.(*GetNavigationDataRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Atlas_GetSalesInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetSalesInfoRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AtlasServer).GetSalesInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/atlas.v1.Atlas/GetSalesInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AtlasServer).GetSalesInfo(ctx, req.(*GetSalesInfoRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Atlas_GetLocations_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetLocationsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AtlasServer).GetLocations(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/atlas.v1.Atlas/GetLocations",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AtlasServer).GetLocations(ctx, req.(*GetLocationsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Atlas_ListElevatedLocations_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListElevatedLocationsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AtlasServer).ListElevatedLocations(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/atlas.v1.Atlas/ListElevatedLocations",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AtlasServer).ListElevatedLocations(ctx, req.(*ListElevatedLocationsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Atlas_ListLocations_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListLocationsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AtlasServer).ListLocations(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/atlas.v1.Atlas/ListLocations",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AtlasServer).ListLocations(ctx, req.(*ListLocationsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Atlas_ContactUs_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ContactUsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AtlasServer).ContactUs(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/atlas.v1.Atlas/ContactUs",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AtlasServer).ContactUs(ctx, req.(*ContactUsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Atlas_SetDefaultLocation_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetDefaultLocationRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AtlasServer).SetDefaultLocation(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/atlas.v1.Atlas/SetDefaultLocation",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AtlasServer).SetDefaultLocation(ctx, req.(*SetDefaultLocationRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Atlas_GetDefaultLocation_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetDefaultLocationRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AtlasServer).GetDefaultLocation(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/atlas.v1.Atlas/GetDefaultLocation",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AtlasServer).GetDefaultLocation(ctx, req.(*GetDefaultLocationRequest))
	}
	return interceptor(ctx, in, info, handler)
}

var _Atlas_serviceDesc = grpc.ServiceDesc{
	ServiceName: "atlas.v1.Atlas",
	HandlerType: (*AtlasServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetData",
			Handler:    _Atlas_GetData_Handler,
		},
		{
			MethodName: "GetNavigationData",
			Handler:    _Atlas_GetNavigationData_Handler,
		},
		{
			MethodName: "GetSalesInfo",
			Handler:    _Atlas_GetSalesInfo_Handler,
		},
		{
			MethodName: "GetLocations",
			Handler:    _Atlas_GetLocations_Handler,
		},
		{
			MethodName: "ListElevatedLocations",
			Handler:    _Atlas_ListElevatedLocations_Handler,
		},
		{
			MethodName: "ListLocations",
			Handler:    _Atlas_ListLocations_Handler,
		},
		{
			MethodName: "ContactUs",
			Handler:    _Atlas_ContactUs_Handler,
		},
		{
			MethodName: "SetDefaultLocation",
			Handler:    _Atlas_SetDefaultLocation_Handler,
		},
		{
			MethodName: "GetDefaultLocation",
			Handler:    _Atlas_GetDefaultLocation_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "atlas/v1/api.proto",
}

// PinsClient is the client API for Pins service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type PinsClient interface {
	// SetPins will set the pins for a user in an business or brand
	SetPins(ctx context.Context, in *SetPinsRequest, opts ...grpc.CallOption) (*empty.Empty, error)
	// GetPins will return the pins for a business or brand
	GetPins(ctx context.Context, in *GetPinsRequest, opts ...grpc.CallOption) (*GetPinsResponse, error)
}

type pinsClient struct {
	cc grpc.ClientConnInterface
}

func NewPinsClient(cc grpc.ClientConnInterface) PinsClient {
	return &pinsClient{cc}
}

func (c *pinsClient) SetPins(ctx context.Context, in *SetPinsRequest, opts ...grpc.CallOption) (*empty.Empty, error) {
	out := new(empty.Empty)
	err := c.cc.Invoke(ctx, "/atlas.v1.Pins/SetPins", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *pinsClient) GetPins(ctx context.Context, in *GetPinsRequest, opts ...grpc.CallOption) (*GetPinsResponse, error) {
	out := new(GetPinsResponse)
	err := c.cc.Invoke(ctx, "/atlas.v1.Pins/GetPins", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// PinsServer is the server API for Pins service.
type PinsServer interface {
	// SetPins will set the pins for a user in an business or brand
	SetPins(context.Context, *SetPinsRequest) (*empty.Empty, error)
	// GetPins will return the pins for a business or brand
	GetPins(context.Context, *GetPinsRequest) (*GetPinsResponse, error)
}

// UnimplementedPinsServer can be embedded to have forward compatible implementations.
type UnimplementedPinsServer struct {
}

func (*UnimplementedPinsServer) SetPins(context.Context, *SetPinsRequest) (*empty.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SetPins not implemented")
}
func (*UnimplementedPinsServer) GetPins(context.Context, *GetPinsRequest) (*GetPinsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetPins not implemented")
}

func RegisterPinsServer(s *grpc.Server, srv PinsServer) {
	s.RegisterService(&_Pins_serviceDesc, srv)
}

func _Pins_SetPins_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetPinsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PinsServer).SetPins(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/atlas.v1.Pins/SetPins",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PinsServer).SetPins(ctx, req.(*SetPinsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Pins_GetPins_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetPinsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PinsServer).GetPins(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/atlas.v1.Pins/GetPins",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PinsServer).GetPins(ctx, req.(*GetPinsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

var _Pins_serviceDesc = grpc.ServiceDesc{
	ServiceName: "atlas.v1.Pins",
	HandlerType: (*PinsServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "SetPins",
			Handler:    _Pins_SetPins_Handler,
		},
		{
			MethodName: "GetPins",
			Handler:    _Pins_GetPins_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "atlas/v1/api.proto",
}

// LanguagesClient is the client API for Languages service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type LanguagesClient interface {
	// SetLanguage will remember your currently selected language
	SetLanguage(ctx context.Context, in *SetLanguageRequest, opts ...grpc.CallOption) (*empty.Empty, error)
	// GetLanguage will return your currently selected language
	GetLanguage(ctx context.Context, in *GetLanguageRequest, opts ...grpc.CallOption) (*GetLanguageResponse, error)
}

type languagesClient struct {
	cc grpc.ClientConnInterface
}

func NewLanguagesClient(cc grpc.ClientConnInterface) LanguagesClient {
	return &languagesClient{cc}
}

func (c *languagesClient) SetLanguage(ctx context.Context, in *SetLanguageRequest, opts ...grpc.CallOption) (*empty.Empty, error) {
	out := new(empty.Empty)
	err := c.cc.Invoke(ctx, "/atlas.v1.Languages/SetLanguage", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *languagesClient) GetLanguage(ctx context.Context, in *GetLanguageRequest, opts ...grpc.CallOption) (*GetLanguageResponse, error) {
	out := new(GetLanguageResponse)
	err := c.cc.Invoke(ctx, "/atlas.v1.Languages/GetLanguage", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// LanguagesServer is the server API for Languages service.
type LanguagesServer interface {
	// SetLanguage will remember your currently selected language
	SetLanguage(context.Context, *SetLanguageRequest) (*empty.Empty, error)
	// GetLanguage will return your currently selected language
	GetLanguage(context.Context, *GetLanguageRequest) (*GetLanguageResponse, error)
}

// UnimplementedLanguagesServer can be embedded to have forward compatible implementations.
type UnimplementedLanguagesServer struct {
}

func (*UnimplementedLanguagesServer) SetLanguage(context.Context, *SetLanguageRequest) (*empty.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SetLanguage not implemented")
}
func (*UnimplementedLanguagesServer) GetLanguage(context.Context, *GetLanguageRequest) (*GetLanguageResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetLanguage not implemented")
}

func RegisterLanguagesServer(s *grpc.Server, srv LanguagesServer) {
	s.RegisterService(&_Languages_serviceDesc, srv)
}

func _Languages_SetLanguage_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetLanguageRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LanguagesServer).SetLanguage(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/atlas.v1.Languages/SetLanguage",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LanguagesServer).SetLanguage(ctx, req.(*SetLanguageRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Languages_GetLanguage_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetLanguageRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LanguagesServer).GetLanguage(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/atlas.v1.Languages/GetLanguage",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LanguagesServer).GetLanguage(ctx, req.(*GetLanguageRequest))
	}
	return interceptor(ctx, in, info, handler)
}

var _Languages_serviceDesc = grpc.ServiceDesc{
	ServiceName: "atlas.v1.Languages",
	HandlerType: (*LanguagesServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "SetLanguage",
			Handler:    _Languages_SetLanguage_Handler,
		},
		{
			MethodName: "GetLanguage",
			Handler:    _Languages_GetLanguage_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "atlas/v1/api.proto",
}
