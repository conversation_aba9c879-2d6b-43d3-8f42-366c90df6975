{"schema": "1.0", "comment": "This file maps proto services/RPCs to the corresponding library clients/methods.", "language": "go", "protoPackage": "google.longrunning", "libraryPackage": "cloud.google.com/go/longrunning/autogen", "services": {"Operations": {"clients": {"grpc": {"libraryClient": "OperationsClient", "rpcs": {"CancelOperation": {"methods": ["CancelOperation"]}, "DeleteOperation": {"methods": ["DeleteOperation"]}, "GetOperation": {"methods": ["GetOperation"]}, "ListOperations": {"methods": ["ListOperations"]}, "WaitOperation": {"methods": ["WaitOperation"]}}}, "rest": {"libraryClient": "OperationsClient", "rpcs": {"CancelOperation": {"methods": ["CancelOperation"]}, "DeleteOperation": {"methods": ["DeleteOperation"]}, "GetOperation": {"methods": ["GetOperation"]}, "ListOperations": {"methods": ["ListOperations"]}, "WaitOperation": {"methods": ["WaitOperation"]}}}}}}}