// Copyright 2023 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.30.0
// 	protoc        v4.23.2
// source: google/pubsub/v1/schema.proto

package pubsubpb

import (
	context "context"
	reflect "reflect"
	sync "sync"

	_ "google.golang.org/genproto/googleapis/api/annotations"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// View of Schema object fields to be returned by GetSchema and ListSchemas.
type SchemaView int32

const (
	// The default / unset value.
	// The API will default to the BASIC view.
	SchemaView_SCHEMA_VIEW_UNSPECIFIED SchemaView = 0
	// Include the name and type of the schema, but not the definition.
	SchemaView_BASIC SchemaView = 1
	// Include all Schema object fields.
	SchemaView_FULL SchemaView = 2
)

// Enum value maps for SchemaView.
var (
	SchemaView_name = map[int32]string{
		0: "SCHEMA_VIEW_UNSPECIFIED",
		1: "BASIC",
		2: "FULL",
	}
	SchemaView_value = map[string]int32{
		"SCHEMA_VIEW_UNSPECIFIED": 0,
		"BASIC":                   1,
		"FULL":                    2,
	}
)

func (x SchemaView) Enum() *SchemaView {
	p := new(SchemaView)
	*p = x
	return p
}

func (x SchemaView) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (SchemaView) Descriptor() protoreflect.EnumDescriptor {
	return file_google_pubsub_v1_schema_proto_enumTypes[0].Descriptor()
}

func (SchemaView) Type() protoreflect.EnumType {
	return &file_google_pubsub_v1_schema_proto_enumTypes[0]
}

func (x SchemaView) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use SchemaView.Descriptor instead.
func (SchemaView) EnumDescriptor() ([]byte, []int) {
	return file_google_pubsub_v1_schema_proto_rawDescGZIP(), []int{0}
}

// Possible encoding types for messages.
type Encoding int32

const (
	// Unspecified
	Encoding_ENCODING_UNSPECIFIED Encoding = 0
	// JSON encoding
	Encoding_JSON Encoding = 1
	// Binary encoding, as defined by the schema type. For some schema types,
	// binary encoding may not be available.
	Encoding_BINARY Encoding = 2
)

// Enum value maps for Encoding.
var (
	Encoding_name = map[int32]string{
		0: "ENCODING_UNSPECIFIED",
		1: "JSON",
		2: "BINARY",
	}
	Encoding_value = map[string]int32{
		"ENCODING_UNSPECIFIED": 0,
		"JSON":                 1,
		"BINARY":               2,
	}
)

func (x Encoding) Enum() *Encoding {
	p := new(Encoding)
	*p = x
	return p
}

func (x Encoding) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (Encoding) Descriptor() protoreflect.EnumDescriptor {
	return file_google_pubsub_v1_schema_proto_enumTypes[1].Descriptor()
}

func (Encoding) Type() protoreflect.EnumType {
	return &file_google_pubsub_v1_schema_proto_enumTypes[1]
}

func (x Encoding) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use Encoding.Descriptor instead.
func (Encoding) EnumDescriptor() ([]byte, []int) {
	return file_google_pubsub_v1_schema_proto_rawDescGZIP(), []int{1}
}

// Possible schema definition types.
type Schema_Type int32

const (
	// Default value. This value is unused.
	Schema_TYPE_UNSPECIFIED Schema_Type = 0
	// A Protocol Buffer schema definition.
	Schema_PROTOCOL_BUFFER Schema_Type = 1
	// An Avro schema definition.
	Schema_AVRO Schema_Type = 2
)

// Enum value maps for Schema_Type.
var (
	Schema_Type_name = map[int32]string{
		0: "TYPE_UNSPECIFIED",
		1: "PROTOCOL_BUFFER",
		2: "AVRO",
	}
	Schema_Type_value = map[string]int32{
		"TYPE_UNSPECIFIED": 0,
		"PROTOCOL_BUFFER":  1,
		"AVRO":             2,
	}
)

func (x Schema_Type) Enum() *Schema_Type {
	p := new(Schema_Type)
	*p = x
	return p
}

func (x Schema_Type) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (Schema_Type) Descriptor() protoreflect.EnumDescriptor {
	return file_google_pubsub_v1_schema_proto_enumTypes[2].Descriptor()
}

func (Schema_Type) Type() protoreflect.EnumType {
	return &file_google_pubsub_v1_schema_proto_enumTypes[2]
}

func (x Schema_Type) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use Schema_Type.Descriptor instead.
func (Schema_Type) EnumDescriptor() ([]byte, []int) {
	return file_google_pubsub_v1_schema_proto_rawDescGZIP(), []int{0, 0}
}

// A schema resource.
type Schema struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Required. Name of the schema.
	// Format is `projects/{project}/schemas/{schema}`.
	Name string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	// The type of the schema definition.
	Type Schema_Type `protobuf:"varint,2,opt,name=type,proto3,enum=google.pubsub.v1.Schema_Type" json:"type,omitempty"`
	// The definition of the schema. This should contain a string representing
	// the full definition of the schema that is a valid schema definition of
	// the type specified in `type`.
	Definition string `protobuf:"bytes,3,opt,name=definition,proto3" json:"definition,omitempty"`
	// Output only. Immutable. The revision ID of the schema.
	RevisionId string `protobuf:"bytes,4,opt,name=revision_id,json=revisionId,proto3" json:"revision_id,omitempty"`
	// Output only. The timestamp that the revision was created.
	RevisionCreateTime *timestamppb.Timestamp `protobuf:"bytes,6,opt,name=revision_create_time,json=revisionCreateTime,proto3" json:"revision_create_time,omitempty"`
}

func (x *Schema) Reset() {
	*x = Schema{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_pubsub_v1_schema_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Schema) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Schema) ProtoMessage() {}

func (x *Schema) ProtoReflect() protoreflect.Message {
	mi := &file_google_pubsub_v1_schema_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Schema.ProtoReflect.Descriptor instead.
func (*Schema) Descriptor() ([]byte, []int) {
	return file_google_pubsub_v1_schema_proto_rawDescGZIP(), []int{0}
}

func (x *Schema) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *Schema) GetType() Schema_Type {
	if x != nil {
		return x.Type
	}
	return Schema_TYPE_UNSPECIFIED
}

func (x *Schema) GetDefinition() string {
	if x != nil {
		return x.Definition
	}
	return ""
}

func (x *Schema) GetRevisionId() string {
	if x != nil {
		return x.RevisionId
	}
	return ""
}

func (x *Schema) GetRevisionCreateTime() *timestamppb.Timestamp {
	if x != nil {
		return x.RevisionCreateTime
	}
	return nil
}

// Request for the CreateSchema method.
type CreateSchemaRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Required. The name of the project in which to create the schema.
	// Format is `projects/{project-id}`.
	Parent string `protobuf:"bytes,1,opt,name=parent,proto3" json:"parent,omitempty"`
	// Required. The schema object to create.
	//
	// This schema's `name` parameter is ignored. The schema object returned
	// by CreateSchema will have a `name` made using the given `parent` and
	// `schema_id`.
	Schema *Schema `protobuf:"bytes,2,opt,name=schema,proto3" json:"schema,omitempty"`
	// The ID to use for the schema, which will become the final component of
	// the schema's resource name.
	//
	// See https://cloud.google.com/pubsub/docs/admin#resource_names for resource
	// name constraints.
	SchemaId string `protobuf:"bytes,3,opt,name=schema_id,json=schemaId,proto3" json:"schema_id,omitempty"`
}

func (x *CreateSchemaRequest) Reset() {
	*x = CreateSchemaRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_pubsub_v1_schema_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateSchemaRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateSchemaRequest) ProtoMessage() {}

func (x *CreateSchemaRequest) ProtoReflect() protoreflect.Message {
	mi := &file_google_pubsub_v1_schema_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateSchemaRequest.ProtoReflect.Descriptor instead.
func (*CreateSchemaRequest) Descriptor() ([]byte, []int) {
	return file_google_pubsub_v1_schema_proto_rawDescGZIP(), []int{1}
}

func (x *CreateSchemaRequest) GetParent() string {
	if x != nil {
		return x.Parent
	}
	return ""
}

func (x *CreateSchemaRequest) GetSchema() *Schema {
	if x != nil {
		return x.Schema
	}
	return nil
}

func (x *CreateSchemaRequest) GetSchemaId() string {
	if x != nil {
		return x.SchemaId
	}
	return ""
}

// Request for the GetSchema method.
type GetSchemaRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Required. The name of the schema to get.
	// Format is `projects/{project}/schemas/{schema}`.
	Name string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	// The set of fields to return in the response. If not set, returns a Schema
	// with all fields filled out. Set to `BASIC` to omit the `definition`.
	View SchemaView `protobuf:"varint,2,opt,name=view,proto3,enum=google.pubsub.v1.SchemaView" json:"view,omitempty"`
}

func (x *GetSchemaRequest) Reset() {
	*x = GetSchemaRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_pubsub_v1_schema_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetSchemaRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetSchemaRequest) ProtoMessage() {}

func (x *GetSchemaRequest) ProtoReflect() protoreflect.Message {
	mi := &file_google_pubsub_v1_schema_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetSchemaRequest.ProtoReflect.Descriptor instead.
func (*GetSchemaRequest) Descriptor() ([]byte, []int) {
	return file_google_pubsub_v1_schema_proto_rawDescGZIP(), []int{2}
}

func (x *GetSchemaRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *GetSchemaRequest) GetView() SchemaView {
	if x != nil {
		return x.View
	}
	return SchemaView_SCHEMA_VIEW_UNSPECIFIED
}

// Request for the `ListSchemas` method.
type ListSchemasRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Required. The name of the project in which to list schemas.
	// Format is `projects/{project-id}`.
	Parent string `protobuf:"bytes,1,opt,name=parent,proto3" json:"parent,omitempty"`
	// The set of Schema fields to return in the response. If not set, returns
	// Schemas with `name` and `type`, but not `definition`. Set to `FULL` to
	// retrieve all fields.
	View SchemaView `protobuf:"varint,2,opt,name=view,proto3,enum=google.pubsub.v1.SchemaView" json:"view,omitempty"`
	// Maximum number of schemas to return.
	PageSize int32 `protobuf:"varint,3,opt,name=page_size,json=pageSize,proto3" json:"page_size,omitempty"`
	// The value returned by the last `ListSchemasResponse`; indicates that
	// this is a continuation of a prior `ListSchemas` call, and that the
	// system should return the next page of data.
	PageToken string `protobuf:"bytes,4,opt,name=page_token,json=pageToken,proto3" json:"page_token,omitempty"`
}

func (x *ListSchemasRequest) Reset() {
	*x = ListSchemasRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_pubsub_v1_schema_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListSchemasRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListSchemasRequest) ProtoMessage() {}

func (x *ListSchemasRequest) ProtoReflect() protoreflect.Message {
	mi := &file_google_pubsub_v1_schema_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListSchemasRequest.ProtoReflect.Descriptor instead.
func (*ListSchemasRequest) Descriptor() ([]byte, []int) {
	return file_google_pubsub_v1_schema_proto_rawDescGZIP(), []int{3}
}

func (x *ListSchemasRequest) GetParent() string {
	if x != nil {
		return x.Parent
	}
	return ""
}

func (x *ListSchemasRequest) GetView() SchemaView {
	if x != nil {
		return x.View
	}
	return SchemaView_SCHEMA_VIEW_UNSPECIFIED
}

func (x *ListSchemasRequest) GetPageSize() int32 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

func (x *ListSchemasRequest) GetPageToken() string {
	if x != nil {
		return x.PageToken
	}
	return ""
}

// Response for the `ListSchemas` method.
type ListSchemasResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The resulting schemas.
	Schemas []*Schema `protobuf:"bytes,1,rep,name=schemas,proto3" json:"schemas,omitempty"`
	// If not empty, indicates that there may be more schemas that match the
	// request; this value should be passed in a new `ListSchemasRequest`.
	NextPageToken string `protobuf:"bytes,2,opt,name=next_page_token,json=nextPageToken,proto3" json:"next_page_token,omitempty"`
}

func (x *ListSchemasResponse) Reset() {
	*x = ListSchemasResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_pubsub_v1_schema_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListSchemasResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListSchemasResponse) ProtoMessage() {}

func (x *ListSchemasResponse) ProtoReflect() protoreflect.Message {
	mi := &file_google_pubsub_v1_schema_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListSchemasResponse.ProtoReflect.Descriptor instead.
func (*ListSchemasResponse) Descriptor() ([]byte, []int) {
	return file_google_pubsub_v1_schema_proto_rawDescGZIP(), []int{4}
}

func (x *ListSchemasResponse) GetSchemas() []*Schema {
	if x != nil {
		return x.Schemas
	}
	return nil
}

func (x *ListSchemasResponse) GetNextPageToken() string {
	if x != nil {
		return x.NextPageToken
	}
	return ""
}

// Request for the `ListSchemaRevisions` method.
type ListSchemaRevisionsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Required. The name of the schema to list revisions for.
	Name string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	// The set of Schema fields to return in the response. If not set, returns
	// Schemas with `name` and `type`, but not `definition`. Set to `FULL` to
	// retrieve all fields.
	View SchemaView `protobuf:"varint,2,opt,name=view,proto3,enum=google.pubsub.v1.SchemaView" json:"view,omitempty"`
	// The maximum number of revisions to return per page.
	PageSize int32 `protobuf:"varint,3,opt,name=page_size,json=pageSize,proto3" json:"page_size,omitempty"`
	// The page token, received from a previous ListSchemaRevisions call.
	// Provide this to retrieve the subsequent page.
	PageToken string `protobuf:"bytes,4,opt,name=page_token,json=pageToken,proto3" json:"page_token,omitempty"`
}

func (x *ListSchemaRevisionsRequest) Reset() {
	*x = ListSchemaRevisionsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_pubsub_v1_schema_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListSchemaRevisionsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListSchemaRevisionsRequest) ProtoMessage() {}

func (x *ListSchemaRevisionsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_google_pubsub_v1_schema_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListSchemaRevisionsRequest.ProtoReflect.Descriptor instead.
func (*ListSchemaRevisionsRequest) Descriptor() ([]byte, []int) {
	return file_google_pubsub_v1_schema_proto_rawDescGZIP(), []int{5}
}

func (x *ListSchemaRevisionsRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *ListSchemaRevisionsRequest) GetView() SchemaView {
	if x != nil {
		return x.View
	}
	return SchemaView_SCHEMA_VIEW_UNSPECIFIED
}

func (x *ListSchemaRevisionsRequest) GetPageSize() int32 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

func (x *ListSchemaRevisionsRequest) GetPageToken() string {
	if x != nil {
		return x.PageToken
	}
	return ""
}

// Response for the `ListSchemaRevisions` method.
type ListSchemaRevisionsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The revisions of the schema.
	Schemas []*Schema `protobuf:"bytes,1,rep,name=schemas,proto3" json:"schemas,omitempty"`
	// A token that can be sent as `page_token` to retrieve the next page.
	// If this field is empty, there are no subsequent pages.
	NextPageToken string `protobuf:"bytes,2,opt,name=next_page_token,json=nextPageToken,proto3" json:"next_page_token,omitempty"`
}

func (x *ListSchemaRevisionsResponse) Reset() {
	*x = ListSchemaRevisionsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_pubsub_v1_schema_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListSchemaRevisionsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListSchemaRevisionsResponse) ProtoMessage() {}

func (x *ListSchemaRevisionsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_google_pubsub_v1_schema_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListSchemaRevisionsResponse.ProtoReflect.Descriptor instead.
func (*ListSchemaRevisionsResponse) Descriptor() ([]byte, []int) {
	return file_google_pubsub_v1_schema_proto_rawDescGZIP(), []int{6}
}

func (x *ListSchemaRevisionsResponse) GetSchemas() []*Schema {
	if x != nil {
		return x.Schemas
	}
	return nil
}

func (x *ListSchemaRevisionsResponse) GetNextPageToken() string {
	if x != nil {
		return x.NextPageToken
	}
	return ""
}

// Request for CommitSchema method.
type CommitSchemaRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Required. The name of the schema we are revising.
	// Format is `projects/{project}/schemas/{schema}`.
	Name string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	// Required. The schema revision to commit.
	Schema *Schema `protobuf:"bytes,2,opt,name=schema,proto3" json:"schema,omitempty"`
}

func (x *CommitSchemaRequest) Reset() {
	*x = CommitSchemaRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_pubsub_v1_schema_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CommitSchemaRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CommitSchemaRequest) ProtoMessage() {}

func (x *CommitSchemaRequest) ProtoReflect() protoreflect.Message {
	mi := &file_google_pubsub_v1_schema_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CommitSchemaRequest.ProtoReflect.Descriptor instead.
func (*CommitSchemaRequest) Descriptor() ([]byte, []int) {
	return file_google_pubsub_v1_schema_proto_rawDescGZIP(), []int{7}
}

func (x *CommitSchemaRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *CommitSchemaRequest) GetSchema() *Schema {
	if x != nil {
		return x.Schema
	}
	return nil
}

// Request for the `RollbackSchema` method.
type RollbackSchemaRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Required. The schema being rolled back with revision id.
	Name string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	// Required. The revision ID to roll back to.
	// It must be a revision of the same schema.
	//
	//   Example: c7cfa2a8
	RevisionId string `protobuf:"bytes,2,opt,name=revision_id,json=revisionId,proto3" json:"revision_id,omitempty"`
}

func (x *RollbackSchemaRequest) Reset() {
	*x = RollbackSchemaRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_pubsub_v1_schema_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RollbackSchemaRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RollbackSchemaRequest) ProtoMessage() {}

func (x *RollbackSchemaRequest) ProtoReflect() protoreflect.Message {
	mi := &file_google_pubsub_v1_schema_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RollbackSchemaRequest.ProtoReflect.Descriptor instead.
func (*RollbackSchemaRequest) Descriptor() ([]byte, []int) {
	return file_google_pubsub_v1_schema_proto_rawDescGZIP(), []int{8}
}

func (x *RollbackSchemaRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *RollbackSchemaRequest) GetRevisionId() string {
	if x != nil {
		return x.RevisionId
	}
	return ""
}

// Request for the `DeleteSchemaRevision` method.
type DeleteSchemaRevisionRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Required. The name of the schema revision to be deleted, with a revision ID
	// explicitly included.
	//
	// Example: `projects/123/schemas/my-schema@c7cfa2a8`
	Name string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	// Optional. This field is deprecated and should not be used for specifying
	// the revision ID. The revision ID should be specified via the `name`
	// parameter.
	//
	// Deprecated: Marked as deprecated in google/pubsub/v1/schema.proto.
	RevisionId string `protobuf:"bytes,2,opt,name=revision_id,json=revisionId,proto3" json:"revision_id,omitempty"`
}

func (x *DeleteSchemaRevisionRequest) Reset() {
	*x = DeleteSchemaRevisionRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_pubsub_v1_schema_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeleteSchemaRevisionRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteSchemaRevisionRequest) ProtoMessage() {}

func (x *DeleteSchemaRevisionRequest) ProtoReflect() protoreflect.Message {
	mi := &file_google_pubsub_v1_schema_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteSchemaRevisionRequest.ProtoReflect.Descriptor instead.
func (*DeleteSchemaRevisionRequest) Descriptor() ([]byte, []int) {
	return file_google_pubsub_v1_schema_proto_rawDescGZIP(), []int{9}
}

func (x *DeleteSchemaRevisionRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

// Deprecated: Marked as deprecated in google/pubsub/v1/schema.proto.
func (x *DeleteSchemaRevisionRequest) GetRevisionId() string {
	if x != nil {
		return x.RevisionId
	}
	return ""
}

// Request for the `DeleteSchema` method.
type DeleteSchemaRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Required. Name of the schema to delete.
	// Format is `projects/{project}/schemas/{schema}`.
	Name string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
}

func (x *DeleteSchemaRequest) Reset() {
	*x = DeleteSchemaRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_pubsub_v1_schema_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeleteSchemaRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteSchemaRequest) ProtoMessage() {}

func (x *DeleteSchemaRequest) ProtoReflect() protoreflect.Message {
	mi := &file_google_pubsub_v1_schema_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteSchemaRequest.ProtoReflect.Descriptor instead.
func (*DeleteSchemaRequest) Descriptor() ([]byte, []int) {
	return file_google_pubsub_v1_schema_proto_rawDescGZIP(), []int{10}
}

func (x *DeleteSchemaRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

// Request for the `ValidateSchema` method.
type ValidateSchemaRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Required. The name of the project in which to validate schemas.
	// Format is `projects/{project-id}`.
	Parent string `protobuf:"bytes,1,opt,name=parent,proto3" json:"parent,omitempty"`
	// Required. The schema object to validate.
	Schema *Schema `protobuf:"bytes,2,opt,name=schema,proto3" json:"schema,omitempty"`
}

func (x *ValidateSchemaRequest) Reset() {
	*x = ValidateSchemaRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_pubsub_v1_schema_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ValidateSchemaRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ValidateSchemaRequest) ProtoMessage() {}

func (x *ValidateSchemaRequest) ProtoReflect() protoreflect.Message {
	mi := &file_google_pubsub_v1_schema_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ValidateSchemaRequest.ProtoReflect.Descriptor instead.
func (*ValidateSchemaRequest) Descriptor() ([]byte, []int) {
	return file_google_pubsub_v1_schema_proto_rawDescGZIP(), []int{11}
}

func (x *ValidateSchemaRequest) GetParent() string {
	if x != nil {
		return x.Parent
	}
	return ""
}

func (x *ValidateSchemaRequest) GetSchema() *Schema {
	if x != nil {
		return x.Schema
	}
	return nil
}

// Response for the `ValidateSchema` method.
// Empty for now.
type ValidateSchemaResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *ValidateSchemaResponse) Reset() {
	*x = ValidateSchemaResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_pubsub_v1_schema_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ValidateSchemaResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ValidateSchemaResponse) ProtoMessage() {}

func (x *ValidateSchemaResponse) ProtoReflect() protoreflect.Message {
	mi := &file_google_pubsub_v1_schema_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ValidateSchemaResponse.ProtoReflect.Descriptor instead.
func (*ValidateSchemaResponse) Descriptor() ([]byte, []int) {
	return file_google_pubsub_v1_schema_proto_rawDescGZIP(), []int{12}
}

// Request for the `ValidateMessage` method.
type ValidateMessageRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Required. The name of the project in which to validate schemas.
	// Format is `projects/{project-id}`.
	Parent string `protobuf:"bytes,1,opt,name=parent,proto3" json:"parent,omitempty"`
	// Types that are assignable to SchemaSpec:
	//	*ValidateMessageRequest_Name
	//	*ValidateMessageRequest_Schema
	SchemaSpec isValidateMessageRequest_SchemaSpec `protobuf_oneof:"schema_spec"`
	// Message to validate against the provided `schema_spec`.
	Message []byte `protobuf:"bytes,4,opt,name=message,proto3" json:"message,omitempty"`
	// The encoding expected for messages
	Encoding Encoding `protobuf:"varint,5,opt,name=encoding,proto3,enum=google.pubsub.v1.Encoding" json:"encoding,omitempty"`
}

func (x *ValidateMessageRequest) Reset() {
	*x = ValidateMessageRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_pubsub_v1_schema_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ValidateMessageRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ValidateMessageRequest) ProtoMessage() {}

func (x *ValidateMessageRequest) ProtoReflect() protoreflect.Message {
	mi := &file_google_pubsub_v1_schema_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ValidateMessageRequest.ProtoReflect.Descriptor instead.
func (*ValidateMessageRequest) Descriptor() ([]byte, []int) {
	return file_google_pubsub_v1_schema_proto_rawDescGZIP(), []int{13}
}

func (x *ValidateMessageRequest) GetParent() string {
	if x != nil {
		return x.Parent
	}
	return ""
}

func (m *ValidateMessageRequest) GetSchemaSpec() isValidateMessageRequest_SchemaSpec {
	if m != nil {
		return m.SchemaSpec
	}
	return nil
}

func (x *ValidateMessageRequest) GetName() string {
	if x, ok := x.GetSchemaSpec().(*ValidateMessageRequest_Name); ok {
		return x.Name
	}
	return ""
}

func (x *ValidateMessageRequest) GetSchema() *Schema {
	if x, ok := x.GetSchemaSpec().(*ValidateMessageRequest_Schema); ok {
		return x.Schema
	}
	return nil
}

func (x *ValidateMessageRequest) GetMessage() []byte {
	if x != nil {
		return x.Message
	}
	return nil
}

func (x *ValidateMessageRequest) GetEncoding() Encoding {
	if x != nil {
		return x.Encoding
	}
	return Encoding_ENCODING_UNSPECIFIED
}

type isValidateMessageRequest_SchemaSpec interface {
	isValidateMessageRequest_SchemaSpec()
}

type ValidateMessageRequest_Name struct {
	// Name of the schema against which to validate.
	//
	// Format is `projects/{project}/schemas/{schema}`.
	Name string `protobuf:"bytes,2,opt,name=name,proto3,oneof"`
}

type ValidateMessageRequest_Schema struct {
	// Ad-hoc schema against which to validate
	Schema *Schema `protobuf:"bytes,3,opt,name=schema,proto3,oneof"`
}

func (*ValidateMessageRequest_Name) isValidateMessageRequest_SchemaSpec() {}

func (*ValidateMessageRequest_Schema) isValidateMessageRequest_SchemaSpec() {}

// Response for the `ValidateMessage` method.
// Empty for now.
type ValidateMessageResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *ValidateMessageResponse) Reset() {
	*x = ValidateMessageResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_pubsub_v1_schema_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ValidateMessageResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ValidateMessageResponse) ProtoMessage() {}

func (x *ValidateMessageResponse) ProtoReflect() protoreflect.Message {
	mi := &file_google_pubsub_v1_schema_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ValidateMessageResponse.ProtoReflect.Descriptor instead.
func (*ValidateMessageResponse) Descriptor() ([]byte, []int) {
	return file_google_pubsub_v1_schema_proto_rawDescGZIP(), []int{14}
}

var File_google_pubsub_v1_schema_proto protoreflect.FileDescriptor

var file_google_pubsub_v1_schema_proto_rawDesc = []byte{
	0x0a, 0x1d, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x75, 0x62, 0x73, 0x75, 0x62, 0x2f,
	0x76, 0x31, 0x2f, 0x73, 0x63, 0x68, 0x65, 0x6d, 0x61, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12,
	0x10, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x75, 0x62, 0x73, 0x75, 0x62, 0x2e, 0x76,
	0x31, 0x1a, 0x1c, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x61, 0x6e,
	0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a,
	0x17, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x63, 0x6c, 0x69, 0x65,
	0x6e, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2f, 0x61, 0x70, 0x69, 0x2f, 0x66, 0x69, 0x65, 0x6c, 0x64, 0x5f, 0x62, 0x65, 0x68, 0x61, 0x76,
	0x69, 0x6f, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x19, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1b, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x65, 0x6d, 0x70, 0x74, 0x79, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x1a, 0x1f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62,
	0x75, 0x66, 0x2f, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x22, 0xf5, 0x02, 0x0a, 0x06, 0x53, 0x63, 0x68, 0x65, 0x6d, 0x61, 0x12, 0x17, 0x0a,
	0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x03, 0xe0, 0x41, 0x02,
	0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x31, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x0e, 0x32, 0x1d, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x75,
	0x62, 0x73, 0x75, 0x62, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x63, 0x68, 0x65, 0x6d, 0x61, 0x2e, 0x54,
	0x79, 0x70, 0x65, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x1e, 0x0a, 0x0a, 0x64, 0x65, 0x66,
	0x69, 0x6e, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x64,
	0x65, 0x66, 0x69, 0x6e, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x27, 0x0a, 0x0b, 0x72, 0x65, 0x76,
	0x69, 0x73, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x42, 0x06,
	0xe0, 0x41, 0x05, 0xe0, 0x41, 0x03, 0x52, 0x0a, 0x72, 0x65, 0x76, 0x69, 0x73, 0x69, 0x6f, 0x6e,
	0x49, 0x64, 0x12, 0x51, 0x0a, 0x14, 0x72, 0x65, 0x76, 0x69, 0x73, 0x69, 0x6f, 0x6e, 0x5f, 0x63,
	0x72, 0x65, 0x61, 0x74, 0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62,
	0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x42, 0x03, 0xe0, 0x41,
	0x03, 0x52, 0x12, 0x72, 0x65, 0x76, 0x69, 0x73, 0x69, 0x6f, 0x6e, 0x43, 0x72, 0x65, 0x61, 0x74,
	0x65, 0x54, 0x69, 0x6d, 0x65, 0x22, 0x3b, 0x0a, 0x04, 0x54, 0x79, 0x70, 0x65, 0x12, 0x14, 0x0a,
	0x10, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45,
	0x44, 0x10, 0x00, 0x12, 0x13, 0x0a, 0x0f, 0x50, 0x52, 0x4f, 0x54, 0x4f, 0x43, 0x4f, 0x4c, 0x5f,
	0x42, 0x55, 0x46, 0x46, 0x45, 0x52, 0x10, 0x01, 0x12, 0x08, 0x0a, 0x04, 0x41, 0x56, 0x52, 0x4f,
	0x10, 0x02, 0x3a, 0x46, 0xea, 0x41, 0x43, 0x0a, 0x1c, 0x70, 0x75, 0x62, 0x73, 0x75, 0x62, 0x2e,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x53,
	0x63, 0x68, 0x65, 0x6d, 0x61, 0x12, 0x23, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x73, 0x2f,
	0x7b, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x7d, 0x2f, 0x73, 0x63, 0x68, 0x65, 0x6d, 0x61,
	0x73, 0x2f, 0x7b, 0x73, 0x63, 0x68, 0x65, 0x6d, 0x61, 0x7d, 0x22, 0xa7, 0x01, 0x0a, 0x13, 0x43,
	0x72, 0x65, 0x61, 0x74, 0x65, 0x53, 0x63, 0x68, 0x65, 0x6d, 0x61, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x12, 0x3c, 0x0a, 0x06, 0x70, 0x61, 0x72, 0x65, 0x6e, 0x74, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x42, 0x24, 0xe0, 0x41, 0x02, 0xfa, 0x41, 0x1e, 0x12, 0x1c, 0x70, 0x75, 0x62, 0x73,
	0x75, 0x62, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x63, 0x6f,
	0x6d, 0x2f, 0x53, 0x63, 0x68, 0x65, 0x6d, 0x61, 0x52, 0x06, 0x70, 0x61, 0x72, 0x65, 0x6e, 0x74,
	0x12, 0x35, 0x0a, 0x06, 0x73, 0x63, 0x68, 0x65, 0x6d, 0x61, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x18, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x75, 0x62, 0x73, 0x75, 0x62,
	0x2e, 0x76, 0x31, 0x2e, 0x53, 0x63, 0x68, 0x65, 0x6d, 0x61, 0x42, 0x03, 0xe0, 0x41, 0x02, 0x52,
	0x06, 0x73, 0x63, 0x68, 0x65, 0x6d, 0x61, 0x12, 0x1b, 0x0a, 0x09, 0x73, 0x63, 0x68, 0x65, 0x6d,
	0x61, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x73, 0x63, 0x68, 0x65,
	0x6d, 0x61, 0x49, 0x64, 0x22, 0x7e, 0x0a, 0x10, 0x47, 0x65, 0x74, 0x53, 0x63, 0x68, 0x65, 0x6d,
	0x61, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x38, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x24, 0xe0, 0x41, 0x02, 0xfa, 0x41, 0x1e, 0x0a, 0x1c,
	0x70, 0x75, 0x62, 0x73, 0x75, 0x62, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x61, 0x70, 0x69,
	0x73, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x53, 0x63, 0x68, 0x65, 0x6d, 0x61, 0x52, 0x04, 0x6e, 0x61,
	0x6d, 0x65, 0x12, 0x30, 0x0a, 0x04, 0x76, 0x69, 0x65, 0x77, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e,
	0x32, 0x1c, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x75, 0x62, 0x73, 0x75, 0x62,
	0x2e, 0x76, 0x31, 0x2e, 0x53, 0x63, 0x68, 0x65, 0x6d, 0x61, 0x56, 0x69, 0x65, 0x77, 0x52, 0x04,
	0x76, 0x69, 0x65, 0x77, 0x22, 0xcf, 0x01, 0x0a, 0x12, 0x4c, 0x69, 0x73, 0x74, 0x53, 0x63, 0x68,
	0x65, 0x6d, 0x61, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x4b, 0x0a, 0x06, 0x70,
	0x61, 0x72, 0x65, 0x6e, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x33, 0xe0, 0x41, 0x02,
	0xfa, 0x41, 0x2d, 0x0a, 0x2b, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72,
	0x63, 0x65, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x61, 0x70, 0x69, 0x73, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x50, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74,
	0x52, 0x06, 0x70, 0x61, 0x72, 0x65, 0x6e, 0x74, 0x12, 0x30, 0x0a, 0x04, 0x76, 0x69, 0x65, 0x77,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1c, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e,
	0x70, 0x75, 0x62, 0x73, 0x75, 0x62, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x63, 0x68, 0x65, 0x6d, 0x61,
	0x56, 0x69, 0x65, 0x77, 0x52, 0x04, 0x76, 0x69, 0x65, 0x77, 0x12, 0x1b, 0x0a, 0x09, 0x70, 0x61,
	0x67, 0x65, 0x5f, 0x73, 0x69, 0x7a, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x70,
	0x61, 0x67, 0x65, 0x53, 0x69, 0x7a, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x70, 0x61, 0x67, 0x65, 0x5f,
	0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x70, 0x61, 0x67,
	0x65, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x22, 0x71, 0x0a, 0x13, 0x4c, 0x69, 0x73, 0x74, 0x53, 0x63,
	0x68, 0x65, 0x6d, 0x61, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x32, 0x0a,
	0x07, 0x73, 0x63, 0x68, 0x65, 0x6d, 0x61, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x18,
	0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x75, 0x62, 0x73, 0x75, 0x62, 0x2e, 0x76,
	0x31, 0x2e, 0x53, 0x63, 0x68, 0x65, 0x6d, 0x61, 0x52, 0x07, 0x73, 0x63, 0x68, 0x65, 0x6d, 0x61,
	0x73, 0x12, 0x26, 0x0a, 0x0f, 0x6e, 0x65, 0x78, 0x74, 0x5f, 0x70, 0x61, 0x67, 0x65, 0x5f, 0x74,
	0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x6e, 0x65, 0x78, 0x74,
	0x50, 0x61, 0x67, 0x65, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x22, 0xc4, 0x01, 0x0a, 0x1a, 0x4c, 0x69,
	0x73, 0x74, 0x53, 0x63, 0x68, 0x65, 0x6d, 0x61, 0x52, 0x65, 0x76, 0x69, 0x73, 0x69, 0x6f, 0x6e,
	0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x38, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x24, 0xe0, 0x41, 0x02, 0xfa, 0x41, 0x1e, 0x0a, 0x1c,
	0x70, 0x75, 0x62, 0x73, 0x75, 0x62, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x61, 0x70, 0x69,
	0x73, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x53, 0x63, 0x68, 0x65, 0x6d, 0x61, 0x52, 0x04, 0x6e, 0x61,
	0x6d, 0x65, 0x12, 0x30, 0x0a, 0x04, 0x76, 0x69, 0x65, 0x77, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e,
	0x32, 0x1c, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x75, 0x62, 0x73, 0x75, 0x62,
	0x2e, 0x76, 0x31, 0x2e, 0x53, 0x63, 0x68, 0x65, 0x6d, 0x61, 0x56, 0x69, 0x65, 0x77, 0x52, 0x04,
	0x76, 0x69, 0x65, 0x77, 0x12, 0x1b, 0x0a, 0x09, 0x70, 0x61, 0x67, 0x65, 0x5f, 0x73, 0x69, 0x7a,
	0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x70, 0x61, 0x67, 0x65, 0x53, 0x69, 0x7a,
	0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x70, 0x61, 0x67, 0x65, 0x5f, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x70, 0x61, 0x67, 0x65, 0x54, 0x6f, 0x6b, 0x65, 0x6e,
	0x22, 0x79, 0x0a, 0x1b, 0x4c, 0x69, 0x73, 0x74, 0x53, 0x63, 0x68, 0x65, 0x6d, 0x61, 0x52, 0x65,
	0x76, 0x69, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12,
	0x32, 0x0a, 0x07, 0x73, 0x63, 0x68, 0x65, 0x6d, 0x61, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x18, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x75, 0x62, 0x73, 0x75, 0x62,
	0x2e, 0x76, 0x31, 0x2e, 0x53, 0x63, 0x68, 0x65, 0x6d, 0x61, 0x52, 0x07, 0x73, 0x63, 0x68, 0x65,
	0x6d, 0x61, 0x73, 0x12, 0x26, 0x0a, 0x0f, 0x6e, 0x65, 0x78, 0x74, 0x5f, 0x70, 0x61, 0x67, 0x65,
	0x5f, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x6e, 0x65,
	0x78, 0x74, 0x50, 0x61, 0x67, 0x65, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x22, 0x86, 0x01, 0x0a, 0x13,
	0x43, 0x6f, 0x6d, 0x6d, 0x69, 0x74, 0x53, 0x63, 0x68, 0x65, 0x6d, 0x61, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x12, 0x38, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x42, 0x24, 0xe0, 0x41, 0x02, 0xfa, 0x41, 0x1e, 0x0a, 0x1c, 0x70, 0x75, 0x62, 0x73, 0x75,
	0x62, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x63, 0x6f, 0x6d,
	0x2f, 0x53, 0x63, 0x68, 0x65, 0x6d, 0x61, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x35, 0x0a,
	0x06, 0x73, 0x63, 0x68, 0x65, 0x6d, 0x61, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x75, 0x62, 0x73, 0x75, 0x62, 0x2e, 0x76, 0x31,
	0x2e, 0x53, 0x63, 0x68, 0x65, 0x6d, 0x61, 0x42, 0x03, 0xe0, 0x41, 0x02, 0x52, 0x06, 0x73, 0x63,
	0x68, 0x65, 0x6d, 0x61, 0x22, 0x77, 0x0a, 0x15, 0x52, 0x6f, 0x6c, 0x6c, 0x62, 0x61, 0x63, 0x6b,
	0x53, 0x63, 0x68, 0x65, 0x6d, 0x61, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x38, 0x0a,
	0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x24, 0xe0, 0x41, 0x02,
	0xfa, 0x41, 0x1e, 0x0a, 0x1c, 0x70, 0x75, 0x62, 0x73, 0x75, 0x62, 0x2e, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x53, 0x63, 0x68, 0x65, 0x6d,
	0x61, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x24, 0x0a, 0x0b, 0x72, 0x65, 0x76, 0x69, 0x73,
	0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x03, 0xe0, 0x41,
	0x02, 0x52, 0x0a, 0x72, 0x65, 0x76, 0x69, 0x73, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x22, 0x7f, 0x0a,
	0x1b, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x53, 0x63, 0x68, 0x65, 0x6d, 0x61, 0x52, 0x65, 0x76,
	0x69, 0x73, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x38, 0x0a, 0x04,
	0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x24, 0xe0, 0x41, 0x02, 0xfa,
	0x41, 0x1e, 0x0a, 0x1c, 0x70, 0x75, 0x62, 0x73, 0x75, 0x62, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x53, 0x63, 0x68, 0x65, 0x6d, 0x61,
	0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x26, 0x0a, 0x0b, 0x72, 0x65, 0x76, 0x69, 0x73, 0x69,
	0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x05, 0xe0, 0x41, 0x01,
	0x18, 0x01, 0x52, 0x0a, 0x72, 0x65, 0x76, 0x69, 0x73, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x22, 0x4f,
	0x0a, 0x13, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x53, 0x63, 0x68, 0x65, 0x6d, 0x61, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x38, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x42, 0x24, 0xe0, 0x41, 0x02, 0xfa, 0x41, 0x1e, 0x0a, 0x1c, 0x70, 0x75, 0x62,
	0x73, 0x75, 0x62, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x63,
	0x6f, 0x6d, 0x2f, 0x53, 0x63, 0x68, 0x65, 0x6d, 0x61, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x22,
	0x9b, 0x01, 0x0a, 0x15, 0x56, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x53, 0x63, 0x68, 0x65,
	0x6d, 0x61, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x4b, 0x0a, 0x06, 0x70, 0x61, 0x72,
	0x65, 0x6e, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x33, 0xe0, 0x41, 0x02, 0xfa, 0x41,
	0x2d, 0x0a, 0x2b, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65,
	0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x61, 0x70,
	0x69, 0x73, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x50, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x52, 0x06,
	0x70, 0x61, 0x72, 0x65, 0x6e, 0x74, 0x12, 0x35, 0x0a, 0x06, 0x73, 0x63, 0x68, 0x65, 0x6d, 0x61,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e,
	0x70, 0x75, 0x62, 0x73, 0x75, 0x62, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x63, 0x68, 0x65, 0x6d, 0x61,
	0x42, 0x03, 0xe0, 0x41, 0x02, 0x52, 0x06, 0x73, 0x63, 0x68, 0x65, 0x6d, 0x61, 0x22, 0x18, 0x0a,
	0x16, 0x56, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x53, 0x63, 0x68, 0x65, 0x6d, 0x61, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0xb3, 0x02, 0x0a, 0x16, 0x56, 0x61, 0x6c, 0x69,
	0x64, 0x61, 0x74, 0x65, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x12, 0x4b, 0x0a, 0x06, 0x70, 0x61, 0x72, 0x65, 0x6e, 0x74, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x42, 0x33, 0xe0, 0x41, 0x02, 0xfa, 0x41, 0x2d, 0x0a, 0x2b, 0x63, 0x6c, 0x6f, 0x75,
	0x64, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72,
	0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x63, 0x6f, 0x6d, 0x2f,
	0x50, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x52, 0x06, 0x70, 0x61, 0x72, 0x65, 0x6e, 0x74, 0x12,
	0x37, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x21, 0xfa,
	0x41, 0x1e, 0x0a, 0x1c, 0x70, 0x75, 0x62, 0x73, 0x75, 0x62, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x53, 0x63, 0x68, 0x65, 0x6d, 0x61,
	0x48, 0x00, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x32, 0x0a, 0x06, 0x73, 0x63, 0x68, 0x65,
	0x6d, 0x61, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2e, 0x70, 0x75, 0x62, 0x73, 0x75, 0x62, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x63, 0x68, 0x65,
	0x6d, 0x61, 0x48, 0x00, 0x52, 0x06, 0x73, 0x63, 0x68, 0x65, 0x6d, 0x61, 0x12, 0x18, 0x0a, 0x07,
	0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x07, 0x6d,
	0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x36, 0x0a, 0x08, 0x65, 0x6e, 0x63, 0x6f, 0x64, 0x69,
	0x6e, 0x67, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2e, 0x70, 0x75, 0x62, 0x73, 0x75, 0x62, 0x2e, 0x76, 0x31, 0x2e, 0x45, 0x6e, 0x63, 0x6f,
	0x64, 0x69, 0x6e, 0x67, 0x52, 0x08, 0x65, 0x6e, 0x63, 0x6f, 0x64, 0x69, 0x6e, 0x67, 0x42, 0x0d,
	0x0a, 0x0b, 0x73, 0x63, 0x68, 0x65, 0x6d, 0x61, 0x5f, 0x73, 0x70, 0x65, 0x63, 0x22, 0x19, 0x0a,
	0x17, 0x56, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2a, 0x3e, 0x0a, 0x0a, 0x53, 0x63, 0x68, 0x65,
	0x6d, 0x61, 0x56, 0x69, 0x65, 0x77, 0x12, 0x1b, 0x0a, 0x17, 0x53, 0x43, 0x48, 0x45, 0x4d, 0x41,
	0x5f, 0x56, 0x49, 0x45, 0x57, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45,
	0x44, 0x10, 0x00, 0x12, 0x09, 0x0a, 0x05, 0x42, 0x41, 0x53, 0x49, 0x43, 0x10, 0x01, 0x12, 0x08,
	0x0a, 0x04, 0x46, 0x55, 0x4c, 0x4c, 0x10, 0x02, 0x2a, 0x3a, 0x0a, 0x08, 0x45, 0x6e, 0x63, 0x6f,
	0x64, 0x69, 0x6e, 0x67, 0x12, 0x18, 0x0a, 0x14, 0x45, 0x4e, 0x43, 0x4f, 0x44, 0x49, 0x4e, 0x47,
	0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x08,
	0x0a, 0x04, 0x4a, 0x53, 0x4f, 0x4e, 0x10, 0x01, 0x12, 0x0a, 0x0a, 0x06, 0x42, 0x49, 0x4e, 0x41,
	0x52, 0x59, 0x10, 0x02, 0x32, 0x88, 0x0d, 0x0a, 0x0d, 0x53, 0x63, 0x68, 0x65, 0x6d, 0x61, 0x53,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x9a, 0x01, 0x0a, 0x0c, 0x43, 0x72, 0x65, 0x61, 0x74,
	0x65, 0x53, 0x63, 0x68, 0x65, 0x6d, 0x61, 0x12, 0x25, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2e, 0x70, 0x75, 0x62, 0x73, 0x75, 0x62, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74,
	0x65, 0x53, 0x63, 0x68, 0x65, 0x6d, 0x61, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x18,
	0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x75, 0x62, 0x73, 0x75, 0x62, 0x2e, 0x76,
	0x31, 0x2e, 0x53, 0x63, 0x68, 0x65, 0x6d, 0x61, 0x22, 0x49, 0xda, 0x41, 0x17, 0x70, 0x61, 0x72,
	0x65, 0x6e, 0x74, 0x2c, 0x73, 0x63, 0x68, 0x65, 0x6d, 0x61, 0x2c, 0x73, 0x63, 0x68, 0x65, 0x6d,
	0x61, 0x5f, 0x69, 0x64, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x29, 0x3a, 0x06, 0x73, 0x63, 0x68, 0x65,
	0x6d, 0x61, 0x22, 0x1f, 0x2f, 0x76, 0x31, 0x2f, 0x7b, 0x70, 0x61, 0x72, 0x65, 0x6e, 0x74, 0x3d,
	0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x73, 0x2f, 0x2a, 0x7d, 0x2f, 0x73, 0x63, 0x68, 0x65,
	0x6d, 0x61, 0x73, 0x12, 0x79, 0x0a, 0x09, 0x47, 0x65, 0x74, 0x53, 0x63, 0x68, 0x65, 0x6d, 0x61,
	0x12, 0x22, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x75, 0x62, 0x73, 0x75, 0x62,
	0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x53, 0x63, 0x68, 0x65, 0x6d, 0x61, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x1a, 0x18, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x75,
	0x62, 0x73, 0x75, 0x62, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x63, 0x68, 0x65, 0x6d, 0x61, 0x22, 0x2e,
	0xda, 0x41, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x21, 0x12, 0x1f, 0x2f,
	0x76, 0x31, 0x2f, 0x7b, 0x6e, 0x61, 0x6d, 0x65, 0x3d, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74,
	0x73, 0x2f, 0x2a, 0x2f, 0x73, 0x63, 0x68, 0x65, 0x6d, 0x61, 0x73, 0x2f, 0x2a, 0x7d, 0x12, 0x8c,
	0x01, 0x0a, 0x0b, 0x4c, 0x69, 0x73, 0x74, 0x53, 0x63, 0x68, 0x65, 0x6d, 0x61, 0x73, 0x12, 0x24,
	0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x75, 0x62, 0x73, 0x75, 0x62, 0x2e, 0x76,
	0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x53, 0x63, 0x68, 0x65, 0x6d, 0x61, 0x73, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x1a, 0x25, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x75,
	0x62, 0x73, 0x75, 0x62, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x53, 0x63, 0x68, 0x65,
	0x6d, 0x61, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x30, 0xda, 0x41, 0x06,
	0x70, 0x61, 0x72, 0x65, 0x6e, 0x74, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x21, 0x12, 0x1f, 0x2f, 0x76,
	0x31, 0x2f, 0x7b, 0x70, 0x61, 0x72, 0x65, 0x6e, 0x74, 0x3d, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63,
	0x74, 0x73, 0x2f, 0x2a, 0x7d, 0x2f, 0x73, 0x63, 0x68, 0x65, 0x6d, 0x61, 0x73, 0x12, 0xb0, 0x01,
	0x0a, 0x13, 0x4c, 0x69, 0x73, 0x74, 0x53, 0x63, 0x68, 0x65, 0x6d, 0x61, 0x52, 0x65, 0x76, 0x69,
	0x73, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x2c, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70,
	0x75, 0x62, 0x73, 0x75, 0x62, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x53, 0x63, 0x68,
	0x65, 0x6d, 0x61, 0x52, 0x65, 0x76, 0x69, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x1a, 0x2d, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x75, 0x62,
	0x73, 0x75, 0x62, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x53, 0x63, 0x68, 0x65, 0x6d,
	0x61, 0x52, 0x65, 0x76, 0x69, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x22, 0x3c, 0xda, 0x41, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x82, 0xd3, 0xe4, 0x93, 0x02,
	0x2f, 0x12, 0x2d, 0x2f, 0x76, 0x31, 0x2f, 0x7b, 0x6e, 0x61, 0x6d, 0x65, 0x3d, 0x70, 0x72, 0x6f,
	0x6a, 0x65, 0x63, 0x74, 0x73, 0x2f, 0x2a, 0x2f, 0x73, 0x63, 0x68, 0x65, 0x6d, 0x61, 0x73, 0x2f,
	0x2a, 0x7d, 0x3a, 0x6c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x76, 0x69, 0x73, 0x69, 0x6f, 0x6e, 0x73,
	0x12, 0x90, 0x01, 0x0a, 0x0c, 0x43, 0x6f, 0x6d, 0x6d, 0x69, 0x74, 0x53, 0x63, 0x68, 0x65, 0x6d,
	0x61, 0x12, 0x25, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x75, 0x62, 0x73, 0x75,
	0x62, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x6f, 0x6d, 0x6d, 0x69, 0x74, 0x53, 0x63, 0x68, 0x65, 0x6d,
	0x61, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x18, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2e, 0x70, 0x75, 0x62, 0x73, 0x75, 0x62, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x63, 0x68, 0x65,
	0x6d, 0x61, 0x22, 0x3f, 0xda, 0x41, 0x0b, 0x6e, 0x61, 0x6d, 0x65, 0x2c, 0x73, 0x63, 0x68, 0x65,
	0x6d, 0x61, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x2b, 0x3a, 0x01, 0x2a, 0x22, 0x26, 0x2f, 0x76, 0x31,
	0x2f, 0x7b, 0x6e, 0x61, 0x6d, 0x65, 0x3d, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x73, 0x2f,
	0x2a, 0x2f, 0x73, 0x63, 0x68, 0x65, 0x6d, 0x61, 0x73, 0x2f, 0x2a, 0x7d, 0x3a, 0x63, 0x6f, 0x6d,
	0x6d, 0x69, 0x74, 0x12, 0x9b, 0x01, 0x0a, 0x0e, 0x52, 0x6f, 0x6c, 0x6c, 0x62, 0x61, 0x63, 0x6b,
	0x53, 0x63, 0x68, 0x65, 0x6d, 0x61, 0x12, 0x27, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e,
	0x70, 0x75, 0x62, 0x73, 0x75, 0x62, 0x2e, 0x76, 0x31, 0x2e, 0x52, 0x6f, 0x6c, 0x6c, 0x62, 0x61,
	0x63, 0x6b, 0x53, 0x63, 0x68, 0x65, 0x6d, 0x61, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a,
	0x18, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x75, 0x62, 0x73, 0x75, 0x62, 0x2e,
	0x76, 0x31, 0x2e, 0x53, 0x63, 0x68, 0x65, 0x6d, 0x61, 0x22, 0x46, 0xda, 0x41, 0x10, 0x6e, 0x61,
	0x6d, 0x65, 0x2c, 0x72, 0x65, 0x76, 0x69, 0x73, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x82, 0xd3,
	0xe4, 0x93, 0x02, 0x2d, 0x3a, 0x01, 0x2a, 0x22, 0x28, 0x2f, 0x76, 0x31, 0x2f, 0x7b, 0x6e, 0x61,
	0x6d, 0x65, 0x3d, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x73, 0x2f, 0x2a, 0x2f, 0x73, 0x63,
	0x68, 0x65, 0x6d, 0x61, 0x73, 0x2f, 0x2a, 0x7d, 0x3a, 0x72, 0x6f, 0x6c, 0x6c, 0x62, 0x61, 0x63,
	0x6b, 0x12, 0xaa, 0x01, 0x0a, 0x14, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x53, 0x63, 0x68, 0x65,
	0x6d, 0x61, 0x52, 0x65, 0x76, 0x69, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x2d, 0x2e, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2e, 0x70, 0x75, 0x62, 0x73, 0x75, 0x62, 0x2e, 0x76, 0x31, 0x2e, 0x44, 0x65,
	0x6c, 0x65, 0x74, 0x65, 0x53, 0x63, 0x68, 0x65, 0x6d, 0x61, 0x52, 0x65, 0x76, 0x69, 0x73, 0x69,
	0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x18, 0x2e, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2e, 0x70, 0x75, 0x62, 0x73, 0x75, 0x62, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x63, 0x68,
	0x65, 0x6d, 0x61, 0x22, 0x49, 0xda, 0x41, 0x10, 0x6e, 0x61, 0x6d, 0x65, 0x2c, 0x72, 0x65, 0x76,
	0x69, 0x73, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x30, 0x2a, 0x2e,
	0x2f, 0x76, 0x31, 0x2f, 0x7b, 0x6e, 0x61, 0x6d, 0x65, 0x3d, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63,
	0x74, 0x73, 0x2f, 0x2a, 0x2f, 0x73, 0x63, 0x68, 0x65, 0x6d, 0x61, 0x73, 0x2f, 0x2a, 0x7d, 0x3a,
	0x64, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x52, 0x65, 0x76, 0x69, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x7d,
	0x0a, 0x0c, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x53, 0x63, 0x68, 0x65, 0x6d, 0x61, 0x12, 0x25,
	0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x75, 0x62, 0x73, 0x75, 0x62, 0x2e, 0x76,
	0x31, 0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x53, 0x63, 0x68, 0x65, 0x6d, 0x61, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x22, 0x2e, 0xda,
	0x41, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x21, 0x2a, 0x1f, 0x2f, 0x76,
	0x31, 0x2f, 0x7b, 0x6e, 0x61, 0x6d, 0x65, 0x3d, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x73,
	0x2f, 0x2a, 0x2f, 0x73, 0x63, 0x68, 0x65, 0x6d, 0x61, 0x73, 0x2f, 0x2a, 0x7d, 0x12, 0xa8, 0x01,
	0x0a, 0x0e, 0x56, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x53, 0x63, 0x68, 0x65, 0x6d, 0x61,
	0x12, 0x27, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x75, 0x62, 0x73, 0x75, 0x62,
	0x2e, 0x76, 0x31, 0x2e, 0x56, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x53, 0x63, 0x68, 0x65,
	0x6d, 0x61, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x28, 0x2e, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2e, 0x70, 0x75, 0x62, 0x73, 0x75, 0x62, 0x2e, 0x76, 0x31, 0x2e, 0x56, 0x61, 0x6c,
	0x69, 0x64, 0x61, 0x74, 0x65, 0x53, 0x63, 0x68, 0x65, 0x6d, 0x61, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x22, 0x43, 0xda, 0x41, 0x0d, 0x70, 0x61, 0x72, 0x65, 0x6e, 0x74, 0x2c, 0x73,
	0x63, 0x68, 0x65, 0x6d, 0x61, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x2d, 0x3a, 0x01, 0x2a, 0x22, 0x28,
	0x2f, 0x76, 0x31, 0x2f, 0x7b, 0x70, 0x61, 0x72, 0x65, 0x6e, 0x74, 0x3d, 0x70, 0x72, 0x6f, 0x6a,
	0x65, 0x63, 0x74, 0x73, 0x2f, 0x2a, 0x7d, 0x2f, 0x73, 0x63, 0x68, 0x65, 0x6d, 0x61, 0x73, 0x3a,
	0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x12, 0xa2, 0x01, 0x0a, 0x0f, 0x56, 0x61, 0x6c,
	0x69, 0x64, 0x61, 0x74, 0x65, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x28, 0x2e, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x75, 0x62, 0x73, 0x75, 0x62, 0x2e, 0x76, 0x31, 0x2e,
	0x56, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x29, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e,
	0x70, 0x75, 0x62, 0x73, 0x75, 0x62, 0x2e, 0x76, 0x31, 0x2e, 0x56, 0x61, 0x6c, 0x69, 0x64, 0x61,
	0x74, 0x65, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x22, 0x3a, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x34, 0x3a, 0x01, 0x2a, 0x22, 0x2f, 0x2f, 0x76,
	0x31, 0x2f, 0x7b, 0x70, 0x61, 0x72, 0x65, 0x6e, 0x74, 0x3d, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63,
	0x74, 0x73, 0x2f, 0x2a, 0x7d, 0x2f, 0x73, 0x63, 0x68, 0x65, 0x6d, 0x61, 0x73, 0x3a, 0x76, 0x61,
	0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x1a, 0x70, 0xca,
	0x41, 0x15, 0x70, 0x75, 0x62, 0x73, 0x75, 0x62, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x61,
	0x70, 0x69, 0x73, 0x2e, 0x63, 0x6f, 0x6d, 0xd2, 0x41, 0x55, 0x68, 0x74, 0x74, 0x70, 0x73, 0x3a,
	0x2f, 0x2f, 0x77, 0x77, 0x77, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x61, 0x70, 0x69, 0x73,
	0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x61, 0x75, 0x74, 0x68, 0x2f, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x2d,
	0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x2c, 0x68, 0x74, 0x74, 0x70, 0x73, 0x3a, 0x2f,
	0x2f, 0x77, 0x77, 0x77, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x61, 0x70, 0x69, 0x73, 0x2e,
	0x63, 0x6f, 0x6d, 0x2f, 0x61, 0x75, 0x74, 0x68, 0x2f, 0x70, 0x75, 0x62, 0x73, 0x75, 0x62, 0x42,
	0xaa, 0x01, 0x0a, 0x14, 0x63, 0x6f, 0x6d, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70,
	0x75, 0x62, 0x73, 0x75, 0x62, 0x2e, 0x76, 0x31, 0x42, 0x0b, 0x53, 0x63, 0x68, 0x65, 0x6d, 0x61,
	0x50, 0x72, 0x6f, 0x74, 0x6f, 0x50, 0x01, 0x5a, 0x32, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x2e, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x67, 0x6f, 0x2f, 0x70, 0x75, 0x62,
	0x73, 0x75, 0x62, 0x2f, 0x61, 0x70, 0x69, 0x76, 0x31, 0x2f, 0x70, 0x75, 0x62, 0x73, 0x75, 0x62,
	0x70, 0x62, 0x3b, 0x70, 0x75, 0x62, 0x73, 0x75, 0x62, 0x70, 0x62, 0xf8, 0x01, 0x01, 0xaa, 0x02,
	0x16, 0x47, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x43, 0x6c, 0x6f, 0x75, 0x64, 0x2e, 0x50, 0x75,
	0x62, 0x53, 0x75, 0x62, 0x2e, 0x56, 0x31, 0xca, 0x02, 0x16, 0x47, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x5c, 0x43, 0x6c, 0x6f, 0x75, 0x64, 0x5c, 0x50, 0x75, 0x62, 0x53, 0x75, 0x62, 0x5c, 0x56, 0x31,
	0xea, 0x02, 0x19, 0x47, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x3a, 0x3a, 0x43, 0x6c, 0x6f, 0x75, 0x64,
	0x3a, 0x3a, 0x50, 0x75, 0x62, 0x53, 0x75, 0x62, 0x3a, 0x3a, 0x56, 0x31, 0x62, 0x06, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_google_pubsub_v1_schema_proto_rawDescOnce sync.Once
	file_google_pubsub_v1_schema_proto_rawDescData = file_google_pubsub_v1_schema_proto_rawDesc
)

func file_google_pubsub_v1_schema_proto_rawDescGZIP() []byte {
	file_google_pubsub_v1_schema_proto_rawDescOnce.Do(func() {
		file_google_pubsub_v1_schema_proto_rawDescData = protoimpl.X.CompressGZIP(file_google_pubsub_v1_schema_proto_rawDescData)
	})
	return file_google_pubsub_v1_schema_proto_rawDescData
}

var file_google_pubsub_v1_schema_proto_enumTypes = make([]protoimpl.EnumInfo, 3)
var file_google_pubsub_v1_schema_proto_msgTypes = make([]protoimpl.MessageInfo, 15)
var file_google_pubsub_v1_schema_proto_goTypes = []interface{}{
	(SchemaView)(0),                     // 0: google.pubsub.v1.SchemaView
	(Encoding)(0),                       // 1: google.pubsub.v1.Encoding
	(Schema_Type)(0),                    // 2: google.pubsub.v1.Schema.Type
	(*Schema)(nil),                      // 3: google.pubsub.v1.Schema
	(*CreateSchemaRequest)(nil),         // 4: google.pubsub.v1.CreateSchemaRequest
	(*GetSchemaRequest)(nil),            // 5: google.pubsub.v1.GetSchemaRequest
	(*ListSchemasRequest)(nil),          // 6: google.pubsub.v1.ListSchemasRequest
	(*ListSchemasResponse)(nil),         // 7: google.pubsub.v1.ListSchemasResponse
	(*ListSchemaRevisionsRequest)(nil),  // 8: google.pubsub.v1.ListSchemaRevisionsRequest
	(*ListSchemaRevisionsResponse)(nil), // 9: google.pubsub.v1.ListSchemaRevisionsResponse
	(*CommitSchemaRequest)(nil),         // 10: google.pubsub.v1.CommitSchemaRequest
	(*RollbackSchemaRequest)(nil),       // 11: google.pubsub.v1.RollbackSchemaRequest
	(*DeleteSchemaRevisionRequest)(nil), // 12: google.pubsub.v1.DeleteSchemaRevisionRequest
	(*DeleteSchemaRequest)(nil),         // 13: google.pubsub.v1.DeleteSchemaRequest
	(*ValidateSchemaRequest)(nil),       // 14: google.pubsub.v1.ValidateSchemaRequest
	(*ValidateSchemaResponse)(nil),      // 15: google.pubsub.v1.ValidateSchemaResponse
	(*ValidateMessageRequest)(nil),      // 16: google.pubsub.v1.ValidateMessageRequest
	(*ValidateMessageResponse)(nil),     // 17: google.pubsub.v1.ValidateMessageResponse
	(*timestamppb.Timestamp)(nil),       // 18: google.protobuf.Timestamp
	(*emptypb.Empty)(nil),               // 19: google.protobuf.Empty
}
var file_google_pubsub_v1_schema_proto_depIdxs = []int32{
	2,  // 0: google.pubsub.v1.Schema.type:type_name -> google.pubsub.v1.Schema.Type
	18, // 1: google.pubsub.v1.Schema.revision_create_time:type_name -> google.protobuf.Timestamp
	3,  // 2: google.pubsub.v1.CreateSchemaRequest.schema:type_name -> google.pubsub.v1.Schema
	0,  // 3: google.pubsub.v1.GetSchemaRequest.view:type_name -> google.pubsub.v1.SchemaView
	0,  // 4: google.pubsub.v1.ListSchemasRequest.view:type_name -> google.pubsub.v1.SchemaView
	3,  // 5: google.pubsub.v1.ListSchemasResponse.schemas:type_name -> google.pubsub.v1.Schema
	0,  // 6: google.pubsub.v1.ListSchemaRevisionsRequest.view:type_name -> google.pubsub.v1.SchemaView
	3,  // 7: google.pubsub.v1.ListSchemaRevisionsResponse.schemas:type_name -> google.pubsub.v1.Schema
	3,  // 8: google.pubsub.v1.CommitSchemaRequest.schema:type_name -> google.pubsub.v1.Schema
	3,  // 9: google.pubsub.v1.ValidateSchemaRequest.schema:type_name -> google.pubsub.v1.Schema
	3,  // 10: google.pubsub.v1.ValidateMessageRequest.schema:type_name -> google.pubsub.v1.Schema
	1,  // 11: google.pubsub.v1.ValidateMessageRequest.encoding:type_name -> google.pubsub.v1.Encoding
	4,  // 12: google.pubsub.v1.SchemaService.CreateSchema:input_type -> google.pubsub.v1.CreateSchemaRequest
	5,  // 13: google.pubsub.v1.SchemaService.GetSchema:input_type -> google.pubsub.v1.GetSchemaRequest
	6,  // 14: google.pubsub.v1.SchemaService.ListSchemas:input_type -> google.pubsub.v1.ListSchemasRequest
	8,  // 15: google.pubsub.v1.SchemaService.ListSchemaRevisions:input_type -> google.pubsub.v1.ListSchemaRevisionsRequest
	10, // 16: google.pubsub.v1.SchemaService.CommitSchema:input_type -> google.pubsub.v1.CommitSchemaRequest
	11, // 17: google.pubsub.v1.SchemaService.RollbackSchema:input_type -> google.pubsub.v1.RollbackSchemaRequest
	12, // 18: google.pubsub.v1.SchemaService.DeleteSchemaRevision:input_type -> google.pubsub.v1.DeleteSchemaRevisionRequest
	13, // 19: google.pubsub.v1.SchemaService.DeleteSchema:input_type -> google.pubsub.v1.DeleteSchemaRequest
	14, // 20: google.pubsub.v1.SchemaService.ValidateSchema:input_type -> google.pubsub.v1.ValidateSchemaRequest
	16, // 21: google.pubsub.v1.SchemaService.ValidateMessage:input_type -> google.pubsub.v1.ValidateMessageRequest
	3,  // 22: google.pubsub.v1.SchemaService.CreateSchema:output_type -> google.pubsub.v1.Schema
	3,  // 23: google.pubsub.v1.SchemaService.GetSchema:output_type -> google.pubsub.v1.Schema
	7,  // 24: google.pubsub.v1.SchemaService.ListSchemas:output_type -> google.pubsub.v1.ListSchemasResponse
	9,  // 25: google.pubsub.v1.SchemaService.ListSchemaRevisions:output_type -> google.pubsub.v1.ListSchemaRevisionsResponse
	3,  // 26: google.pubsub.v1.SchemaService.CommitSchema:output_type -> google.pubsub.v1.Schema
	3,  // 27: google.pubsub.v1.SchemaService.RollbackSchema:output_type -> google.pubsub.v1.Schema
	3,  // 28: google.pubsub.v1.SchemaService.DeleteSchemaRevision:output_type -> google.pubsub.v1.Schema
	19, // 29: google.pubsub.v1.SchemaService.DeleteSchema:output_type -> google.protobuf.Empty
	15, // 30: google.pubsub.v1.SchemaService.ValidateSchema:output_type -> google.pubsub.v1.ValidateSchemaResponse
	17, // 31: google.pubsub.v1.SchemaService.ValidateMessage:output_type -> google.pubsub.v1.ValidateMessageResponse
	22, // [22:32] is the sub-list for method output_type
	12, // [12:22] is the sub-list for method input_type
	12, // [12:12] is the sub-list for extension type_name
	12, // [12:12] is the sub-list for extension extendee
	0,  // [0:12] is the sub-list for field type_name
}

func init() { file_google_pubsub_v1_schema_proto_init() }
func file_google_pubsub_v1_schema_proto_init() {
	if File_google_pubsub_v1_schema_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_google_pubsub_v1_schema_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Schema); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_pubsub_v1_schema_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateSchemaRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_pubsub_v1_schema_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetSchemaRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_pubsub_v1_schema_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListSchemasRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_pubsub_v1_schema_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListSchemasResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_pubsub_v1_schema_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListSchemaRevisionsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_pubsub_v1_schema_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListSchemaRevisionsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_pubsub_v1_schema_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CommitSchemaRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_pubsub_v1_schema_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RollbackSchemaRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_pubsub_v1_schema_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeleteSchemaRevisionRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_pubsub_v1_schema_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeleteSchemaRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_pubsub_v1_schema_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ValidateSchemaRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_pubsub_v1_schema_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ValidateSchemaResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_pubsub_v1_schema_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ValidateMessageRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_pubsub_v1_schema_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ValidateMessageResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_google_pubsub_v1_schema_proto_msgTypes[13].OneofWrappers = []interface{}{
		(*ValidateMessageRequest_Name)(nil),
		(*ValidateMessageRequest_Schema)(nil),
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_google_pubsub_v1_schema_proto_rawDesc,
			NumEnums:      3,
			NumMessages:   15,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_google_pubsub_v1_schema_proto_goTypes,
		DependencyIndexes: file_google_pubsub_v1_schema_proto_depIdxs,
		EnumInfos:         file_google_pubsub_v1_schema_proto_enumTypes,
		MessageInfos:      file_google_pubsub_v1_schema_proto_msgTypes,
	}.Build()
	File_google_pubsub_v1_schema_proto = out.File
	file_google_pubsub_v1_schema_proto_rawDesc = nil
	file_google_pubsub_v1_schema_proto_goTypes = nil
	file_google_pubsub_v1_schema_proto_depIdxs = nil
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConnInterface

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion6

// SchemaServiceClient is the client API for SchemaService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type SchemaServiceClient interface {
	// Creates a schema.
	CreateSchema(ctx context.Context, in *CreateSchemaRequest, opts ...grpc.CallOption) (*Schema, error)
	// Gets a schema.
	GetSchema(ctx context.Context, in *GetSchemaRequest, opts ...grpc.CallOption) (*Schema, error)
	// Lists schemas in a project.
	ListSchemas(ctx context.Context, in *ListSchemasRequest, opts ...grpc.CallOption) (*ListSchemasResponse, error)
	// Lists all schema revisions for the named schema.
	ListSchemaRevisions(ctx context.Context, in *ListSchemaRevisionsRequest, opts ...grpc.CallOption) (*ListSchemaRevisionsResponse, error)
	// Commits a new schema revision to an existing schema.
	CommitSchema(ctx context.Context, in *CommitSchemaRequest, opts ...grpc.CallOption) (*Schema, error)
	// Creates a new schema revision that is a copy of the provided revision_id.
	RollbackSchema(ctx context.Context, in *RollbackSchemaRequest, opts ...grpc.CallOption) (*Schema, error)
	// Deletes a specific schema revision.
	DeleteSchemaRevision(ctx context.Context, in *DeleteSchemaRevisionRequest, opts ...grpc.CallOption) (*Schema, error)
	// Deletes a schema.
	DeleteSchema(ctx context.Context, in *DeleteSchemaRequest, opts ...grpc.CallOption) (*emptypb.Empty, error)
	// Validates a schema.
	ValidateSchema(ctx context.Context, in *ValidateSchemaRequest, opts ...grpc.CallOption) (*ValidateSchemaResponse, error)
	// Validates a message against a schema.
	ValidateMessage(ctx context.Context, in *ValidateMessageRequest, opts ...grpc.CallOption) (*ValidateMessageResponse, error)
}

type schemaServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewSchemaServiceClient(cc grpc.ClientConnInterface) SchemaServiceClient {
	return &schemaServiceClient{cc}
}

func (c *schemaServiceClient) CreateSchema(ctx context.Context, in *CreateSchemaRequest, opts ...grpc.CallOption) (*Schema, error) {
	out := new(Schema)
	err := c.cc.Invoke(ctx, "/google.pubsub.v1.SchemaService/CreateSchema", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *schemaServiceClient) GetSchema(ctx context.Context, in *GetSchemaRequest, opts ...grpc.CallOption) (*Schema, error) {
	out := new(Schema)
	err := c.cc.Invoke(ctx, "/google.pubsub.v1.SchemaService/GetSchema", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *schemaServiceClient) ListSchemas(ctx context.Context, in *ListSchemasRequest, opts ...grpc.CallOption) (*ListSchemasResponse, error) {
	out := new(ListSchemasResponse)
	err := c.cc.Invoke(ctx, "/google.pubsub.v1.SchemaService/ListSchemas", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *schemaServiceClient) ListSchemaRevisions(ctx context.Context, in *ListSchemaRevisionsRequest, opts ...grpc.CallOption) (*ListSchemaRevisionsResponse, error) {
	out := new(ListSchemaRevisionsResponse)
	err := c.cc.Invoke(ctx, "/google.pubsub.v1.SchemaService/ListSchemaRevisions", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *schemaServiceClient) CommitSchema(ctx context.Context, in *CommitSchemaRequest, opts ...grpc.CallOption) (*Schema, error) {
	out := new(Schema)
	err := c.cc.Invoke(ctx, "/google.pubsub.v1.SchemaService/CommitSchema", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *schemaServiceClient) RollbackSchema(ctx context.Context, in *RollbackSchemaRequest, opts ...grpc.CallOption) (*Schema, error) {
	out := new(Schema)
	err := c.cc.Invoke(ctx, "/google.pubsub.v1.SchemaService/RollbackSchema", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *schemaServiceClient) DeleteSchemaRevision(ctx context.Context, in *DeleteSchemaRevisionRequest, opts ...grpc.CallOption) (*Schema, error) {
	out := new(Schema)
	err := c.cc.Invoke(ctx, "/google.pubsub.v1.SchemaService/DeleteSchemaRevision", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *schemaServiceClient) DeleteSchema(ctx context.Context, in *DeleteSchemaRequest, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, "/google.pubsub.v1.SchemaService/DeleteSchema", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *schemaServiceClient) ValidateSchema(ctx context.Context, in *ValidateSchemaRequest, opts ...grpc.CallOption) (*ValidateSchemaResponse, error) {
	out := new(ValidateSchemaResponse)
	err := c.cc.Invoke(ctx, "/google.pubsub.v1.SchemaService/ValidateSchema", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *schemaServiceClient) ValidateMessage(ctx context.Context, in *ValidateMessageRequest, opts ...grpc.CallOption) (*ValidateMessageResponse, error) {
	out := new(ValidateMessageResponse)
	err := c.cc.Invoke(ctx, "/google.pubsub.v1.SchemaService/ValidateMessage", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// SchemaServiceServer is the server API for SchemaService service.
type SchemaServiceServer interface {
	// Creates a schema.
	CreateSchema(context.Context, *CreateSchemaRequest) (*Schema, error)
	// Gets a schema.
	GetSchema(context.Context, *GetSchemaRequest) (*Schema, error)
	// Lists schemas in a project.
	ListSchemas(context.Context, *ListSchemasRequest) (*ListSchemasResponse, error)
	// Lists all schema revisions for the named schema.
	ListSchemaRevisions(context.Context, *ListSchemaRevisionsRequest) (*ListSchemaRevisionsResponse, error)
	// Commits a new schema revision to an existing schema.
	CommitSchema(context.Context, *CommitSchemaRequest) (*Schema, error)
	// Creates a new schema revision that is a copy of the provided revision_id.
	RollbackSchema(context.Context, *RollbackSchemaRequest) (*Schema, error)
	// Deletes a specific schema revision.
	DeleteSchemaRevision(context.Context, *DeleteSchemaRevisionRequest) (*Schema, error)
	// Deletes a schema.
	DeleteSchema(context.Context, *DeleteSchemaRequest) (*emptypb.Empty, error)
	// Validates a schema.
	ValidateSchema(context.Context, *ValidateSchemaRequest) (*ValidateSchemaResponse, error)
	// Validates a message against a schema.
	ValidateMessage(context.Context, *ValidateMessageRequest) (*ValidateMessageResponse, error)
}

// UnimplementedSchemaServiceServer can be embedded to have forward compatible implementations.
type UnimplementedSchemaServiceServer struct {
}

func (*UnimplementedSchemaServiceServer) CreateSchema(context.Context, *CreateSchemaRequest) (*Schema, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateSchema not implemented")
}
func (*UnimplementedSchemaServiceServer) GetSchema(context.Context, *GetSchemaRequest) (*Schema, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetSchema not implemented")
}
func (*UnimplementedSchemaServiceServer) ListSchemas(context.Context, *ListSchemasRequest) (*ListSchemasResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListSchemas not implemented")
}
func (*UnimplementedSchemaServiceServer) ListSchemaRevisions(context.Context, *ListSchemaRevisionsRequest) (*ListSchemaRevisionsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListSchemaRevisions not implemented")
}
func (*UnimplementedSchemaServiceServer) CommitSchema(context.Context, *CommitSchemaRequest) (*Schema, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CommitSchema not implemented")
}
func (*UnimplementedSchemaServiceServer) RollbackSchema(context.Context, *RollbackSchemaRequest) (*Schema, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RollbackSchema not implemented")
}
func (*UnimplementedSchemaServiceServer) DeleteSchemaRevision(context.Context, *DeleteSchemaRevisionRequest) (*Schema, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteSchemaRevision not implemented")
}
func (*UnimplementedSchemaServiceServer) DeleteSchema(context.Context, *DeleteSchemaRequest) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteSchema not implemented")
}
func (*UnimplementedSchemaServiceServer) ValidateSchema(context.Context, *ValidateSchemaRequest) (*ValidateSchemaResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ValidateSchema not implemented")
}
func (*UnimplementedSchemaServiceServer) ValidateMessage(context.Context, *ValidateMessageRequest) (*ValidateMessageResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ValidateMessage not implemented")
}

func RegisterSchemaServiceServer(s *grpc.Server, srv SchemaServiceServer) {
	s.RegisterService(&_SchemaService_serviceDesc, srv)
}

func _SchemaService_CreateSchema_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateSchemaRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SchemaServiceServer).CreateSchema(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/google.pubsub.v1.SchemaService/CreateSchema",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SchemaServiceServer).CreateSchema(ctx, req.(*CreateSchemaRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SchemaService_GetSchema_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetSchemaRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SchemaServiceServer).GetSchema(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/google.pubsub.v1.SchemaService/GetSchema",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SchemaServiceServer).GetSchema(ctx, req.(*GetSchemaRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SchemaService_ListSchemas_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListSchemasRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SchemaServiceServer).ListSchemas(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/google.pubsub.v1.SchemaService/ListSchemas",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SchemaServiceServer).ListSchemas(ctx, req.(*ListSchemasRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SchemaService_ListSchemaRevisions_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListSchemaRevisionsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SchemaServiceServer).ListSchemaRevisions(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/google.pubsub.v1.SchemaService/ListSchemaRevisions",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SchemaServiceServer).ListSchemaRevisions(ctx, req.(*ListSchemaRevisionsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SchemaService_CommitSchema_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CommitSchemaRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SchemaServiceServer).CommitSchema(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/google.pubsub.v1.SchemaService/CommitSchema",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SchemaServiceServer).CommitSchema(ctx, req.(*CommitSchemaRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SchemaService_RollbackSchema_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RollbackSchemaRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SchemaServiceServer).RollbackSchema(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/google.pubsub.v1.SchemaService/RollbackSchema",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SchemaServiceServer).RollbackSchema(ctx, req.(*RollbackSchemaRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SchemaService_DeleteSchemaRevision_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteSchemaRevisionRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SchemaServiceServer).DeleteSchemaRevision(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/google.pubsub.v1.SchemaService/DeleteSchemaRevision",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SchemaServiceServer).DeleteSchemaRevision(ctx, req.(*DeleteSchemaRevisionRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SchemaService_DeleteSchema_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteSchemaRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SchemaServiceServer).DeleteSchema(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/google.pubsub.v1.SchemaService/DeleteSchema",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SchemaServiceServer).DeleteSchema(ctx, req.(*DeleteSchemaRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SchemaService_ValidateSchema_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ValidateSchemaRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SchemaServiceServer).ValidateSchema(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/google.pubsub.v1.SchemaService/ValidateSchema",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SchemaServiceServer).ValidateSchema(ctx, req.(*ValidateSchemaRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SchemaService_ValidateMessage_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ValidateMessageRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SchemaServiceServer).ValidateMessage(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/google.pubsub.v1.SchemaService/ValidateMessage",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SchemaServiceServer).ValidateMessage(ctx, req.(*ValidateMessageRequest))
	}
	return interceptor(ctx, in, info, handler)
}

var _SchemaService_serviceDesc = grpc.ServiceDesc{
	ServiceName: "google.pubsub.v1.SchemaService",
	HandlerType: (*SchemaServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "CreateSchema",
			Handler:    _SchemaService_CreateSchema_Handler,
		},
		{
			MethodName: "GetSchema",
			Handler:    _SchemaService_GetSchema_Handler,
		},
		{
			MethodName: "ListSchemas",
			Handler:    _SchemaService_ListSchemas_Handler,
		},
		{
			MethodName: "ListSchemaRevisions",
			Handler:    _SchemaService_ListSchemaRevisions_Handler,
		},
		{
			MethodName: "CommitSchema",
			Handler:    _SchemaService_CommitSchema_Handler,
		},
		{
			MethodName: "RollbackSchema",
			Handler:    _SchemaService_RollbackSchema_Handler,
		},
		{
			MethodName: "DeleteSchemaRevision",
			Handler:    _SchemaService_DeleteSchemaRevision_Handler,
		},
		{
			MethodName: "DeleteSchema",
			Handler:    _SchemaService_DeleteSchema_Handler,
		},
		{
			MethodName: "ValidateSchema",
			Handler:    _SchemaService_ValidateSchema_Handler,
		},
		{
			MethodName: "ValidateMessage",
			Handler:    _SchemaService_ValidateMessage_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "google/pubsub/v1/schema.proto",
}
