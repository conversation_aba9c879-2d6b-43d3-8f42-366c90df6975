// Copyright 2023 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.30.0
// 	protoc        v4.23.2
// source: google/monitoring/v3/uptime.proto

package monitoringpb

import (
	reflect "reflect"
	sync "sync"

	_ "google.golang.org/genproto/googleapis/api/annotations"
	monitoredres "google.golang.org/genproto/googleapis/api/monitoredres"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	durationpb "google.golang.org/protobuf/types/known/durationpb"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// The regions from which an Uptime check can be run.
type UptimeCheckRegion int32

const (
	// Default value if no region is specified. Will result in Uptime checks
	// running from all regions.
	UptimeCheckRegion_REGION_UNSPECIFIED UptimeCheckRegion = 0
	// Allows checks to run from locations within the United States of America.
	UptimeCheckRegion_USA UptimeCheckRegion = 1
	// Allows checks to run from locations within the continent of Europe.
	UptimeCheckRegion_EUROPE UptimeCheckRegion = 2
	// Allows checks to run from locations within the continent of South
	// America.
	UptimeCheckRegion_SOUTH_AMERICA UptimeCheckRegion = 3
	// Allows checks to run from locations within the Asia Pacific area (ex:
	// Singapore).
	UptimeCheckRegion_ASIA_PACIFIC UptimeCheckRegion = 4
	// Allows checks to run from locations within the western United States of
	// America
	UptimeCheckRegion_USA_OREGON UptimeCheckRegion = 5
	// Allows checks to run from locations within the central United States of
	// America
	UptimeCheckRegion_USA_IOWA UptimeCheckRegion = 6
	// Allows checks to run from locations within the eastern United States of
	// America
	UptimeCheckRegion_USA_VIRGINIA UptimeCheckRegion = 7
)

// Enum value maps for UptimeCheckRegion.
var (
	UptimeCheckRegion_name = map[int32]string{
		0: "REGION_UNSPECIFIED",
		1: "USA",
		2: "EUROPE",
		3: "SOUTH_AMERICA",
		4: "ASIA_PACIFIC",
		5: "USA_OREGON",
		6: "USA_IOWA",
		7: "USA_VIRGINIA",
	}
	UptimeCheckRegion_value = map[string]int32{
		"REGION_UNSPECIFIED": 0,
		"USA":                1,
		"EUROPE":             2,
		"SOUTH_AMERICA":      3,
		"ASIA_PACIFIC":       4,
		"USA_OREGON":         5,
		"USA_IOWA":           6,
		"USA_VIRGINIA":       7,
	}
)

func (x UptimeCheckRegion) Enum() *UptimeCheckRegion {
	p := new(UptimeCheckRegion)
	*p = x
	return p
}

func (x UptimeCheckRegion) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (UptimeCheckRegion) Descriptor() protoreflect.EnumDescriptor {
	return file_google_monitoring_v3_uptime_proto_enumTypes[0].Descriptor()
}

func (UptimeCheckRegion) Type() protoreflect.EnumType {
	return &file_google_monitoring_v3_uptime_proto_enumTypes[0]
}

func (x UptimeCheckRegion) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use UptimeCheckRegion.Descriptor instead.
func (UptimeCheckRegion) EnumDescriptor() ([]byte, []int) {
	return file_google_monitoring_v3_uptime_proto_rawDescGZIP(), []int{0}
}

// The supported resource types that can be used as values of
// `group_resource.resource_type`.
// `INSTANCE` includes `gce_instance` and `aws_ec2_instance` resource types.
// The resource types `gae_app` and `uptime_url` are not valid here because
// group checks on App Engine modules and URLs are not allowed.
type GroupResourceType int32

const (
	// Default value (not valid).
	GroupResourceType_RESOURCE_TYPE_UNSPECIFIED GroupResourceType = 0
	// A group of instances from Google Cloud Platform (GCP) or
	// Amazon Web Services (AWS).
	GroupResourceType_INSTANCE GroupResourceType = 1
	// A group of Amazon ELB load balancers.
	GroupResourceType_AWS_ELB_LOAD_BALANCER GroupResourceType = 2
)

// Enum value maps for GroupResourceType.
var (
	GroupResourceType_name = map[int32]string{
		0: "RESOURCE_TYPE_UNSPECIFIED",
		1: "INSTANCE",
		2: "AWS_ELB_LOAD_BALANCER",
	}
	GroupResourceType_value = map[string]int32{
		"RESOURCE_TYPE_UNSPECIFIED": 0,
		"INSTANCE":                  1,
		"AWS_ELB_LOAD_BALANCER":     2,
	}
)

func (x GroupResourceType) Enum() *GroupResourceType {
	p := new(GroupResourceType)
	*p = x
	return p
}

func (x GroupResourceType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (GroupResourceType) Descriptor() protoreflect.EnumDescriptor {
	return file_google_monitoring_v3_uptime_proto_enumTypes[1].Descriptor()
}

func (GroupResourceType) Type() protoreflect.EnumType {
	return &file_google_monitoring_v3_uptime_proto_enumTypes[1]
}

func (x GroupResourceType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use GroupResourceType.Descriptor instead.
func (GroupResourceType) EnumDescriptor() ([]byte, []int) {
	return file_google_monitoring_v3_uptime_proto_rawDescGZIP(), []int{1}
}

// Operational states for an internal checker.
type InternalChecker_State int32

const (
	// An internal checker should never be in the unspecified state.
	InternalChecker_UNSPECIFIED InternalChecker_State = 0
	// The checker is being created, provisioned, and configured. A checker in
	// this state can be returned by `ListInternalCheckers` or
	// `GetInternalChecker`, as well as by examining the [long running
	// Operation](https://cloud.google.com/apis/design/design_patterns#long_running_operations)
	// that created it.
	InternalChecker_CREATING InternalChecker_State = 1
	// The checker is running and available for use. A checker in this state
	// can be returned by `ListInternalCheckers` or `GetInternalChecker` as
	// well as by examining the [long running
	// Operation](https://cloud.google.com/apis/design/design_patterns#long_running_operations)
	// that created it.
	// If a checker is being torn down, it is neither visible nor usable, so
	// there is no "deleting" or "down" state.
	InternalChecker_RUNNING InternalChecker_State = 2
)

// Enum value maps for InternalChecker_State.
var (
	InternalChecker_State_name = map[int32]string{
		0: "UNSPECIFIED",
		1: "CREATING",
		2: "RUNNING",
	}
	InternalChecker_State_value = map[string]int32{
		"UNSPECIFIED": 0,
		"CREATING":    1,
		"RUNNING":     2,
	}
)

func (x InternalChecker_State) Enum() *InternalChecker_State {
	p := new(InternalChecker_State)
	*p = x
	return p
}

func (x InternalChecker_State) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (InternalChecker_State) Descriptor() protoreflect.EnumDescriptor {
	return file_google_monitoring_v3_uptime_proto_enumTypes[2].Descriptor()
}

func (InternalChecker_State) Type() protoreflect.EnumType {
	return &file_google_monitoring_v3_uptime_proto_enumTypes[2]
}

func (x InternalChecker_State) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use InternalChecker_State.Descriptor instead.
func (InternalChecker_State) EnumDescriptor() ([]byte, []int) {
	return file_google_monitoring_v3_uptime_proto_rawDescGZIP(), []int{0, 0}
}

// What kind of checkers are available to be used by the check.
type UptimeCheckConfig_CheckerType int32

const (
	// The default checker type. Currently converted to `STATIC_IP_CHECKERS`
	// on creation, the default conversion behavior may change in the future.
	UptimeCheckConfig_CHECKER_TYPE_UNSPECIFIED UptimeCheckConfig_CheckerType = 0
	// `STATIC_IP_CHECKERS` are used for uptime checks that perform egress
	// across the public internet. `STATIC_IP_CHECKERS` use the static IP
	// addresses returned by `ListUptimeCheckIps`.
	UptimeCheckConfig_STATIC_IP_CHECKERS UptimeCheckConfig_CheckerType = 1
	// `VPC_CHECKERS` are used for uptime checks that perform egress using
	// Service Directory and private network access. When using `VPC_CHECKERS`,
	// the monitored resource type must be `servicedirectory_service`.
	UptimeCheckConfig_VPC_CHECKERS UptimeCheckConfig_CheckerType = 3
)

// Enum value maps for UptimeCheckConfig_CheckerType.
var (
	UptimeCheckConfig_CheckerType_name = map[int32]string{
		0: "CHECKER_TYPE_UNSPECIFIED",
		1: "STATIC_IP_CHECKERS",
		3: "VPC_CHECKERS",
	}
	UptimeCheckConfig_CheckerType_value = map[string]int32{
		"CHECKER_TYPE_UNSPECIFIED": 0,
		"STATIC_IP_CHECKERS":       1,
		"VPC_CHECKERS":             3,
	}
)

func (x UptimeCheckConfig_CheckerType) Enum() *UptimeCheckConfig_CheckerType {
	p := new(UptimeCheckConfig_CheckerType)
	*p = x
	return p
}

func (x UptimeCheckConfig_CheckerType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (UptimeCheckConfig_CheckerType) Descriptor() protoreflect.EnumDescriptor {
	return file_google_monitoring_v3_uptime_proto_enumTypes[3].Descriptor()
}

func (UptimeCheckConfig_CheckerType) Type() protoreflect.EnumType {
	return &file_google_monitoring_v3_uptime_proto_enumTypes[3]
}

func (x UptimeCheckConfig_CheckerType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use UptimeCheckConfig_CheckerType.Descriptor instead.
func (UptimeCheckConfig_CheckerType) EnumDescriptor() ([]byte, []int) {
	return file_google_monitoring_v3_uptime_proto_rawDescGZIP(), []int{1, 0}
}

// The HTTP request method options.
type UptimeCheckConfig_HttpCheck_RequestMethod int32

const (
	// No request method specified.
	UptimeCheckConfig_HttpCheck_METHOD_UNSPECIFIED UptimeCheckConfig_HttpCheck_RequestMethod = 0
	// GET request.
	UptimeCheckConfig_HttpCheck_GET UptimeCheckConfig_HttpCheck_RequestMethod = 1
	// POST request.
	UptimeCheckConfig_HttpCheck_POST UptimeCheckConfig_HttpCheck_RequestMethod = 2
)

// Enum value maps for UptimeCheckConfig_HttpCheck_RequestMethod.
var (
	UptimeCheckConfig_HttpCheck_RequestMethod_name = map[int32]string{
		0: "METHOD_UNSPECIFIED",
		1: "GET",
		2: "POST",
	}
	UptimeCheckConfig_HttpCheck_RequestMethod_value = map[string]int32{
		"METHOD_UNSPECIFIED": 0,
		"GET":                1,
		"POST":               2,
	}
)

func (x UptimeCheckConfig_HttpCheck_RequestMethod) Enum() *UptimeCheckConfig_HttpCheck_RequestMethod {
	p := new(UptimeCheckConfig_HttpCheck_RequestMethod)
	*p = x
	return p
}

func (x UptimeCheckConfig_HttpCheck_RequestMethod) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (UptimeCheckConfig_HttpCheck_RequestMethod) Descriptor() protoreflect.EnumDescriptor {
	return file_google_monitoring_v3_uptime_proto_enumTypes[4].Descriptor()
}

func (UptimeCheckConfig_HttpCheck_RequestMethod) Type() protoreflect.EnumType {
	return &file_google_monitoring_v3_uptime_proto_enumTypes[4]
}

func (x UptimeCheckConfig_HttpCheck_RequestMethod) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use UptimeCheckConfig_HttpCheck_RequestMethod.Descriptor instead.
func (UptimeCheckConfig_HttpCheck_RequestMethod) EnumDescriptor() ([]byte, []int) {
	return file_google_monitoring_v3_uptime_proto_rawDescGZIP(), []int{1, 2, 0}
}

// Header options corresponding to the content type of a HTTP request body.
type UptimeCheckConfig_HttpCheck_ContentType int32

const (
	// No content type specified.
	UptimeCheckConfig_HttpCheck_TYPE_UNSPECIFIED UptimeCheckConfig_HttpCheck_ContentType = 0
	// `body` is in URL-encoded form. Equivalent to setting the `Content-Type`
	// to `application/x-www-form-urlencoded` in the HTTP request.
	UptimeCheckConfig_HttpCheck_URL_ENCODED UptimeCheckConfig_HttpCheck_ContentType = 1
	// `body` is in `custom_content_type` form. Equivalent to setting the
	// `Content-Type` to the contents of `custom_content_type` in the HTTP
	// request.
	UptimeCheckConfig_HttpCheck_USER_PROVIDED UptimeCheckConfig_HttpCheck_ContentType = 2
)

// Enum value maps for UptimeCheckConfig_HttpCheck_ContentType.
var (
	UptimeCheckConfig_HttpCheck_ContentType_name = map[int32]string{
		0: "TYPE_UNSPECIFIED",
		1: "URL_ENCODED",
		2: "USER_PROVIDED",
	}
	UptimeCheckConfig_HttpCheck_ContentType_value = map[string]int32{
		"TYPE_UNSPECIFIED": 0,
		"URL_ENCODED":      1,
		"USER_PROVIDED":    2,
	}
)

func (x UptimeCheckConfig_HttpCheck_ContentType) Enum() *UptimeCheckConfig_HttpCheck_ContentType {
	p := new(UptimeCheckConfig_HttpCheck_ContentType)
	*p = x
	return p
}

func (x UptimeCheckConfig_HttpCheck_ContentType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (UptimeCheckConfig_HttpCheck_ContentType) Descriptor() protoreflect.EnumDescriptor {
	return file_google_monitoring_v3_uptime_proto_enumTypes[5].Descriptor()
}

func (UptimeCheckConfig_HttpCheck_ContentType) Type() protoreflect.EnumType {
	return &file_google_monitoring_v3_uptime_proto_enumTypes[5]
}

func (x UptimeCheckConfig_HttpCheck_ContentType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use UptimeCheckConfig_HttpCheck_ContentType.Descriptor instead.
func (UptimeCheckConfig_HttpCheck_ContentType) EnumDescriptor() ([]byte, []int) {
	return file_google_monitoring_v3_uptime_proto_rawDescGZIP(), []int{1, 2, 1}
}

// An HTTP status code class.
type UptimeCheckConfig_HttpCheck_ResponseStatusCode_StatusClass int32

const (
	// Default value that matches no status codes.
	UptimeCheckConfig_HttpCheck_ResponseStatusCode_STATUS_CLASS_UNSPECIFIED UptimeCheckConfig_HttpCheck_ResponseStatusCode_StatusClass = 0
	// The class of status codes between 100 and 199.
	UptimeCheckConfig_HttpCheck_ResponseStatusCode_STATUS_CLASS_1XX UptimeCheckConfig_HttpCheck_ResponseStatusCode_StatusClass = 100
	// The class of status codes between 200 and 299.
	UptimeCheckConfig_HttpCheck_ResponseStatusCode_STATUS_CLASS_2XX UptimeCheckConfig_HttpCheck_ResponseStatusCode_StatusClass = 200
	// The class of status codes between 300 and 399.
	UptimeCheckConfig_HttpCheck_ResponseStatusCode_STATUS_CLASS_3XX UptimeCheckConfig_HttpCheck_ResponseStatusCode_StatusClass = 300
	// The class of status codes between 400 and 499.
	UptimeCheckConfig_HttpCheck_ResponseStatusCode_STATUS_CLASS_4XX UptimeCheckConfig_HttpCheck_ResponseStatusCode_StatusClass = 400
	// The class of status codes between 500 and 599.
	UptimeCheckConfig_HttpCheck_ResponseStatusCode_STATUS_CLASS_5XX UptimeCheckConfig_HttpCheck_ResponseStatusCode_StatusClass = 500
	// The class of all status codes.
	UptimeCheckConfig_HttpCheck_ResponseStatusCode_STATUS_CLASS_ANY UptimeCheckConfig_HttpCheck_ResponseStatusCode_StatusClass = 1000
)

// Enum value maps for UptimeCheckConfig_HttpCheck_ResponseStatusCode_StatusClass.
var (
	UptimeCheckConfig_HttpCheck_ResponseStatusCode_StatusClass_name = map[int32]string{
		0:    "STATUS_CLASS_UNSPECIFIED",
		100:  "STATUS_CLASS_1XX",
		200:  "STATUS_CLASS_2XX",
		300:  "STATUS_CLASS_3XX",
		400:  "STATUS_CLASS_4XX",
		500:  "STATUS_CLASS_5XX",
		1000: "STATUS_CLASS_ANY",
	}
	UptimeCheckConfig_HttpCheck_ResponseStatusCode_StatusClass_value = map[string]int32{
		"STATUS_CLASS_UNSPECIFIED": 0,
		"STATUS_CLASS_1XX":         100,
		"STATUS_CLASS_2XX":         200,
		"STATUS_CLASS_3XX":         300,
		"STATUS_CLASS_4XX":         400,
		"STATUS_CLASS_5XX":         500,
		"STATUS_CLASS_ANY":         1000,
	}
)

func (x UptimeCheckConfig_HttpCheck_ResponseStatusCode_StatusClass) Enum() *UptimeCheckConfig_HttpCheck_ResponseStatusCode_StatusClass {
	p := new(UptimeCheckConfig_HttpCheck_ResponseStatusCode_StatusClass)
	*p = x
	return p
}

func (x UptimeCheckConfig_HttpCheck_ResponseStatusCode_StatusClass) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (UptimeCheckConfig_HttpCheck_ResponseStatusCode_StatusClass) Descriptor() protoreflect.EnumDescriptor {
	return file_google_monitoring_v3_uptime_proto_enumTypes[6].Descriptor()
}

func (UptimeCheckConfig_HttpCheck_ResponseStatusCode_StatusClass) Type() protoreflect.EnumType {
	return &file_google_monitoring_v3_uptime_proto_enumTypes[6]
}

func (x UptimeCheckConfig_HttpCheck_ResponseStatusCode_StatusClass) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use UptimeCheckConfig_HttpCheck_ResponseStatusCode_StatusClass.Descriptor instead.
func (UptimeCheckConfig_HttpCheck_ResponseStatusCode_StatusClass) EnumDescriptor() ([]byte, []int) {
	return file_google_monitoring_v3_uptime_proto_rawDescGZIP(), []int{1, 2, 1, 0}
}

// Options to perform content matching.
type UptimeCheckConfig_ContentMatcher_ContentMatcherOption int32

const (
	// No content matcher type specified (maintained for backward
	// compatibility, but deprecated for future use).
	// Treated as `CONTAINS_STRING`.
	UptimeCheckConfig_ContentMatcher_CONTENT_MATCHER_OPTION_UNSPECIFIED UptimeCheckConfig_ContentMatcher_ContentMatcherOption = 0
	// Selects substring matching. The match succeeds if the output contains
	// the `content` string.  This is the default value for checks without
	// a `matcher` option, or where the value of `matcher` is
	// `CONTENT_MATCHER_OPTION_UNSPECIFIED`.
	UptimeCheckConfig_ContentMatcher_CONTAINS_STRING UptimeCheckConfig_ContentMatcher_ContentMatcherOption = 1
	// Selects negation of substring matching. The match succeeds if the
	// output does _NOT_ contain the `content` string.
	UptimeCheckConfig_ContentMatcher_NOT_CONTAINS_STRING UptimeCheckConfig_ContentMatcher_ContentMatcherOption = 2
	// Selects regular-expression matching. The match succeeds if the output
	// matches the regular expression specified in the `content` string.
	// Regex matching is only supported for HTTP/HTTPS checks.
	UptimeCheckConfig_ContentMatcher_MATCHES_REGEX UptimeCheckConfig_ContentMatcher_ContentMatcherOption = 3
	// Selects negation of regular-expression matching. The match succeeds if
	// the output does _NOT_ match the regular expression specified in the
	// `content` string. Regex matching is only supported for HTTP/HTTPS
	// checks.
	UptimeCheckConfig_ContentMatcher_NOT_MATCHES_REGEX UptimeCheckConfig_ContentMatcher_ContentMatcherOption = 4
	// Selects JSONPath matching. See `JsonPathMatcher` for details on when
	// the match succeeds. JSONPath matching is only supported for HTTP/HTTPS
	// checks.
	UptimeCheckConfig_ContentMatcher_MATCHES_JSON_PATH UptimeCheckConfig_ContentMatcher_ContentMatcherOption = 5
	// Selects JSONPath matching. See `JsonPathMatcher` for details on when
	// the match succeeds. Succeeds when output does _NOT_ match as specified.
	// JSONPath is only supported for HTTP/HTTPS checks.
	UptimeCheckConfig_ContentMatcher_NOT_MATCHES_JSON_PATH UptimeCheckConfig_ContentMatcher_ContentMatcherOption = 6
)

// Enum value maps for UptimeCheckConfig_ContentMatcher_ContentMatcherOption.
var (
	UptimeCheckConfig_ContentMatcher_ContentMatcherOption_name = map[int32]string{
		0: "CONTENT_MATCHER_OPTION_UNSPECIFIED",
		1: "CONTAINS_STRING",
		2: "NOT_CONTAINS_STRING",
		3: "MATCHES_REGEX",
		4: "NOT_MATCHES_REGEX",
		5: "MATCHES_JSON_PATH",
		6: "NOT_MATCHES_JSON_PATH",
	}
	UptimeCheckConfig_ContentMatcher_ContentMatcherOption_value = map[string]int32{
		"CONTENT_MATCHER_OPTION_UNSPECIFIED": 0,
		"CONTAINS_STRING":                    1,
		"NOT_CONTAINS_STRING":                2,
		"MATCHES_REGEX":                      3,
		"NOT_MATCHES_REGEX":                  4,
		"MATCHES_JSON_PATH":                  5,
		"NOT_MATCHES_JSON_PATH":              6,
	}
)

func (x UptimeCheckConfig_ContentMatcher_ContentMatcherOption) Enum() *UptimeCheckConfig_ContentMatcher_ContentMatcherOption {
	p := new(UptimeCheckConfig_ContentMatcher_ContentMatcherOption)
	*p = x
	return p
}

func (x UptimeCheckConfig_ContentMatcher_ContentMatcherOption) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (UptimeCheckConfig_ContentMatcher_ContentMatcherOption) Descriptor() protoreflect.EnumDescriptor {
	return file_google_monitoring_v3_uptime_proto_enumTypes[7].Descriptor()
}

func (UptimeCheckConfig_ContentMatcher_ContentMatcherOption) Type() protoreflect.EnumType {
	return &file_google_monitoring_v3_uptime_proto_enumTypes[7]
}

func (x UptimeCheckConfig_ContentMatcher_ContentMatcherOption) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use UptimeCheckConfig_ContentMatcher_ContentMatcherOption.Descriptor instead.
func (UptimeCheckConfig_ContentMatcher_ContentMatcherOption) EnumDescriptor() ([]byte, []int) {
	return file_google_monitoring_v3_uptime_proto_rawDescGZIP(), []int{1, 4, 0}
}

// Options to perform JSONPath content matching.
type UptimeCheckConfig_ContentMatcher_JsonPathMatcher_JsonPathMatcherOption int32

const (
	// No JSONPath matcher type specified (not valid).
	UptimeCheckConfig_ContentMatcher_JsonPathMatcher_JSON_PATH_MATCHER_OPTION_UNSPECIFIED UptimeCheckConfig_ContentMatcher_JsonPathMatcher_JsonPathMatcherOption = 0
	// Selects 'exact string' matching. The match succeeds if the content at
	// the `json_path` within the output is exactly the same as the
	// `content` string.
	UptimeCheckConfig_ContentMatcher_JsonPathMatcher_EXACT_MATCH UptimeCheckConfig_ContentMatcher_JsonPathMatcher_JsonPathMatcherOption = 1
	// Selects regular-expression matching. The match succeeds if the
	// content at the `json_path` within the output matches the regular
	// expression specified in the `content` string.
	UptimeCheckConfig_ContentMatcher_JsonPathMatcher_REGEX_MATCH UptimeCheckConfig_ContentMatcher_JsonPathMatcher_JsonPathMatcherOption = 2
)

// Enum value maps for UptimeCheckConfig_ContentMatcher_JsonPathMatcher_JsonPathMatcherOption.
var (
	UptimeCheckConfig_ContentMatcher_JsonPathMatcher_JsonPathMatcherOption_name = map[int32]string{
		0: "JSON_PATH_MATCHER_OPTION_UNSPECIFIED",
		1: "EXACT_MATCH",
		2: "REGEX_MATCH",
	}
	UptimeCheckConfig_ContentMatcher_JsonPathMatcher_JsonPathMatcherOption_value = map[string]int32{
		"JSON_PATH_MATCHER_OPTION_UNSPECIFIED": 0,
		"EXACT_MATCH":                          1,
		"REGEX_MATCH":                          2,
	}
)

func (x UptimeCheckConfig_ContentMatcher_JsonPathMatcher_JsonPathMatcherOption) Enum() *UptimeCheckConfig_ContentMatcher_JsonPathMatcher_JsonPathMatcherOption {
	p := new(UptimeCheckConfig_ContentMatcher_JsonPathMatcher_JsonPathMatcherOption)
	*p = x
	return p
}

func (x UptimeCheckConfig_ContentMatcher_JsonPathMatcher_JsonPathMatcherOption) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (UptimeCheckConfig_ContentMatcher_JsonPathMatcher_JsonPathMatcherOption) Descriptor() protoreflect.EnumDescriptor {
	return file_google_monitoring_v3_uptime_proto_enumTypes[8].Descriptor()
}

func (UptimeCheckConfig_ContentMatcher_JsonPathMatcher_JsonPathMatcherOption) Type() protoreflect.EnumType {
	return &file_google_monitoring_v3_uptime_proto_enumTypes[8]
}

func (x UptimeCheckConfig_ContentMatcher_JsonPathMatcher_JsonPathMatcherOption) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use UptimeCheckConfig_ContentMatcher_JsonPathMatcher_JsonPathMatcherOption.Descriptor instead.
func (UptimeCheckConfig_ContentMatcher_JsonPathMatcher_JsonPathMatcherOption) EnumDescriptor() ([]byte, []int) {
	return file_google_monitoring_v3_uptime_proto_rawDescGZIP(), []int{1, 4, 0, 0}
}

// An internal checker allows Uptime checks to run on private/internal GCP
// resources.
//
// Deprecated: Marked as deprecated in google/monitoring/v3/uptime.proto.
type InternalChecker struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// A unique resource name for this InternalChecker. The format is:
	//
	//     projects/[PROJECT_ID_OR_NUMBER]/internalCheckers/[INTERNAL_CHECKER_ID]
	//
	// `[PROJECT_ID_OR_NUMBER]` is the Cloud Monitoring Metrics Scope project for
	// the Uptime check config associated with the internal checker.
	Name string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	// The checker's human-readable name. The display name
	// should be unique within a Cloud Monitoring Metrics Scope in order to make
	// it easier to identify; however, uniqueness is not enforced.
	DisplayName string `protobuf:"bytes,2,opt,name=display_name,json=displayName,proto3" json:"display_name,omitempty"`
	// The [GCP VPC network](https://cloud.google.com/vpc/docs/vpc) where the
	// internal resource lives (ex: "default").
	Network string `protobuf:"bytes,3,opt,name=network,proto3" json:"network,omitempty"`
	// The GCP zone the Uptime check should egress from. Only respected for
	// internal Uptime checks, where internal_network is specified.
	GcpZone string `protobuf:"bytes,4,opt,name=gcp_zone,json=gcpZone,proto3" json:"gcp_zone,omitempty"`
	// The GCP project ID where the internal checker lives. Not necessary
	// the same as the Metrics Scope project.
	PeerProjectId string `protobuf:"bytes,6,opt,name=peer_project_id,json=peerProjectId,proto3" json:"peer_project_id,omitempty"`
	// The current operational state of the internal checker.
	State InternalChecker_State `protobuf:"varint,7,opt,name=state,proto3,enum=google.monitoring.v3.InternalChecker_State" json:"state,omitempty"`
}

func (x *InternalChecker) Reset() {
	*x = InternalChecker{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_monitoring_v3_uptime_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *InternalChecker) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*InternalChecker) ProtoMessage() {}

func (x *InternalChecker) ProtoReflect() protoreflect.Message {
	mi := &file_google_monitoring_v3_uptime_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use InternalChecker.ProtoReflect.Descriptor instead.
func (*InternalChecker) Descriptor() ([]byte, []int) {
	return file_google_monitoring_v3_uptime_proto_rawDescGZIP(), []int{0}
}

func (x *InternalChecker) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *InternalChecker) GetDisplayName() string {
	if x != nil {
		return x.DisplayName
	}
	return ""
}

func (x *InternalChecker) GetNetwork() string {
	if x != nil {
		return x.Network
	}
	return ""
}

func (x *InternalChecker) GetGcpZone() string {
	if x != nil {
		return x.GcpZone
	}
	return ""
}

func (x *InternalChecker) GetPeerProjectId() string {
	if x != nil {
		return x.PeerProjectId
	}
	return ""
}

func (x *InternalChecker) GetState() InternalChecker_State {
	if x != nil {
		return x.State
	}
	return InternalChecker_UNSPECIFIED
}

// This message configures which resources and services to monitor for
// availability.
type UptimeCheckConfig struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// A unique resource name for this Uptime check configuration. The format is:
	//
	//      projects/[PROJECT_ID_OR_NUMBER]/uptimeCheckConfigs/[UPTIME_CHECK_ID]
	//
	// `[PROJECT_ID_OR_NUMBER]` is the Workspace host project associated with the
	// Uptime check.
	//
	// This field should be omitted when creating the Uptime check configuration;
	// on create, the resource name is assigned by the server and included in the
	// response.
	Name string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	// A human-friendly name for the Uptime check configuration. The display name
	// should be unique within a Cloud Monitoring Workspace in order to make it
	// easier to identify; however, uniqueness is not enforced. Required.
	DisplayName string `protobuf:"bytes,2,opt,name=display_name,json=displayName,proto3" json:"display_name,omitempty"`
	// The resource the check is checking. Required.
	//
	// Types that are assignable to Resource:
	//	*UptimeCheckConfig_MonitoredResource
	//	*UptimeCheckConfig_ResourceGroup_
	Resource isUptimeCheckConfig_Resource `protobuf_oneof:"resource"`
	// The type of Uptime check request.
	//
	// Types that are assignable to CheckRequestType:
	//	*UptimeCheckConfig_HttpCheck_
	//	*UptimeCheckConfig_TcpCheck_
	CheckRequestType isUptimeCheckConfig_CheckRequestType `protobuf_oneof:"check_request_type"`
	// How often, in seconds, the Uptime check is performed.
	// Currently, the only supported values are `60s` (1 minute), `300s`
	// (5 minutes), `600s` (10 minutes), and `900s` (15 minutes). Optional,
	// defaults to `60s`.
	Period *durationpb.Duration `protobuf:"bytes,7,opt,name=period,proto3" json:"period,omitempty"`
	// The maximum amount of time to wait for the request to complete (must be
	// between 1 and 60 seconds). Required.
	Timeout *durationpb.Duration `protobuf:"bytes,8,opt,name=timeout,proto3" json:"timeout,omitempty"`
	// The content that is expected to appear in the data returned by the target
	// server against which the check is run.  Currently, only the first entry
	// in the `content_matchers` list is supported, and additional entries will
	// be ignored. This field is optional and should only be specified if a
	// content match is required as part of the/ Uptime check.
	ContentMatchers []*UptimeCheckConfig_ContentMatcher `protobuf:"bytes,9,rep,name=content_matchers,json=contentMatchers,proto3" json:"content_matchers,omitempty"`
	// The type of checkers to use to execute the Uptime check.
	CheckerType UptimeCheckConfig_CheckerType `protobuf:"varint,17,opt,name=checker_type,json=checkerType,proto3,enum=google.monitoring.v3.UptimeCheckConfig_CheckerType" json:"checker_type,omitempty"`
	// The list of regions from which the check will be run.
	// Some regions contain one location, and others contain more than one.
	// If this field is specified, enough regions must be provided to include a
	// minimum of 3 locations.  Not specifying this field will result in Uptime
	// checks running from all available regions.
	SelectedRegions []UptimeCheckRegion `protobuf:"varint,10,rep,packed,name=selected_regions,json=selectedRegions,proto3,enum=google.monitoring.v3.UptimeCheckRegion" json:"selected_regions,omitempty"`
	// If this is `true`, then checks are made only from the 'internal_checkers'.
	// If it is `false`, then checks are made only from the 'selected_regions'.
	// It is an error to provide 'selected_regions' when is_internal is `true`,
	// or to provide 'internal_checkers' when is_internal is `false`.
	//
	// Deprecated: Marked as deprecated in google/monitoring/v3/uptime.proto.
	IsInternal bool `protobuf:"varint,15,opt,name=is_internal,json=isInternal,proto3" json:"is_internal,omitempty"`
	// The internal checkers that this check will egress from. If `is_internal` is
	// `true` and this list is empty, the check will egress from all the
	// InternalCheckers configured for the project that owns this
	// `UptimeCheckConfig`.
	//
	// Deprecated: Marked as deprecated in google/monitoring/v3/uptime.proto.
	InternalCheckers []*InternalChecker `protobuf:"bytes,14,rep,name=internal_checkers,json=internalCheckers,proto3" json:"internal_checkers,omitempty"`
	// User-supplied key/value data to be used for organizing and
	// identifying the `UptimeCheckConfig` objects.
	//
	// The field can contain up to 64 entries. Each key and value is limited to
	// 63 Unicode characters or 128 bytes, whichever is smaller. Labels and
	// values can contain only lowercase letters, numerals, underscores, and
	// dashes. Keys must begin with a letter.
	UserLabels map[string]string `protobuf:"bytes,20,rep,name=user_labels,json=userLabels,proto3" json:"user_labels,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
}

func (x *UptimeCheckConfig) Reset() {
	*x = UptimeCheckConfig{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_monitoring_v3_uptime_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UptimeCheckConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UptimeCheckConfig) ProtoMessage() {}

func (x *UptimeCheckConfig) ProtoReflect() protoreflect.Message {
	mi := &file_google_monitoring_v3_uptime_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UptimeCheckConfig.ProtoReflect.Descriptor instead.
func (*UptimeCheckConfig) Descriptor() ([]byte, []int) {
	return file_google_monitoring_v3_uptime_proto_rawDescGZIP(), []int{1}
}

func (x *UptimeCheckConfig) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *UptimeCheckConfig) GetDisplayName() string {
	if x != nil {
		return x.DisplayName
	}
	return ""
}

func (m *UptimeCheckConfig) GetResource() isUptimeCheckConfig_Resource {
	if m != nil {
		return m.Resource
	}
	return nil
}

func (x *UptimeCheckConfig) GetMonitoredResource() *monitoredres.MonitoredResource {
	if x, ok := x.GetResource().(*UptimeCheckConfig_MonitoredResource); ok {
		return x.MonitoredResource
	}
	return nil
}

func (x *UptimeCheckConfig) GetResourceGroup() *UptimeCheckConfig_ResourceGroup {
	if x, ok := x.GetResource().(*UptimeCheckConfig_ResourceGroup_); ok {
		return x.ResourceGroup
	}
	return nil
}

func (m *UptimeCheckConfig) GetCheckRequestType() isUptimeCheckConfig_CheckRequestType {
	if m != nil {
		return m.CheckRequestType
	}
	return nil
}

func (x *UptimeCheckConfig) GetHttpCheck() *UptimeCheckConfig_HttpCheck {
	if x, ok := x.GetCheckRequestType().(*UptimeCheckConfig_HttpCheck_); ok {
		return x.HttpCheck
	}
	return nil
}

func (x *UptimeCheckConfig) GetTcpCheck() *UptimeCheckConfig_TcpCheck {
	if x, ok := x.GetCheckRequestType().(*UptimeCheckConfig_TcpCheck_); ok {
		return x.TcpCheck
	}
	return nil
}

func (x *UptimeCheckConfig) GetPeriod() *durationpb.Duration {
	if x != nil {
		return x.Period
	}
	return nil
}

func (x *UptimeCheckConfig) GetTimeout() *durationpb.Duration {
	if x != nil {
		return x.Timeout
	}
	return nil
}

func (x *UptimeCheckConfig) GetContentMatchers() []*UptimeCheckConfig_ContentMatcher {
	if x != nil {
		return x.ContentMatchers
	}
	return nil
}

func (x *UptimeCheckConfig) GetCheckerType() UptimeCheckConfig_CheckerType {
	if x != nil {
		return x.CheckerType
	}
	return UptimeCheckConfig_CHECKER_TYPE_UNSPECIFIED
}

func (x *UptimeCheckConfig) GetSelectedRegions() []UptimeCheckRegion {
	if x != nil {
		return x.SelectedRegions
	}
	return nil
}

// Deprecated: Marked as deprecated in google/monitoring/v3/uptime.proto.
func (x *UptimeCheckConfig) GetIsInternal() bool {
	if x != nil {
		return x.IsInternal
	}
	return false
}

// Deprecated: Marked as deprecated in google/monitoring/v3/uptime.proto.
func (x *UptimeCheckConfig) GetInternalCheckers() []*InternalChecker {
	if x != nil {
		return x.InternalCheckers
	}
	return nil
}

func (x *UptimeCheckConfig) GetUserLabels() map[string]string {
	if x != nil {
		return x.UserLabels
	}
	return nil
}

type isUptimeCheckConfig_Resource interface {
	isUptimeCheckConfig_Resource()
}

type UptimeCheckConfig_MonitoredResource struct {
	// The [monitored
	// resource](https://cloud.google.com/monitoring/api/resources) associated
	// with the configuration.
	// The following monitored resource types are valid for this field:
	//   `uptime_url`,
	//   `gce_instance`,
	//   `gae_app`,
	//   `aws_ec2_instance`,
	//   `aws_elb_load_balancer`
	//   `k8s_service`
	//   `servicedirectory_service`
	//   `cloud_run_revision`
	MonitoredResource *monitoredres.MonitoredResource `protobuf:"bytes,3,opt,name=monitored_resource,json=monitoredResource,proto3,oneof"`
}

type UptimeCheckConfig_ResourceGroup_ struct {
	// The group resource associated with the configuration.
	ResourceGroup *UptimeCheckConfig_ResourceGroup `protobuf:"bytes,4,opt,name=resource_group,json=resourceGroup,proto3,oneof"`
}

func (*UptimeCheckConfig_MonitoredResource) isUptimeCheckConfig_Resource() {}

func (*UptimeCheckConfig_ResourceGroup_) isUptimeCheckConfig_Resource() {}

type isUptimeCheckConfig_CheckRequestType interface {
	isUptimeCheckConfig_CheckRequestType()
}

type UptimeCheckConfig_HttpCheck_ struct {
	// Contains information needed to make an HTTP or HTTPS check.
	HttpCheck *UptimeCheckConfig_HttpCheck `protobuf:"bytes,5,opt,name=http_check,json=httpCheck,proto3,oneof"`
}

type UptimeCheckConfig_TcpCheck_ struct {
	// Contains information needed to make a TCP check.
	TcpCheck *UptimeCheckConfig_TcpCheck `protobuf:"bytes,6,opt,name=tcp_check,json=tcpCheck,proto3,oneof"`
}

func (*UptimeCheckConfig_HttpCheck_) isUptimeCheckConfig_CheckRequestType() {}

func (*UptimeCheckConfig_TcpCheck_) isUptimeCheckConfig_CheckRequestType() {}

// Contains the region, location, and list of IP
// addresses where checkers in the location run from.
type UptimeCheckIp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// A broad region category in which the IP address is located.
	Region UptimeCheckRegion `protobuf:"varint,1,opt,name=region,proto3,enum=google.monitoring.v3.UptimeCheckRegion" json:"region,omitempty"`
	// A more specific location within the region that typically encodes
	// a particular city/town/metro (and its containing state/province or country)
	// within the broader umbrella region category.
	Location string `protobuf:"bytes,2,opt,name=location,proto3" json:"location,omitempty"`
	// The IP address from which the Uptime check originates. This is a fully
	// specified IP address (not an IP address range). Most IP addresses, as of
	// this publication, are in IPv4 format; however, one should not rely on the
	// IP addresses being in IPv4 format indefinitely, and should support
	// interpreting this field in either IPv4 or IPv6 format.
	IpAddress string `protobuf:"bytes,3,opt,name=ip_address,json=ipAddress,proto3" json:"ip_address,omitempty"`
}

func (x *UptimeCheckIp) Reset() {
	*x = UptimeCheckIp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_monitoring_v3_uptime_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UptimeCheckIp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UptimeCheckIp) ProtoMessage() {}

func (x *UptimeCheckIp) ProtoReflect() protoreflect.Message {
	mi := &file_google_monitoring_v3_uptime_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UptimeCheckIp.ProtoReflect.Descriptor instead.
func (*UptimeCheckIp) Descriptor() ([]byte, []int) {
	return file_google_monitoring_v3_uptime_proto_rawDescGZIP(), []int{2}
}

func (x *UptimeCheckIp) GetRegion() UptimeCheckRegion {
	if x != nil {
		return x.Region
	}
	return UptimeCheckRegion_REGION_UNSPECIFIED
}

func (x *UptimeCheckIp) GetLocation() string {
	if x != nil {
		return x.Location
	}
	return ""
}

func (x *UptimeCheckIp) GetIpAddress() string {
	if x != nil {
		return x.IpAddress
	}
	return ""
}

// The resource submessage for group checks. It can be used instead of a
// monitored resource, when multiple resources are being monitored.
type UptimeCheckConfig_ResourceGroup struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The group of resources being monitored. Should be only the `[GROUP_ID]`,
	// and not the full-path
	// `projects/[PROJECT_ID_OR_NUMBER]/groups/[GROUP_ID]`.
	GroupId string `protobuf:"bytes,1,opt,name=group_id,json=groupId,proto3" json:"group_id,omitempty"`
	// The resource type of the group members.
	ResourceType GroupResourceType `protobuf:"varint,2,opt,name=resource_type,json=resourceType,proto3,enum=google.monitoring.v3.GroupResourceType" json:"resource_type,omitempty"`
}

func (x *UptimeCheckConfig_ResourceGroup) Reset() {
	*x = UptimeCheckConfig_ResourceGroup{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_monitoring_v3_uptime_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UptimeCheckConfig_ResourceGroup) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UptimeCheckConfig_ResourceGroup) ProtoMessage() {}

func (x *UptimeCheckConfig_ResourceGroup) ProtoReflect() protoreflect.Message {
	mi := &file_google_monitoring_v3_uptime_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UptimeCheckConfig_ResourceGroup.ProtoReflect.Descriptor instead.
func (*UptimeCheckConfig_ResourceGroup) Descriptor() ([]byte, []int) {
	return file_google_monitoring_v3_uptime_proto_rawDescGZIP(), []int{1, 0}
}

func (x *UptimeCheckConfig_ResourceGroup) GetGroupId() string {
	if x != nil {
		return x.GroupId
	}
	return ""
}

func (x *UptimeCheckConfig_ResourceGroup) GetResourceType() GroupResourceType {
	if x != nil {
		return x.ResourceType
	}
	return GroupResourceType_RESOURCE_TYPE_UNSPECIFIED
}

// Information involved in sending ICMP pings alongside public HTTP/TCP
// checks. For HTTP, the pings are performed for each part of the redirect
// chain.
type UptimeCheckConfig_PingConfig struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Number of ICMP pings. A maximum of 3 ICMP pings is currently supported.
	PingsCount int32 `protobuf:"varint,1,opt,name=pings_count,json=pingsCount,proto3" json:"pings_count,omitempty"`
}

func (x *UptimeCheckConfig_PingConfig) Reset() {
	*x = UptimeCheckConfig_PingConfig{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_monitoring_v3_uptime_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UptimeCheckConfig_PingConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UptimeCheckConfig_PingConfig) ProtoMessage() {}

func (x *UptimeCheckConfig_PingConfig) ProtoReflect() protoreflect.Message {
	mi := &file_google_monitoring_v3_uptime_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UptimeCheckConfig_PingConfig.ProtoReflect.Descriptor instead.
func (*UptimeCheckConfig_PingConfig) Descriptor() ([]byte, []int) {
	return file_google_monitoring_v3_uptime_proto_rawDescGZIP(), []int{1, 1}
}

func (x *UptimeCheckConfig_PingConfig) GetPingsCount() int32 {
	if x != nil {
		return x.PingsCount
	}
	return 0
}

// Information involved in an HTTP/HTTPS Uptime check request.
type UptimeCheckConfig_HttpCheck struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The HTTP request method to use for the check. If set to
	// `METHOD_UNSPECIFIED` then `request_method` defaults to `GET`.
	RequestMethod UptimeCheckConfig_HttpCheck_RequestMethod `protobuf:"varint,8,opt,name=request_method,json=requestMethod,proto3,enum=google.monitoring.v3.UptimeCheckConfig_HttpCheck_RequestMethod" json:"request_method,omitempty"`
	// If `true`, use HTTPS instead of HTTP to run the check.
	UseSsl bool `protobuf:"varint,1,opt,name=use_ssl,json=useSsl,proto3" json:"use_ssl,omitempty"`
	// Optional (defaults to "/"). The path to the page against which to run
	// the check. Will be combined with the `host` (specified within the
	// `monitored_resource`) and `port` to construct the full URL. If the
	// provided path does not begin with "/", a "/" will be prepended
	// automatically.
	Path string `protobuf:"bytes,2,opt,name=path,proto3" json:"path,omitempty"`
	// Optional (defaults to 80 when `use_ssl` is `false`, and 443 when
	// `use_ssl` is `true`). The TCP port on the HTTP server against which to
	// run the check. Will be combined with host (specified within the
	// `monitored_resource`) and `path` to construct the full URL.
	Port int32 `protobuf:"varint,3,opt,name=port,proto3" json:"port,omitempty"`
	// The authentication information. Optional when creating an HTTP check;
	// defaults to empty.
	AuthInfo *UptimeCheckConfig_HttpCheck_BasicAuthentication `protobuf:"bytes,4,opt,name=auth_info,json=authInfo,proto3" json:"auth_info,omitempty"`
	// Boolean specifying whether to encrypt the header information.
	// Encryption should be specified for any headers related to authentication
	// that you do not wish to be seen when retrieving the configuration. The
	// server will be responsible for encrypting the headers.
	// On Get/List calls, if `mask_headers` is set to `true` then the headers
	// will be obscured with `******.`
	MaskHeaders bool `protobuf:"varint,5,opt,name=mask_headers,json=maskHeaders,proto3" json:"mask_headers,omitempty"`
	// The list of headers to send as part of the Uptime check request.
	// If two headers have the same key and different values, they should
	// be entered as a single header, with the value being a comma-separated
	// list of all the desired values as described at
	// https://www.w3.org/Protocols/rfc2616/rfc2616.txt (page 31).
	// Entering two separate headers with the same key in a Create call will
	// cause the first to be overwritten by the second.
	// The maximum number of headers allowed is 100.
	Headers map[string]string `protobuf:"bytes,6,rep,name=headers,proto3" json:"headers,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	// The content type header to use for the check. The following
	// configurations result in errors:
	// 1. Content type is specified in both the `headers` field and the
	// `content_type` field.
	// 2. Request method is `GET` and `content_type` is not `TYPE_UNSPECIFIED`
	// 3. Request method is `POST` and `content_type` is `TYPE_UNSPECIFIED`.
	// 4. Request method is `POST` and a "Content-Type" header is provided via
	// `headers` field. The `content_type` field should be used instead.
	ContentType UptimeCheckConfig_HttpCheck_ContentType `protobuf:"varint,9,opt,name=content_type,json=contentType,proto3,enum=google.monitoring.v3.UptimeCheckConfig_HttpCheck_ContentType" json:"content_type,omitempty"`
	// A user provided content type header to use for the check. The invalid
	// configurations outlined in the `content_type` field apply to
	// `custom_content_type`, as well as the following:
	// 1. `content_type` is `URL_ENCODED` and `custom_content_type` is set.
	// 2. `content_type` is `USER_PROVIDED` and `custom_content_type` is not
	// set.
	CustomContentType string `protobuf:"bytes,13,opt,name=custom_content_type,json=customContentType,proto3" json:"custom_content_type,omitempty"`
	// Boolean specifying whether to include SSL certificate validation as a
	// part of the Uptime check. Only applies to checks where
	// `monitored_resource` is set to `uptime_url`. If `use_ssl` is `false`,
	// setting `validate_ssl` to `true` has no effect.
	ValidateSsl bool `protobuf:"varint,7,opt,name=validate_ssl,json=validateSsl,proto3" json:"validate_ssl,omitempty"`
	// The request body associated with the HTTP POST request. If `content_type`
	// is `URL_ENCODED`, the body passed in must be URL-encoded. Users can
	// provide a `Content-Length` header via the `headers` field or the API will
	// do so. If the `request_method` is `GET` and `body` is not empty, the API
	// will return an error. The maximum byte size is 1 megabyte.
	//
	// Note: If client libraries aren't used (which performs the conversion
	// automatically) base64 encode your `body` data since the field is of
	// `bytes` type.
	Body []byte `protobuf:"bytes,10,opt,name=body,proto3" json:"body,omitempty"`
	// If present, the check will only pass if the HTTP response status code is
	// in this set of status codes. If empty, the HTTP status code will only
	// pass if the HTTP status code is 200-299.
	AcceptedResponseStatusCodes []*UptimeCheckConfig_HttpCheck_ResponseStatusCode `protobuf:"bytes,11,rep,name=accepted_response_status_codes,json=acceptedResponseStatusCodes,proto3" json:"accepted_response_status_codes,omitempty"`
	// Contains information needed to add pings to an HTTP check.
	PingConfig *UptimeCheckConfig_PingConfig `protobuf:"bytes,12,opt,name=ping_config,json=pingConfig,proto3" json:"ping_config,omitempty"`
}

func (x *UptimeCheckConfig_HttpCheck) Reset() {
	*x = UptimeCheckConfig_HttpCheck{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_monitoring_v3_uptime_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UptimeCheckConfig_HttpCheck) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UptimeCheckConfig_HttpCheck) ProtoMessage() {}

func (x *UptimeCheckConfig_HttpCheck) ProtoReflect() protoreflect.Message {
	mi := &file_google_monitoring_v3_uptime_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UptimeCheckConfig_HttpCheck.ProtoReflect.Descriptor instead.
func (*UptimeCheckConfig_HttpCheck) Descriptor() ([]byte, []int) {
	return file_google_monitoring_v3_uptime_proto_rawDescGZIP(), []int{1, 2}
}

func (x *UptimeCheckConfig_HttpCheck) GetRequestMethod() UptimeCheckConfig_HttpCheck_RequestMethod {
	if x != nil {
		return x.RequestMethod
	}
	return UptimeCheckConfig_HttpCheck_METHOD_UNSPECIFIED
}

func (x *UptimeCheckConfig_HttpCheck) GetUseSsl() bool {
	if x != nil {
		return x.UseSsl
	}
	return false
}

func (x *UptimeCheckConfig_HttpCheck) GetPath() string {
	if x != nil {
		return x.Path
	}
	return ""
}

func (x *UptimeCheckConfig_HttpCheck) GetPort() int32 {
	if x != nil {
		return x.Port
	}
	return 0
}

func (x *UptimeCheckConfig_HttpCheck) GetAuthInfo() *UptimeCheckConfig_HttpCheck_BasicAuthentication {
	if x != nil {
		return x.AuthInfo
	}
	return nil
}

func (x *UptimeCheckConfig_HttpCheck) GetMaskHeaders() bool {
	if x != nil {
		return x.MaskHeaders
	}
	return false
}

func (x *UptimeCheckConfig_HttpCheck) GetHeaders() map[string]string {
	if x != nil {
		return x.Headers
	}
	return nil
}

func (x *UptimeCheckConfig_HttpCheck) GetContentType() UptimeCheckConfig_HttpCheck_ContentType {
	if x != nil {
		return x.ContentType
	}
	return UptimeCheckConfig_HttpCheck_TYPE_UNSPECIFIED
}

func (x *UptimeCheckConfig_HttpCheck) GetCustomContentType() string {
	if x != nil {
		return x.CustomContentType
	}
	return ""
}

func (x *UptimeCheckConfig_HttpCheck) GetValidateSsl() bool {
	if x != nil {
		return x.ValidateSsl
	}
	return false
}

func (x *UptimeCheckConfig_HttpCheck) GetBody() []byte {
	if x != nil {
		return x.Body
	}
	return nil
}

func (x *UptimeCheckConfig_HttpCheck) GetAcceptedResponseStatusCodes() []*UptimeCheckConfig_HttpCheck_ResponseStatusCode {
	if x != nil {
		return x.AcceptedResponseStatusCodes
	}
	return nil
}

func (x *UptimeCheckConfig_HttpCheck) GetPingConfig() *UptimeCheckConfig_PingConfig {
	if x != nil {
		return x.PingConfig
	}
	return nil
}

// Information required for a TCP Uptime check request.
type UptimeCheckConfig_TcpCheck struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The TCP port on the server against which to run the check. Will be
	// combined with host (specified within the `monitored_resource`) to
	// construct the full URL. Required.
	Port int32 `protobuf:"varint,1,opt,name=port,proto3" json:"port,omitempty"`
	// Contains information needed to add pings to a TCP check.
	PingConfig *UptimeCheckConfig_PingConfig `protobuf:"bytes,2,opt,name=ping_config,json=pingConfig,proto3" json:"ping_config,omitempty"`
}

func (x *UptimeCheckConfig_TcpCheck) Reset() {
	*x = UptimeCheckConfig_TcpCheck{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_monitoring_v3_uptime_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UptimeCheckConfig_TcpCheck) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UptimeCheckConfig_TcpCheck) ProtoMessage() {}

func (x *UptimeCheckConfig_TcpCheck) ProtoReflect() protoreflect.Message {
	mi := &file_google_monitoring_v3_uptime_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UptimeCheckConfig_TcpCheck.ProtoReflect.Descriptor instead.
func (*UptimeCheckConfig_TcpCheck) Descriptor() ([]byte, []int) {
	return file_google_monitoring_v3_uptime_proto_rawDescGZIP(), []int{1, 3}
}

func (x *UptimeCheckConfig_TcpCheck) GetPort() int32 {
	if x != nil {
		return x.Port
	}
	return 0
}

func (x *UptimeCheckConfig_TcpCheck) GetPingConfig() *UptimeCheckConfig_PingConfig {
	if x != nil {
		return x.PingConfig
	}
	return nil
}

// Optional. Used to perform content matching. This allows matching based on
// substrings and regular expressions, together with their negations. Only the
// first 4&nbsp;MB of an HTTP or HTTPS check's response (and the first
// 1&nbsp;MB of a TCP check's response) are examined for purposes of content
// matching.
type UptimeCheckConfig_ContentMatcher struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// String, regex or JSON content to match. Maximum 1024 bytes. An empty
	// `content` string indicates no content matching is to be performed.
	Content string `protobuf:"bytes,1,opt,name=content,proto3" json:"content,omitempty"`
	// The type of content matcher that will be applied to the server output,
	// compared to the `content` string when the check is run.
	Matcher UptimeCheckConfig_ContentMatcher_ContentMatcherOption `protobuf:"varint,2,opt,name=matcher,proto3,enum=google.monitoring.v3.UptimeCheckConfig_ContentMatcher_ContentMatcherOption" json:"matcher,omitempty"`
	// Certain `ContentMatcherOption` types require additional information.
	// `MATCHES_JSON_PATH` or `NOT_MATCHES_JSON_PATH` require a
	// `JsonPathMatcher`; not used for other options.
	//
	// Types that are assignable to AdditionalMatcherInfo:
	//	*UptimeCheckConfig_ContentMatcher_JsonPathMatcher_
	AdditionalMatcherInfo isUptimeCheckConfig_ContentMatcher_AdditionalMatcherInfo `protobuf_oneof:"additional_matcher_info"`
}

func (x *UptimeCheckConfig_ContentMatcher) Reset() {
	*x = UptimeCheckConfig_ContentMatcher{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_monitoring_v3_uptime_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UptimeCheckConfig_ContentMatcher) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UptimeCheckConfig_ContentMatcher) ProtoMessage() {}

func (x *UptimeCheckConfig_ContentMatcher) ProtoReflect() protoreflect.Message {
	mi := &file_google_monitoring_v3_uptime_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UptimeCheckConfig_ContentMatcher.ProtoReflect.Descriptor instead.
func (*UptimeCheckConfig_ContentMatcher) Descriptor() ([]byte, []int) {
	return file_google_monitoring_v3_uptime_proto_rawDescGZIP(), []int{1, 4}
}

func (x *UptimeCheckConfig_ContentMatcher) GetContent() string {
	if x != nil {
		return x.Content
	}
	return ""
}

func (x *UptimeCheckConfig_ContentMatcher) GetMatcher() UptimeCheckConfig_ContentMatcher_ContentMatcherOption {
	if x != nil {
		return x.Matcher
	}
	return UptimeCheckConfig_ContentMatcher_CONTENT_MATCHER_OPTION_UNSPECIFIED
}

func (m *UptimeCheckConfig_ContentMatcher) GetAdditionalMatcherInfo() isUptimeCheckConfig_ContentMatcher_AdditionalMatcherInfo {
	if m != nil {
		return m.AdditionalMatcherInfo
	}
	return nil
}

func (x *UptimeCheckConfig_ContentMatcher) GetJsonPathMatcher() *UptimeCheckConfig_ContentMatcher_JsonPathMatcher {
	if x, ok := x.GetAdditionalMatcherInfo().(*UptimeCheckConfig_ContentMatcher_JsonPathMatcher_); ok {
		return x.JsonPathMatcher
	}
	return nil
}

type isUptimeCheckConfig_ContentMatcher_AdditionalMatcherInfo interface {
	isUptimeCheckConfig_ContentMatcher_AdditionalMatcherInfo()
}

type UptimeCheckConfig_ContentMatcher_JsonPathMatcher_ struct {
	// Matcher information for `MATCHES_JSON_PATH` and `NOT_MATCHES_JSON_PATH`
	JsonPathMatcher *UptimeCheckConfig_ContentMatcher_JsonPathMatcher `protobuf:"bytes,3,opt,name=json_path_matcher,json=jsonPathMatcher,proto3,oneof"`
}

func (*UptimeCheckConfig_ContentMatcher_JsonPathMatcher_) isUptimeCheckConfig_ContentMatcher_AdditionalMatcherInfo() {
}

// The authentication parameters to provide to the specified resource or
// URL that requires a username and password. Currently, only
// [Basic HTTP authentication](https://tools.ietf.org/html/rfc7617) is
// supported in Uptime checks.
type UptimeCheckConfig_HttpCheck_BasicAuthentication struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The username to use when authenticating with the HTTP server.
	Username string `protobuf:"bytes,1,opt,name=username,proto3" json:"username,omitempty"`
	// The password to use when authenticating with the HTTP server.
	Password string `protobuf:"bytes,2,opt,name=password,proto3" json:"password,omitempty"`
}

func (x *UptimeCheckConfig_HttpCheck_BasicAuthentication) Reset() {
	*x = UptimeCheckConfig_HttpCheck_BasicAuthentication{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_monitoring_v3_uptime_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UptimeCheckConfig_HttpCheck_BasicAuthentication) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UptimeCheckConfig_HttpCheck_BasicAuthentication) ProtoMessage() {}

func (x *UptimeCheckConfig_HttpCheck_BasicAuthentication) ProtoReflect() protoreflect.Message {
	mi := &file_google_monitoring_v3_uptime_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UptimeCheckConfig_HttpCheck_BasicAuthentication.ProtoReflect.Descriptor instead.
func (*UptimeCheckConfig_HttpCheck_BasicAuthentication) Descriptor() ([]byte, []int) {
	return file_google_monitoring_v3_uptime_proto_rawDescGZIP(), []int{1, 2, 0}
}

func (x *UptimeCheckConfig_HttpCheck_BasicAuthentication) GetUsername() string {
	if x != nil {
		return x.Username
	}
	return ""
}

func (x *UptimeCheckConfig_HttpCheck_BasicAuthentication) GetPassword() string {
	if x != nil {
		return x.Password
	}
	return ""
}

// A status to accept. Either a status code class like "2xx", or an integer
// status code like "200".
type UptimeCheckConfig_HttpCheck_ResponseStatusCode struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Either a specific value or a class of status codes.
	//
	// Types that are assignable to StatusCode:
	//	*UptimeCheckConfig_HttpCheck_ResponseStatusCode_StatusValue
	//	*UptimeCheckConfig_HttpCheck_ResponseStatusCode_StatusClass_
	StatusCode isUptimeCheckConfig_HttpCheck_ResponseStatusCode_StatusCode `protobuf_oneof:"status_code"`
}

func (x *UptimeCheckConfig_HttpCheck_ResponseStatusCode) Reset() {
	*x = UptimeCheckConfig_HttpCheck_ResponseStatusCode{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_monitoring_v3_uptime_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UptimeCheckConfig_HttpCheck_ResponseStatusCode) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UptimeCheckConfig_HttpCheck_ResponseStatusCode) ProtoMessage() {}

func (x *UptimeCheckConfig_HttpCheck_ResponseStatusCode) ProtoReflect() protoreflect.Message {
	mi := &file_google_monitoring_v3_uptime_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UptimeCheckConfig_HttpCheck_ResponseStatusCode.ProtoReflect.Descriptor instead.
func (*UptimeCheckConfig_HttpCheck_ResponseStatusCode) Descriptor() ([]byte, []int) {
	return file_google_monitoring_v3_uptime_proto_rawDescGZIP(), []int{1, 2, 1}
}

func (m *UptimeCheckConfig_HttpCheck_ResponseStatusCode) GetStatusCode() isUptimeCheckConfig_HttpCheck_ResponseStatusCode_StatusCode {
	if m != nil {
		return m.StatusCode
	}
	return nil
}

func (x *UptimeCheckConfig_HttpCheck_ResponseStatusCode) GetStatusValue() int32 {
	if x, ok := x.GetStatusCode().(*UptimeCheckConfig_HttpCheck_ResponseStatusCode_StatusValue); ok {
		return x.StatusValue
	}
	return 0
}

func (x *UptimeCheckConfig_HttpCheck_ResponseStatusCode) GetStatusClass() UptimeCheckConfig_HttpCheck_ResponseStatusCode_StatusClass {
	if x, ok := x.GetStatusCode().(*UptimeCheckConfig_HttpCheck_ResponseStatusCode_StatusClass_); ok {
		return x.StatusClass
	}
	return UptimeCheckConfig_HttpCheck_ResponseStatusCode_STATUS_CLASS_UNSPECIFIED
}

type isUptimeCheckConfig_HttpCheck_ResponseStatusCode_StatusCode interface {
	isUptimeCheckConfig_HttpCheck_ResponseStatusCode_StatusCode()
}

type UptimeCheckConfig_HttpCheck_ResponseStatusCode_StatusValue struct {
	// A status code to accept.
	StatusValue int32 `protobuf:"varint,1,opt,name=status_value,json=statusValue,proto3,oneof"`
}

type UptimeCheckConfig_HttpCheck_ResponseStatusCode_StatusClass_ struct {
	// A class of status codes to accept.
	StatusClass UptimeCheckConfig_HttpCheck_ResponseStatusCode_StatusClass `protobuf:"varint,2,opt,name=status_class,json=statusClass,proto3,enum=google.monitoring.v3.UptimeCheckConfig_HttpCheck_ResponseStatusCode_StatusClass,oneof"`
}

func (*UptimeCheckConfig_HttpCheck_ResponseStatusCode_StatusValue) isUptimeCheckConfig_HttpCheck_ResponseStatusCode_StatusCode() {
}

func (*UptimeCheckConfig_HttpCheck_ResponseStatusCode_StatusClass_) isUptimeCheckConfig_HttpCheck_ResponseStatusCode_StatusCode() {
}

// Information needed to perform a JSONPath content match.
// Used for `ContentMatcherOption::MATCHES_JSON_PATH` and
// `ContentMatcherOption::NOT_MATCHES_JSON_PATH`.
type UptimeCheckConfig_ContentMatcher_JsonPathMatcher struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// JSONPath within the response output pointing to the expected
	// `ContentMatcher::content` to match against.
	JsonPath string `protobuf:"bytes,1,opt,name=json_path,json=jsonPath,proto3" json:"json_path,omitempty"`
	// The type of JSONPath match that will be applied to the JSON output
	// (`ContentMatcher.content`)
	JsonMatcher UptimeCheckConfig_ContentMatcher_JsonPathMatcher_JsonPathMatcherOption `protobuf:"varint,2,opt,name=json_matcher,json=jsonMatcher,proto3,enum=google.monitoring.v3.UptimeCheckConfig_ContentMatcher_JsonPathMatcher_JsonPathMatcherOption" json:"json_matcher,omitempty"`
}

func (x *UptimeCheckConfig_ContentMatcher_JsonPathMatcher) Reset() {
	*x = UptimeCheckConfig_ContentMatcher_JsonPathMatcher{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_monitoring_v3_uptime_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UptimeCheckConfig_ContentMatcher_JsonPathMatcher) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UptimeCheckConfig_ContentMatcher_JsonPathMatcher) ProtoMessage() {}

func (x *UptimeCheckConfig_ContentMatcher_JsonPathMatcher) ProtoReflect() protoreflect.Message {
	mi := &file_google_monitoring_v3_uptime_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UptimeCheckConfig_ContentMatcher_JsonPathMatcher.ProtoReflect.Descriptor instead.
func (*UptimeCheckConfig_ContentMatcher_JsonPathMatcher) Descriptor() ([]byte, []int) {
	return file_google_monitoring_v3_uptime_proto_rawDescGZIP(), []int{1, 4, 0}
}

func (x *UptimeCheckConfig_ContentMatcher_JsonPathMatcher) GetJsonPath() string {
	if x != nil {
		return x.JsonPath
	}
	return ""
}

func (x *UptimeCheckConfig_ContentMatcher_JsonPathMatcher) GetJsonMatcher() UptimeCheckConfig_ContentMatcher_JsonPathMatcher_JsonPathMatcherOption {
	if x != nil {
		return x.JsonMatcher
	}
	return UptimeCheckConfig_ContentMatcher_JsonPathMatcher_JSON_PATH_MATCHER_OPTION_UNSPECIFIED
}

var File_google_monitoring_v3_uptime_proto protoreflect.FileDescriptor

var file_google_monitoring_v3_uptime_proto_rawDesc = []byte{
	0x0a, 0x21, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x6d, 0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72,
	0x69, 0x6e, 0x67, 0x2f, 0x76, 0x33, 0x2f, 0x75, 0x70, 0x74, 0x69, 0x6d, 0x65, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x12, 0x14, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x6d, 0x6f, 0x6e, 0x69,
	0x74, 0x6f, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x33, 0x1a, 0x1f, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x66, 0x69, 0x65, 0x6c, 0x64, 0x5f, 0x62, 0x65, 0x68, 0x61,
	0x76, 0x69, 0x6f, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x23, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x6d, 0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72, 0x65, 0x64,
	0x5f, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a,
	0x19, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x72, 0x65, 0x73, 0x6f,
	0x75, 0x72, 0x63, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1e, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x64, 0x75, 0x72, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xa1, 0x02, 0x0a, 0x0f, 0x49,
	0x6e, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x65, 0x72, 0x12, 0x12,
	0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61,
	0x6d, 0x65, 0x12, 0x21, 0x0a, 0x0c, 0x64, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x5f, 0x6e, 0x61,
	0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x64, 0x69, 0x73, 0x70, 0x6c, 0x61,
	0x79, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x12,
	0x19, 0x0a, 0x08, 0x67, 0x63, 0x70, 0x5f, 0x7a, 0x6f, 0x6e, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x07, 0x67, 0x63, 0x70, 0x5a, 0x6f, 0x6e, 0x65, 0x12, 0x26, 0x0a, 0x0f, 0x70, 0x65,
	0x65, 0x72, 0x5f, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x06, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0d, 0x70, 0x65, 0x65, 0x72, 0x50, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74,
	0x49, 0x64, 0x12, 0x41, 0x0a, 0x05, 0x73, 0x74, 0x61, 0x74, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28,
	0x0e, 0x32, 0x2b, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x6d, 0x6f, 0x6e, 0x69, 0x74,
	0x6f, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x33, 0x2e, 0x49, 0x6e, 0x74, 0x65, 0x72, 0x6e, 0x61,
	0x6c, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x65, 0x72, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x65, 0x52, 0x05,
	0x73, 0x74, 0x61, 0x74, 0x65, 0x22, 0x33, 0x0a, 0x05, 0x53, 0x74, 0x61, 0x74, 0x65, 0x12, 0x0f,
	0x0a, 0x0b, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12,
	0x0c, 0x0a, 0x08, 0x43, 0x52, 0x45, 0x41, 0x54, 0x49, 0x4e, 0x47, 0x10, 0x01, 0x12, 0x0b, 0x0a,
	0x07, 0x52, 0x55, 0x4e, 0x4e, 0x49, 0x4e, 0x47, 0x10, 0x02, 0x3a, 0x02, 0x18, 0x01, 0x22, 0x8b,
	0x1f, 0x0a, 0x11, 0x55, 0x70, 0x74, 0x69, 0x6d, 0x65, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x43, 0x6f,
	0x6e, 0x66, 0x69, 0x67, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x21, 0x0a, 0x0c, 0x64, 0x69, 0x73, 0x70,
	0x6c, 0x61, 0x79, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b,
	0x64, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x4e, 0x0a, 0x12, 0x6d,
	0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72, 0x65, 0x64, 0x5f, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63,
	0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1d, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x4d, 0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72, 0x65, 0x64, 0x52, 0x65,
	0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x48, 0x00, 0x52, 0x11, 0x6d, 0x6f, 0x6e, 0x69, 0x74, 0x6f,
	0x72, 0x65, 0x64, 0x52, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x12, 0x5e, 0x0a, 0x0e, 0x72,
	0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x35, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x6d, 0x6f, 0x6e,
	0x69, 0x74, 0x6f, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x33, 0x2e, 0x55, 0x70, 0x74, 0x69, 0x6d,
	0x65, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2e, 0x52, 0x65, 0x73,
	0x6f, 0x75, 0x72, 0x63, 0x65, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x48, 0x00, 0x52, 0x0d, 0x72, 0x65,
	0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x12, 0x52, 0x0a, 0x0a, 0x68,
	0x74, 0x74, 0x70, 0x5f, 0x63, 0x68, 0x65, 0x63, 0x6b, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x31, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x6d, 0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72,
	0x69, 0x6e, 0x67, 0x2e, 0x76, 0x33, 0x2e, 0x55, 0x70, 0x74, 0x69, 0x6d, 0x65, 0x43, 0x68, 0x65,
	0x63, 0x6b, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2e, 0x48, 0x74, 0x74, 0x70, 0x43, 0x68, 0x65,
	0x63, 0x6b, 0x48, 0x01, 0x52, 0x09, 0x68, 0x74, 0x74, 0x70, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x12,
	0x4f, 0x0a, 0x09, 0x74, 0x63, 0x70, 0x5f, 0x63, 0x68, 0x65, 0x63, 0x6b, 0x18, 0x06, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x30, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x6d, 0x6f, 0x6e, 0x69,
	0x74, 0x6f, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x33, 0x2e, 0x55, 0x70, 0x74, 0x69, 0x6d, 0x65,
	0x43, 0x68, 0x65, 0x63, 0x6b, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2e, 0x54, 0x63, 0x70, 0x43,
	0x68, 0x65, 0x63, 0x6b, 0x48, 0x01, 0x52, 0x08, 0x74, 0x63, 0x70, 0x43, 0x68, 0x65, 0x63, 0x6b,
	0x12, 0x31, 0x0a, 0x06, 0x70, 0x65, 0x72, 0x69, 0x6f, 0x64, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x19, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62,
	0x75, 0x66, 0x2e, 0x44, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x06, 0x70, 0x65, 0x72,
	0x69, 0x6f, 0x64, 0x12, 0x33, 0x0a, 0x07, 0x74, 0x69, 0x6d, 0x65, 0x6f, 0x75, 0x74, 0x18, 0x08,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x44, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52,
	0x07, 0x74, 0x69, 0x6d, 0x65, 0x6f, 0x75, 0x74, 0x12, 0x61, 0x0a, 0x10, 0x63, 0x6f, 0x6e, 0x74,
	0x65, 0x6e, 0x74, 0x5f, 0x6d, 0x61, 0x74, 0x63, 0x68, 0x65, 0x72, 0x73, 0x18, 0x09, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x36, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x6d, 0x6f, 0x6e, 0x69,
	0x74, 0x6f, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x33, 0x2e, 0x55, 0x70, 0x74, 0x69, 0x6d, 0x65,
	0x43, 0x68, 0x65, 0x63, 0x6b, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2e, 0x43, 0x6f, 0x6e, 0x74,
	0x65, 0x6e, 0x74, 0x4d, 0x61, 0x74, 0x63, 0x68, 0x65, 0x72, 0x52, 0x0f, 0x63, 0x6f, 0x6e, 0x74,
	0x65, 0x6e, 0x74, 0x4d, 0x61, 0x74, 0x63, 0x68, 0x65, 0x72, 0x73, 0x12, 0x56, 0x0a, 0x0c, 0x63,
	0x68, 0x65, 0x63, 0x6b, 0x65, 0x72, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x11, 0x20, 0x01, 0x28,
	0x0e, 0x32, 0x33, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x6d, 0x6f, 0x6e, 0x69, 0x74,
	0x6f, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x33, 0x2e, 0x55, 0x70, 0x74, 0x69, 0x6d, 0x65, 0x43,
	0x68, 0x65, 0x63, 0x6b, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2e, 0x43, 0x68, 0x65, 0x63, 0x6b,
	0x65, 0x72, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0b, 0x63, 0x68, 0x65, 0x63, 0x6b, 0x65, 0x72, 0x54,
	0x79, 0x70, 0x65, 0x12, 0x52, 0x0a, 0x10, 0x73, 0x65, 0x6c, 0x65, 0x63, 0x74, 0x65, 0x64, 0x5f,
	0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x0a, 0x20, 0x03, 0x28, 0x0e, 0x32, 0x27, 0x2e,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x6d, 0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72, 0x69, 0x6e,
	0x67, 0x2e, 0x76, 0x33, 0x2e, 0x55, 0x70, 0x74, 0x69, 0x6d, 0x65, 0x43, 0x68, 0x65, 0x63, 0x6b,
	0x52, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x52, 0x0f, 0x73, 0x65, 0x6c, 0x65, 0x63, 0x74, 0x65, 0x64,
	0x52, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x23, 0x0a, 0x0b, 0x69, 0x73, 0x5f, 0x69, 0x6e,
	0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x08, 0x42, 0x02, 0x18, 0x01,
	0x52, 0x0a, 0x69, 0x73, 0x49, 0x6e, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x12, 0x56, 0x0a, 0x11,
	0x69, 0x6e, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x5f, 0x63, 0x68, 0x65, 0x63, 0x6b, 0x65, 0x72,
	0x73, 0x18, 0x0e, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x25, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2e, 0x6d, 0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x33, 0x2e, 0x49,
	0x6e, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x65, 0x72, 0x42, 0x02,
	0x18, 0x01, 0x52, 0x10, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x43, 0x68, 0x65, 0x63,
	0x6b, 0x65, 0x72, 0x73, 0x12, 0x58, 0x0a, 0x0b, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x6c, 0x61, 0x62,
	0x65, 0x6c, 0x73, 0x18, 0x14, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x37, 0x2e, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2e, 0x6d, 0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x33,
	0x2e, 0x55, 0x70, 0x74, 0x69, 0x6d, 0x65, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x43, 0x6f, 0x6e, 0x66,
	0x69, 0x67, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x4c, 0x61, 0x62, 0x65, 0x6c, 0x73, 0x45, 0x6e, 0x74,
	0x72, 0x79, 0x52, 0x0a, 0x75, 0x73, 0x65, 0x72, 0x4c, 0x61, 0x62, 0x65, 0x6c, 0x73, 0x1a, 0x78,
	0x0a, 0x0d, 0x52, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x12,
	0x19, 0x0a, 0x08, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x07, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x49, 0x64, 0x12, 0x4c, 0x0a, 0x0d, 0x72, 0x65,
	0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x0e, 0x32, 0x27, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x6d, 0x6f, 0x6e, 0x69, 0x74,
	0x6f, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x33, 0x2e, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x52, 0x65,
	0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0c, 0x72, 0x65, 0x73, 0x6f,
	0x75, 0x72, 0x63, 0x65, 0x54, 0x79, 0x70, 0x65, 0x1a, 0x2d, 0x0a, 0x0a, 0x50, 0x69, 0x6e, 0x67,
	0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x1f, 0x0a, 0x0b, 0x70, 0x69, 0x6e, 0x67, 0x73, 0x5f,
	0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0a, 0x70, 0x69, 0x6e,
	0x67, 0x73, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x1a, 0xc8, 0x0b, 0x0a, 0x09, 0x48, 0x74, 0x74, 0x70,
	0x43, 0x68, 0x65, 0x63, 0x6b, 0x12, 0x66, 0x0a, 0x0e, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x5f, 0x6d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x3f, 0x2e,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x6d, 0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72, 0x69, 0x6e,
	0x67, 0x2e, 0x76, 0x33, 0x2e, 0x55, 0x70, 0x74, 0x69, 0x6d, 0x65, 0x43, 0x68, 0x65, 0x63, 0x6b,
	0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2e, 0x48, 0x74, 0x74, 0x70, 0x43, 0x68, 0x65, 0x63, 0x6b,
	0x2e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x4d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x52, 0x0d,
	0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x4d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x12, 0x17, 0x0a,
	0x07, 0x75, 0x73, 0x65, 0x5f, 0x73, 0x73, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x06,
	0x75, 0x73, 0x65, 0x53, 0x73, 0x6c, 0x12, 0x12, 0x0a, 0x04, 0x70, 0x61, 0x74, 0x68, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x70, 0x61, 0x74, 0x68, 0x12, 0x12, 0x0a, 0x04, 0x70, 0x6f,
	0x72, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x70, 0x6f, 0x72, 0x74, 0x12, 0x62,
	0x0a, 0x09, 0x61, 0x75, 0x74, 0x68, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x45, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x6d, 0x6f, 0x6e, 0x69, 0x74,
	0x6f, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x33, 0x2e, 0x55, 0x70, 0x74, 0x69, 0x6d, 0x65, 0x43,
	0x68, 0x65, 0x63, 0x6b, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2e, 0x48, 0x74, 0x74, 0x70, 0x43,
	0x68, 0x65, 0x63, 0x6b, 0x2e, 0x42, 0x61, 0x73, 0x69, 0x63, 0x41, 0x75, 0x74, 0x68, 0x65, 0x6e,
	0x74, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x08, 0x61, 0x75, 0x74, 0x68, 0x49, 0x6e,
	0x66, 0x6f, 0x12, 0x21, 0x0a, 0x0c, 0x6d, 0x61, 0x73, 0x6b, 0x5f, 0x68, 0x65, 0x61, 0x64, 0x65,
	0x72, 0x73, 0x18, 0x05, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0b, 0x6d, 0x61, 0x73, 0x6b, 0x48, 0x65,
	0x61, 0x64, 0x65, 0x72, 0x73, 0x12, 0x58, 0x0a, 0x07, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x73,
	0x18, 0x06, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x3e, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e,
	0x6d, 0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x33, 0x2e, 0x55, 0x70,
	0x74, 0x69, 0x6d, 0x65, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2e,
	0x48, 0x74, 0x74, 0x70, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x2e, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72,
	0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x07, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x73, 0x12,
	0x60, 0x0a, 0x0c, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18,
	0x09, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x3d, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x6d,
	0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x33, 0x2e, 0x55, 0x70, 0x74,
	0x69, 0x6d, 0x65, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2e, 0x48,
	0x74, 0x74, 0x70, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x2e, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74,
	0x54, 0x79, 0x70, 0x65, 0x52, 0x0b, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x54, 0x79, 0x70,
	0x65, 0x12, 0x2e, 0x0a, 0x13, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x5f, 0x63, 0x6f, 0x6e, 0x74,
	0x65, 0x6e, 0x74, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x09, 0x52, 0x11,
	0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x54, 0x79, 0x70,
	0x65, 0x12, 0x21, 0x0a, 0x0c, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x5f, 0x73, 0x73,
	0x6c, 0x18, 0x07, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0b, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74,
	0x65, 0x53, 0x73, 0x6c, 0x12, 0x12, 0x0a, 0x04, 0x62, 0x6f, 0x64, 0x79, 0x18, 0x0a, 0x20, 0x01,
	0x28, 0x0c, 0x52, 0x04, 0x62, 0x6f, 0x64, 0x79, 0x12, 0x89, 0x01, 0x0a, 0x1e, 0x61, 0x63, 0x63,
	0x65, 0x70, 0x74, 0x65, 0x64, 0x5f, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x5f, 0x73,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x73, 0x18, 0x0b, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x44, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x6d, 0x6f, 0x6e, 0x69, 0x74,
	0x6f, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x33, 0x2e, 0x55, 0x70, 0x74, 0x69, 0x6d, 0x65, 0x43,
	0x68, 0x65, 0x63, 0x6b, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2e, 0x48, 0x74, 0x74, 0x70, 0x43,
	0x68, 0x65, 0x63, 0x6b, 0x2e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x53, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x43, 0x6f, 0x64, 0x65, 0x52, 0x1b, 0x61, 0x63, 0x63, 0x65, 0x70, 0x74, 0x65,
	0x64, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x43,
	0x6f, 0x64, 0x65, 0x73, 0x12, 0x53, 0x0a, 0x0b, 0x70, 0x69, 0x6e, 0x67, 0x5f, 0x63, 0x6f, 0x6e,
	0x66, 0x69, 0x67, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x32, 0x2e, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2e, 0x6d, 0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x33,
	0x2e, 0x55, 0x70, 0x74, 0x69, 0x6d, 0x65, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x43, 0x6f, 0x6e, 0x66,
	0x69, 0x67, 0x2e, 0x50, 0x69, 0x6e, 0x67, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x0a, 0x70,
	0x69, 0x6e, 0x67, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x1a, 0x4d, 0x0a, 0x13, 0x42, 0x61, 0x73,
	0x69, 0x63, 0x41, 0x75, 0x74, 0x68, 0x65, 0x6e, 0x74, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x12, 0x1a, 0x0a, 0x08, 0x75, 0x73, 0x65, 0x72, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x08, 0x75, 0x73, 0x65, 0x72, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x1a, 0x0a, 0x08,
	0x70, 0x61, 0x73, 0x73, 0x77, 0x6f, 0x72, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08,
	0x70, 0x61, 0x73, 0x73, 0x77, 0x6f, 0x72, 0x64, 0x1a, 0xf6, 0x02, 0x0a, 0x12, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x43, 0x6f, 0x64, 0x65, 0x12,
	0x23, 0x0a, 0x0c, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x5f, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x05, 0x48, 0x00, 0x52, 0x0b, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x56,
	0x61, 0x6c, 0x75, 0x65, 0x12, 0x75, 0x0a, 0x0c, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x5f, 0x63,
	0x6c, 0x61, 0x73, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x50, 0x2e, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2e, 0x6d, 0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76,
	0x33, 0x2e, 0x55, 0x70, 0x74, 0x69, 0x6d, 0x65, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x43, 0x6f, 0x6e,
	0x66, 0x69, 0x67, 0x2e, 0x48, 0x74, 0x74, 0x70, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x2e, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x43, 0x6f, 0x64, 0x65,
	0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x43, 0x6c, 0x61, 0x73, 0x73, 0x48, 0x00, 0x52, 0x0b,
	0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x43, 0x6c, 0x61, 0x73, 0x73, 0x22, 0xb4, 0x01, 0x0a, 0x0b,
	0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x43, 0x6c, 0x61, 0x73, 0x73, 0x12, 0x1c, 0x0a, 0x18, 0x53,
	0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x43, 0x4c, 0x41, 0x53, 0x53, 0x5f, 0x55, 0x4e, 0x53, 0x50,
	0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x14, 0x0a, 0x10, 0x53, 0x54, 0x41,
	0x54, 0x55, 0x53, 0x5f, 0x43, 0x4c, 0x41, 0x53, 0x53, 0x5f, 0x31, 0x58, 0x58, 0x10, 0x64, 0x12,
	0x15, 0x0a, 0x10, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x43, 0x4c, 0x41, 0x53, 0x53, 0x5f,
	0x32, 0x58, 0x58, 0x10, 0xc8, 0x01, 0x12, 0x15, 0x0a, 0x10, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53,
	0x5f, 0x43, 0x4c, 0x41, 0x53, 0x53, 0x5f, 0x33, 0x58, 0x58, 0x10, 0xac, 0x02, 0x12, 0x15, 0x0a,
	0x10, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x43, 0x4c, 0x41, 0x53, 0x53, 0x5f, 0x34, 0x58,
	0x58, 0x10, 0x90, 0x03, 0x12, 0x15, 0x0a, 0x10, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x43,
	0x4c, 0x41, 0x53, 0x53, 0x5f, 0x35, 0x58, 0x58, 0x10, 0xf4, 0x03, 0x12, 0x15, 0x0a, 0x10, 0x53,
	0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x43, 0x4c, 0x41, 0x53, 0x53, 0x5f, 0x41, 0x4e, 0x59, 0x10,
	0xe8, 0x07, 0x42, 0x0d, 0x0a, 0x0b, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x5f, 0x63, 0x6f, 0x64,
	0x65, 0x1a, 0x3a, 0x0a, 0x0c, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x73, 0x45, 0x6e, 0x74, 0x72,
	0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03,
	0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0x3a, 0x0a,
	0x0d, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x4d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x12, 0x16,
	0x0a, 0x12, 0x4d, 0x45, 0x54, 0x48, 0x4f, 0x44, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49,
	0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x07, 0x0a, 0x03, 0x47, 0x45, 0x54, 0x10, 0x01, 0x12,
	0x08, 0x0a, 0x04, 0x50, 0x4f, 0x53, 0x54, 0x10, 0x02, 0x22, 0x47, 0x0a, 0x0b, 0x43, 0x6f, 0x6e,
	0x74, 0x65, 0x6e, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x14, 0x0a, 0x10, 0x54, 0x59, 0x50, 0x45,
	0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x0f,
	0x0a, 0x0b, 0x55, 0x52, 0x4c, 0x5f, 0x45, 0x4e, 0x43, 0x4f, 0x44, 0x45, 0x44, 0x10, 0x01, 0x12,
	0x11, 0x0a, 0x0d, 0x55, 0x53, 0x45, 0x52, 0x5f, 0x50, 0x52, 0x4f, 0x56, 0x49, 0x44, 0x45, 0x44,
	0x10, 0x02, 0x1a, 0x73, 0x0a, 0x08, 0x54, 0x63, 0x70, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x12, 0x12,
	0x0a, 0x04, 0x70, 0x6f, 0x72, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x70, 0x6f,
	0x72, 0x74, 0x12, 0x53, 0x0a, 0x0b, 0x70, 0x69, 0x6e, 0x67, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69,
	0x67, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x32, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2e, 0x6d, 0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x33, 0x2e, 0x55,
	0x70, 0x74, 0x69, 0x6d, 0x65, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67,
	0x2e, 0x50, 0x69, 0x6e, 0x67, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x0a, 0x70, 0x69, 0x6e,
	0x67, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x1a, 0x84, 0x06, 0x0a, 0x0e, 0x43, 0x6f, 0x6e, 0x74,
	0x65, 0x6e, 0x74, 0x4d, 0x61, 0x74, 0x63, 0x68, 0x65, 0x72, 0x12, 0x18, 0x0a, 0x07, 0x63, 0x6f,
	0x6e, 0x74, 0x65, 0x6e, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x63, 0x6f, 0x6e,
	0x74, 0x65, 0x6e, 0x74, 0x12, 0x65, 0x0a, 0x07, 0x6d, 0x61, 0x74, 0x63, 0x68, 0x65, 0x72, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x4b, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x6d,
	0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x33, 0x2e, 0x55, 0x70, 0x74,
	0x69, 0x6d, 0x65, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2e, 0x43,
	0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x4d, 0x61, 0x74, 0x63, 0x68, 0x65, 0x72, 0x2e, 0x43, 0x6f,
	0x6e, 0x74, 0x65, 0x6e, 0x74, 0x4d, 0x61, 0x74, 0x63, 0x68, 0x65, 0x72, 0x4f, 0x70, 0x74, 0x69,
	0x6f, 0x6e, 0x52, 0x07, 0x6d, 0x61, 0x74, 0x63, 0x68, 0x65, 0x72, 0x12, 0x74, 0x0a, 0x11, 0x6a,
	0x73, 0x6f, 0x6e, 0x5f, 0x70, 0x61, 0x74, 0x68, 0x5f, 0x6d, 0x61, 0x74, 0x63, 0x68, 0x65, 0x72,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x46, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e,
	0x6d, 0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x33, 0x2e, 0x55, 0x70,
	0x74, 0x69, 0x6d, 0x65, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2e,
	0x43, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x4d, 0x61, 0x74, 0x63, 0x68, 0x65, 0x72, 0x2e, 0x4a,
	0x73, 0x6f, 0x6e, 0x50, 0x61, 0x74, 0x68, 0x4d, 0x61, 0x74, 0x63, 0x68, 0x65, 0x72, 0x48, 0x00,
	0x52, 0x0f, 0x6a, 0x73, 0x6f, 0x6e, 0x50, 0x61, 0x74, 0x68, 0x4d, 0x61, 0x74, 0x63, 0x68, 0x65,
	0x72, 0x1a, 0x94, 0x02, 0x0a, 0x0f, 0x4a, 0x73, 0x6f, 0x6e, 0x50, 0x61, 0x74, 0x68, 0x4d, 0x61,
	0x74, 0x63, 0x68, 0x65, 0x72, 0x12, 0x1b, 0x0a, 0x09, 0x6a, 0x73, 0x6f, 0x6e, 0x5f, 0x70, 0x61,
	0x74, 0x68, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x6a, 0x73, 0x6f, 0x6e, 0x50, 0x61,
	0x74, 0x68, 0x12, 0x7f, 0x0a, 0x0c, 0x6a, 0x73, 0x6f, 0x6e, 0x5f, 0x6d, 0x61, 0x74, 0x63, 0x68,
	0x65, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x5c, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2e, 0x6d, 0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x33, 0x2e,
	0x55, 0x70, 0x74, 0x69, 0x6d, 0x65, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x43, 0x6f, 0x6e, 0x66, 0x69,
	0x67, 0x2e, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x4d, 0x61, 0x74, 0x63, 0x68, 0x65, 0x72,
	0x2e, 0x4a, 0x73, 0x6f, 0x6e, 0x50, 0x61, 0x74, 0x68, 0x4d, 0x61, 0x74, 0x63, 0x68, 0x65, 0x72,
	0x2e, 0x4a, 0x73, 0x6f, 0x6e, 0x50, 0x61, 0x74, 0x68, 0x4d, 0x61, 0x74, 0x63, 0x68, 0x65, 0x72,
	0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x0b, 0x6a, 0x73, 0x6f, 0x6e, 0x4d, 0x61, 0x74, 0x63,
	0x68, 0x65, 0x72, 0x22, 0x63, 0x0a, 0x15, 0x4a, 0x73, 0x6f, 0x6e, 0x50, 0x61, 0x74, 0x68, 0x4d,
	0x61, 0x74, 0x63, 0x68, 0x65, 0x72, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x28, 0x0a, 0x24,
	0x4a, 0x53, 0x4f, 0x4e, 0x5f, 0x50, 0x41, 0x54, 0x48, 0x5f, 0x4d, 0x41, 0x54, 0x43, 0x48, 0x45,
	0x52, 0x5f, 0x4f, 0x50, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49,
	0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x0f, 0x0a, 0x0b, 0x45, 0x58, 0x41, 0x43, 0x54, 0x5f,
	0x4d, 0x41, 0x54, 0x43, 0x48, 0x10, 0x01, 0x12, 0x0f, 0x0a, 0x0b, 0x52, 0x45, 0x47, 0x45, 0x58,
	0x5f, 0x4d, 0x41, 0x54, 0x43, 0x48, 0x10, 0x02, 0x22, 0xc8, 0x01, 0x0a, 0x14, 0x43, 0x6f, 0x6e,
	0x74, 0x65, 0x6e, 0x74, 0x4d, 0x61, 0x74, 0x63, 0x68, 0x65, 0x72, 0x4f, 0x70, 0x74, 0x69, 0x6f,
	0x6e, 0x12, 0x26, 0x0a, 0x22, 0x43, 0x4f, 0x4e, 0x54, 0x45, 0x4e, 0x54, 0x5f, 0x4d, 0x41, 0x54,
	0x43, 0x48, 0x45, 0x52, 0x5f, 0x4f, 0x50, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x55, 0x4e, 0x53, 0x50,
	0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x13, 0x0a, 0x0f, 0x43, 0x4f, 0x4e,
	0x54, 0x41, 0x49, 0x4e, 0x53, 0x5f, 0x53, 0x54, 0x52, 0x49, 0x4e, 0x47, 0x10, 0x01, 0x12, 0x17,
	0x0a, 0x13, 0x4e, 0x4f, 0x54, 0x5f, 0x43, 0x4f, 0x4e, 0x54, 0x41, 0x49, 0x4e, 0x53, 0x5f, 0x53,
	0x54, 0x52, 0x49, 0x4e, 0x47, 0x10, 0x02, 0x12, 0x11, 0x0a, 0x0d, 0x4d, 0x41, 0x54, 0x43, 0x48,
	0x45, 0x53, 0x5f, 0x52, 0x45, 0x47, 0x45, 0x58, 0x10, 0x03, 0x12, 0x15, 0x0a, 0x11, 0x4e, 0x4f,
	0x54, 0x5f, 0x4d, 0x41, 0x54, 0x43, 0x48, 0x45, 0x53, 0x5f, 0x52, 0x45, 0x47, 0x45, 0x58, 0x10,
	0x04, 0x12, 0x15, 0x0a, 0x11, 0x4d, 0x41, 0x54, 0x43, 0x48, 0x45, 0x53, 0x5f, 0x4a, 0x53, 0x4f,
	0x4e, 0x5f, 0x50, 0x41, 0x54, 0x48, 0x10, 0x05, 0x12, 0x19, 0x0a, 0x15, 0x4e, 0x4f, 0x54, 0x5f,
	0x4d, 0x41, 0x54, 0x43, 0x48, 0x45, 0x53, 0x5f, 0x4a, 0x53, 0x4f, 0x4e, 0x5f, 0x50, 0x41, 0x54,
	0x48, 0x10, 0x06, 0x42, 0x19, 0x0a, 0x17, 0x61, 0x64, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x61,
	0x6c, 0x5f, 0x6d, 0x61, 0x74, 0x63, 0x68, 0x65, 0x72, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x1a, 0x3d,
	0x0a, 0x0f, 0x55, 0x73, 0x65, 0x72, 0x4c, 0x61, 0x62, 0x65, 0x6c, 0x73, 0x45, 0x6e, 0x74, 0x72,
	0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03,
	0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0x55, 0x0a,
	0x0b, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x65, 0x72, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1c, 0x0a, 0x18,
	0x43, 0x48, 0x45, 0x43, 0x4b, 0x45, 0x52, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x55, 0x4e, 0x53,
	0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x16, 0x0a, 0x12, 0x53, 0x54,
	0x41, 0x54, 0x49, 0x43, 0x5f, 0x49, 0x50, 0x5f, 0x43, 0x48, 0x45, 0x43, 0x4b, 0x45, 0x52, 0x53,
	0x10, 0x01, 0x12, 0x10, 0x0a, 0x0c, 0x56, 0x50, 0x43, 0x5f, 0x43, 0x48, 0x45, 0x43, 0x4b, 0x45,
	0x52, 0x53, 0x10, 0x03, 0x3a, 0xf3, 0x01, 0xea, 0x41, 0xef, 0x01, 0x0a, 0x2b, 0x6d, 0x6f, 0x6e,
	0x69, 0x74, 0x6f, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x61, 0x70,
	0x69, 0x73, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x55, 0x70, 0x74, 0x69, 0x6d, 0x65, 0x43, 0x68, 0x65,
	0x63, 0x6b, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x3b, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63,
	0x74, 0x73, 0x2f, 0x7b, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x7d, 0x2f, 0x75, 0x70, 0x74,
	0x69, 0x6d, 0x65, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x73, 0x2f,
	0x7b, 0x75, 0x70, 0x74, 0x69, 0x6d, 0x65, 0x5f, 0x63, 0x68, 0x65, 0x63, 0x6b, 0x5f, 0x63, 0x6f,
	0x6e, 0x66, 0x69, 0x67, 0x7d, 0x12, 0x45, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x7b, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x7d, 0x2f, 0x75, 0x70, 0x74, 0x69, 0x6d, 0x65, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x43,
	0x6f, 0x6e, 0x66, 0x69, 0x67, 0x73, 0x2f, 0x7b, 0x75, 0x70, 0x74, 0x69, 0x6d, 0x65, 0x5f, 0x63,
	0x68, 0x65, 0x63, 0x6b, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x7d, 0x12, 0x39, 0x66, 0x6f,
	0x6c, 0x64, 0x65, 0x72, 0x73, 0x2f, 0x7b, 0x66, 0x6f, 0x6c, 0x64, 0x65, 0x72, 0x7d, 0x2f, 0x75,
	0x70, 0x74, 0x69, 0x6d, 0x65, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67,
	0x73, 0x2f, 0x7b, 0x75, 0x70, 0x74, 0x69, 0x6d, 0x65, 0x5f, 0x63, 0x68, 0x65, 0x63, 0x6b, 0x5f,
	0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x7d, 0x12, 0x01, 0x2a, 0x42, 0x0a, 0x0a, 0x08, 0x72, 0x65,
	0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x42, 0x14, 0x0a, 0x12, 0x63, 0x68, 0x65, 0x63, 0x6b, 0x5f,
	0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x22, 0x8b, 0x01, 0x0a,
	0x0d, 0x55, 0x70, 0x74, 0x69, 0x6d, 0x65, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x49, 0x70, 0x12, 0x3f,
	0x0a, 0x06, 0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x27,
	0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x6d, 0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72, 0x69,
	0x6e, 0x67, 0x2e, 0x76, 0x33, 0x2e, 0x55, 0x70, 0x74, 0x69, 0x6d, 0x65, 0x43, 0x68, 0x65, 0x63,
	0x6b, 0x52, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x52, 0x06, 0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x12,
	0x1a, 0x0a, 0x08, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x08, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x1d, 0x0a, 0x0a, 0x69,
	0x70, 0x5f, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x09, 0x69, 0x70, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x2a, 0x95, 0x01, 0x0a, 0x11, 0x55,
	0x70, 0x74, 0x69, 0x6d, 0x65, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x52, 0x65, 0x67, 0x69, 0x6f, 0x6e,
	0x12, 0x16, 0x0a, 0x12, 0x52, 0x45, 0x47, 0x49, 0x4f, 0x4e, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45,
	0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x07, 0x0a, 0x03, 0x55, 0x53, 0x41, 0x10,
	0x01, 0x12, 0x0a, 0x0a, 0x06, 0x45, 0x55, 0x52, 0x4f, 0x50, 0x45, 0x10, 0x02, 0x12, 0x11, 0x0a,
	0x0d, 0x53, 0x4f, 0x55, 0x54, 0x48, 0x5f, 0x41, 0x4d, 0x45, 0x52, 0x49, 0x43, 0x41, 0x10, 0x03,
	0x12, 0x10, 0x0a, 0x0c, 0x41, 0x53, 0x49, 0x41, 0x5f, 0x50, 0x41, 0x43, 0x49, 0x46, 0x49, 0x43,
	0x10, 0x04, 0x12, 0x0e, 0x0a, 0x0a, 0x55, 0x53, 0x41, 0x5f, 0x4f, 0x52, 0x45, 0x47, 0x4f, 0x4e,
	0x10, 0x05, 0x12, 0x0c, 0x0a, 0x08, 0x55, 0x53, 0x41, 0x5f, 0x49, 0x4f, 0x57, 0x41, 0x10, 0x06,
	0x12, 0x10, 0x0a, 0x0c, 0x55, 0x53, 0x41, 0x5f, 0x56, 0x49, 0x52, 0x47, 0x49, 0x4e, 0x49, 0x41,
	0x10, 0x07, 0x2a, 0x5b, 0x0a, 0x11, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x52, 0x65, 0x73, 0x6f, 0x75,
	0x72, 0x63, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1d, 0x0a, 0x19, 0x52, 0x45, 0x53, 0x4f, 0x55,
	0x52, 0x43, 0x45, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49,
	0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x0c, 0x0a, 0x08, 0x49, 0x4e, 0x53, 0x54, 0x41, 0x4e,
	0x43, 0x45, 0x10, 0x01, 0x12, 0x19, 0x0a, 0x15, 0x41, 0x57, 0x53, 0x5f, 0x45, 0x4c, 0x42, 0x5f,
	0x4c, 0x4f, 0x41, 0x44, 0x5f, 0x42, 0x41, 0x4c, 0x41, 0x4e, 0x43, 0x45, 0x52, 0x10, 0x02, 0x42,
	0xc6, 0x01, 0x0a, 0x18, 0x63, 0x6f, 0x6d, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x6d,
	0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x33, 0x42, 0x0b, 0x55, 0x70,
	0x74, 0x69, 0x6d, 0x65, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x50, 0x01, 0x5a, 0x41, 0x63, 0x6c, 0x6f,
	0x75, 0x64, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x67, 0x6f,
	0x2f, 0x6d, 0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72, 0x69, 0x6e, 0x67, 0x2f, 0x61, 0x70, 0x69, 0x76,
	0x33, 0x2f, 0x76, 0x32, 0x2f, 0x6d, 0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72, 0x69, 0x6e, 0x67, 0x70,
	0x62, 0x3b, 0x6d, 0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72, 0x69, 0x6e, 0x67, 0x70, 0x62, 0xaa, 0x02,
	0x1a, 0x47, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x43, 0x6c, 0x6f, 0x75, 0x64, 0x2e, 0x4d, 0x6f,
	0x6e, 0x69, 0x74, 0x6f, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x56, 0x33, 0xca, 0x02, 0x1a, 0x47, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x5c, 0x43, 0x6c, 0x6f, 0x75, 0x64, 0x5c, 0x4d, 0x6f, 0x6e, 0x69, 0x74,
	0x6f, 0x72, 0x69, 0x6e, 0x67, 0x5c, 0x56, 0x33, 0xea, 0x02, 0x1d, 0x47, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x3a, 0x3a, 0x43, 0x6c, 0x6f, 0x75, 0x64, 0x3a, 0x3a, 0x4d, 0x6f, 0x6e, 0x69, 0x74, 0x6f,
	0x72, 0x69, 0x6e, 0x67, 0x3a, 0x3a, 0x56, 0x33, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_google_monitoring_v3_uptime_proto_rawDescOnce sync.Once
	file_google_monitoring_v3_uptime_proto_rawDescData = file_google_monitoring_v3_uptime_proto_rawDesc
)

func file_google_monitoring_v3_uptime_proto_rawDescGZIP() []byte {
	file_google_monitoring_v3_uptime_proto_rawDescOnce.Do(func() {
		file_google_monitoring_v3_uptime_proto_rawDescData = protoimpl.X.CompressGZIP(file_google_monitoring_v3_uptime_proto_rawDescData)
	})
	return file_google_monitoring_v3_uptime_proto_rawDescData
}

var file_google_monitoring_v3_uptime_proto_enumTypes = make([]protoimpl.EnumInfo, 9)
var file_google_monitoring_v3_uptime_proto_msgTypes = make([]protoimpl.MessageInfo, 13)
var file_google_monitoring_v3_uptime_proto_goTypes = []interface{}{
	(UptimeCheckRegion)(0),                                                      // 0: google.monitoring.v3.UptimeCheckRegion
	(GroupResourceType)(0),                                                      // 1: google.monitoring.v3.GroupResourceType
	(InternalChecker_State)(0),                                                  // 2: google.monitoring.v3.InternalChecker.State
	(UptimeCheckConfig_CheckerType)(0),                                          // 3: google.monitoring.v3.UptimeCheckConfig.CheckerType
	(UptimeCheckConfig_HttpCheck_RequestMethod)(0),                              // 4: google.monitoring.v3.UptimeCheckConfig.HttpCheck.RequestMethod
	(UptimeCheckConfig_HttpCheck_ContentType)(0),                                // 5: google.monitoring.v3.UptimeCheckConfig.HttpCheck.ContentType
	(UptimeCheckConfig_HttpCheck_ResponseStatusCode_StatusClass)(0),             // 6: google.monitoring.v3.UptimeCheckConfig.HttpCheck.ResponseStatusCode.StatusClass
	(UptimeCheckConfig_ContentMatcher_ContentMatcherOption)(0),                  // 7: google.monitoring.v3.UptimeCheckConfig.ContentMatcher.ContentMatcherOption
	(UptimeCheckConfig_ContentMatcher_JsonPathMatcher_JsonPathMatcherOption)(0), // 8: google.monitoring.v3.UptimeCheckConfig.ContentMatcher.JsonPathMatcher.JsonPathMatcherOption
	(*InternalChecker)(nil),                                                     // 9: google.monitoring.v3.InternalChecker
	(*UptimeCheckConfig)(nil),                                                   // 10: google.monitoring.v3.UptimeCheckConfig
	(*UptimeCheckIp)(nil),                                                       // 11: google.monitoring.v3.UptimeCheckIp
	(*UptimeCheckConfig_ResourceGroup)(nil),                                     // 12: google.monitoring.v3.UptimeCheckConfig.ResourceGroup
	(*UptimeCheckConfig_PingConfig)(nil),                                        // 13: google.monitoring.v3.UptimeCheckConfig.PingConfig
	(*UptimeCheckConfig_HttpCheck)(nil),                                         // 14: google.monitoring.v3.UptimeCheckConfig.HttpCheck
	(*UptimeCheckConfig_TcpCheck)(nil),                                          // 15: google.monitoring.v3.UptimeCheckConfig.TcpCheck
	(*UptimeCheckConfig_ContentMatcher)(nil),                                    // 16: google.monitoring.v3.UptimeCheckConfig.ContentMatcher
	nil,                                                                         // 17: google.monitoring.v3.UptimeCheckConfig.UserLabelsEntry
	(*UptimeCheckConfig_HttpCheck_BasicAuthentication)(nil),                     // 18: google.monitoring.v3.UptimeCheckConfig.HttpCheck.BasicAuthentication
	(*UptimeCheckConfig_HttpCheck_ResponseStatusCode)(nil),                      // 19: google.monitoring.v3.UptimeCheckConfig.HttpCheck.ResponseStatusCode
	nil, // 20: google.monitoring.v3.UptimeCheckConfig.HttpCheck.HeadersEntry
	(*UptimeCheckConfig_ContentMatcher_JsonPathMatcher)(nil), // 21: google.monitoring.v3.UptimeCheckConfig.ContentMatcher.JsonPathMatcher
	(*monitoredres.MonitoredResource)(nil),                   // 22: google.api.MonitoredResource
	(*durationpb.Duration)(nil),                              // 23: google.protobuf.Duration
}
var file_google_monitoring_v3_uptime_proto_depIdxs = []int32{
	2,  // 0: google.monitoring.v3.InternalChecker.state:type_name -> google.monitoring.v3.InternalChecker.State
	22, // 1: google.monitoring.v3.UptimeCheckConfig.monitored_resource:type_name -> google.api.MonitoredResource
	12, // 2: google.monitoring.v3.UptimeCheckConfig.resource_group:type_name -> google.monitoring.v3.UptimeCheckConfig.ResourceGroup
	14, // 3: google.monitoring.v3.UptimeCheckConfig.http_check:type_name -> google.monitoring.v3.UptimeCheckConfig.HttpCheck
	15, // 4: google.monitoring.v3.UptimeCheckConfig.tcp_check:type_name -> google.monitoring.v3.UptimeCheckConfig.TcpCheck
	23, // 5: google.monitoring.v3.UptimeCheckConfig.period:type_name -> google.protobuf.Duration
	23, // 6: google.monitoring.v3.UptimeCheckConfig.timeout:type_name -> google.protobuf.Duration
	16, // 7: google.monitoring.v3.UptimeCheckConfig.content_matchers:type_name -> google.monitoring.v3.UptimeCheckConfig.ContentMatcher
	3,  // 8: google.monitoring.v3.UptimeCheckConfig.checker_type:type_name -> google.monitoring.v3.UptimeCheckConfig.CheckerType
	0,  // 9: google.monitoring.v3.UptimeCheckConfig.selected_regions:type_name -> google.monitoring.v3.UptimeCheckRegion
	9,  // 10: google.monitoring.v3.UptimeCheckConfig.internal_checkers:type_name -> google.monitoring.v3.InternalChecker
	17, // 11: google.monitoring.v3.UptimeCheckConfig.user_labels:type_name -> google.monitoring.v3.UptimeCheckConfig.UserLabelsEntry
	0,  // 12: google.monitoring.v3.UptimeCheckIp.region:type_name -> google.monitoring.v3.UptimeCheckRegion
	1,  // 13: google.monitoring.v3.UptimeCheckConfig.ResourceGroup.resource_type:type_name -> google.monitoring.v3.GroupResourceType
	4,  // 14: google.monitoring.v3.UptimeCheckConfig.HttpCheck.request_method:type_name -> google.monitoring.v3.UptimeCheckConfig.HttpCheck.RequestMethod
	18, // 15: google.monitoring.v3.UptimeCheckConfig.HttpCheck.auth_info:type_name -> google.monitoring.v3.UptimeCheckConfig.HttpCheck.BasicAuthentication
	20, // 16: google.monitoring.v3.UptimeCheckConfig.HttpCheck.headers:type_name -> google.monitoring.v3.UptimeCheckConfig.HttpCheck.HeadersEntry
	5,  // 17: google.monitoring.v3.UptimeCheckConfig.HttpCheck.content_type:type_name -> google.monitoring.v3.UptimeCheckConfig.HttpCheck.ContentType
	19, // 18: google.monitoring.v3.UptimeCheckConfig.HttpCheck.accepted_response_status_codes:type_name -> google.monitoring.v3.UptimeCheckConfig.HttpCheck.ResponseStatusCode
	13, // 19: google.monitoring.v3.UptimeCheckConfig.HttpCheck.ping_config:type_name -> google.monitoring.v3.UptimeCheckConfig.PingConfig
	13, // 20: google.monitoring.v3.UptimeCheckConfig.TcpCheck.ping_config:type_name -> google.monitoring.v3.UptimeCheckConfig.PingConfig
	7,  // 21: google.monitoring.v3.UptimeCheckConfig.ContentMatcher.matcher:type_name -> google.monitoring.v3.UptimeCheckConfig.ContentMatcher.ContentMatcherOption
	21, // 22: google.monitoring.v3.UptimeCheckConfig.ContentMatcher.json_path_matcher:type_name -> google.monitoring.v3.UptimeCheckConfig.ContentMatcher.JsonPathMatcher
	6,  // 23: google.monitoring.v3.UptimeCheckConfig.HttpCheck.ResponseStatusCode.status_class:type_name -> google.monitoring.v3.UptimeCheckConfig.HttpCheck.ResponseStatusCode.StatusClass
	8,  // 24: google.monitoring.v3.UptimeCheckConfig.ContentMatcher.JsonPathMatcher.json_matcher:type_name -> google.monitoring.v3.UptimeCheckConfig.ContentMatcher.JsonPathMatcher.JsonPathMatcherOption
	25, // [25:25] is the sub-list for method output_type
	25, // [25:25] is the sub-list for method input_type
	25, // [25:25] is the sub-list for extension type_name
	25, // [25:25] is the sub-list for extension extendee
	0,  // [0:25] is the sub-list for field type_name
}

func init() { file_google_monitoring_v3_uptime_proto_init() }
func file_google_monitoring_v3_uptime_proto_init() {
	if File_google_monitoring_v3_uptime_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_google_monitoring_v3_uptime_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*InternalChecker); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_monitoring_v3_uptime_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UptimeCheckConfig); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_monitoring_v3_uptime_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UptimeCheckIp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_monitoring_v3_uptime_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UptimeCheckConfig_ResourceGroup); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_monitoring_v3_uptime_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UptimeCheckConfig_PingConfig); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_monitoring_v3_uptime_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UptimeCheckConfig_HttpCheck); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_monitoring_v3_uptime_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UptimeCheckConfig_TcpCheck); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_monitoring_v3_uptime_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UptimeCheckConfig_ContentMatcher); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_monitoring_v3_uptime_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UptimeCheckConfig_HttpCheck_BasicAuthentication); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_monitoring_v3_uptime_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UptimeCheckConfig_HttpCheck_ResponseStatusCode); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_monitoring_v3_uptime_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UptimeCheckConfig_ContentMatcher_JsonPathMatcher); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_google_monitoring_v3_uptime_proto_msgTypes[1].OneofWrappers = []interface{}{
		(*UptimeCheckConfig_MonitoredResource)(nil),
		(*UptimeCheckConfig_ResourceGroup_)(nil),
		(*UptimeCheckConfig_HttpCheck_)(nil),
		(*UptimeCheckConfig_TcpCheck_)(nil),
	}
	file_google_monitoring_v3_uptime_proto_msgTypes[7].OneofWrappers = []interface{}{
		(*UptimeCheckConfig_ContentMatcher_JsonPathMatcher_)(nil),
	}
	file_google_monitoring_v3_uptime_proto_msgTypes[10].OneofWrappers = []interface{}{
		(*UptimeCheckConfig_HttpCheck_ResponseStatusCode_StatusValue)(nil),
		(*UptimeCheckConfig_HttpCheck_ResponseStatusCode_StatusClass_)(nil),
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_google_monitoring_v3_uptime_proto_rawDesc,
			NumEnums:      9,
			NumMessages:   13,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_google_monitoring_v3_uptime_proto_goTypes,
		DependencyIndexes: file_google_monitoring_v3_uptime_proto_depIdxs,
		EnumInfos:         file_google_monitoring_v3_uptime_proto_enumTypes,
		MessageInfos:      file_google_monitoring_v3_uptime_proto_msgTypes,
	}.Build()
	File_google_monitoring_v3_uptime_proto = out.File
	file_google_monitoring_v3_uptime_proto_rawDesc = nil
	file_google_monitoring_v3_uptime_proto_goTypes = nil
	file_google_monitoring_v3_uptime_proto_depIdxs = nil
}
