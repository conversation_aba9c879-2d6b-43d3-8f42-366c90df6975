package location

// AccountGroup entity structure
type AccountGroup struct {
	AccountGroupID      string
	Name                string
	Address             string
	ActivatedProductIDs []string
	URL                 string
}

// BrandTabStatuses is tab statuses for a brand
type BrandTabStatuses struct {
	ReviewsTabEnabled     bool
	ListingsTabEnabled    bool
	SocialTabEnabled      bool
	MapTabEnabled         bool
	AdvertisingTabEnabled bool
	ReportTabEnabled      bool
	DataExportTabEnabled  bool
}

// Brand entity structure
type Brand struct {
	Name        string
	PathNodes   []string
	HasAccess   bool
	URL         string
	TabStatuses *BrandTabStatuses
}

func (b *Brand) GetName() string {
	if b == nil {
		return ""
	}
	return b.Name
}

func (b *Brand) GetTabStatuses() *BrandTabStatuses {
	if b == nil {
		return nil
	}
	return b.TabStatuses
}
