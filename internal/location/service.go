package location

import (
	"context"
	"fmt"
	"net/url"
	"strings"
	"sync"

	"github.com/vendasta/IAM/sdks/go/v1/subject"
	"github.com/vendasta/IAM/sdks/go/v1/subjectcontext"
	partner "github.com/vendasta/partner/sdks/go/v1"
	tesseract "github.com/vendasta/tesseract/sdks/go/v1"

	internaliam "github.com/vendasta/atlas/internal/iam"

	"github.com/vendasta/IAM/sdks/go/iaminterceptor"
	iam "github.com/vendasta/IAM/sdks/go/v1"
	accountgroup "github.com/vendasta/account-group/sdks/go/v1"
	domain "github.com/vendasta/domain/sdks/go/v2"
	multilocation_v1 "github.com/vendasta/generated-protos-go/multi_location/v1"
	"github.com/vendasta/gosdks/logging"
	"github.com/vendasta/gosdks/validation"
	"github.com/vendasta/gosdks/verrors"
	group "github.com/vendasta/group/sdks/go/v2"
	multilocation "github.com/vendasta/multi-location/sdks/go/v1"
	"golang.org/x/sync/errgroup"

	defaultlocation "github.com/vendasta/atlas/internal/default-location"
)

var (
	errDomainNotFound = verrors.New(verrors.NotFound, "domain not found")
)

// Service implements a location service
type Service struct {
	accountGroupClient        accountgroup.Interface
	groupClient               group.Interface
	domainClient              domain.Interface
	multiLocationClient       multilocation.MultiLocationClientInterface
	iamAuth                   iam.AuthService
	iamClient                 iam.Interface
	defaultLocationRepository defaultlocation.Repository
	smbFetcher                SMBFetcher
	partnerClient             partner.Interface
	tesseractClient           tesseract.Interface
}

// NewService returns a new location service implementation
func NewService(accountGroupClient accountgroup.Interface, groupClient group.Interface, domainClient domain.Interface, multilocationClient multilocation.MultiLocationClientInterface, iamAuthClient iam.AuthService, iamClient iam.Interface, defaultLocationRepository defaultlocation.Repository, partnerClient partner.Interface, tesseractClient tesseract.Interface) *Service {
	smbFetcher := NewSMBFetcher(iamClient)
	return &Service{
		accountGroupClient:        accountGroupClient,
		groupClient:               groupClient,
		domainClient:              domainClient,
		multiLocationClient:       multilocationClient,
		iamAuth:                   iamAuthClient,
		iamClient:                 iamClient,
		defaultLocationRepository: defaultLocationRepository,
		smbFetcher:                smbFetcher,
		partnerClient:             partnerClient,
		tesseractClient:           tesseractClient,
	}
}

// GetAccountGroups returns the data for the given account group ids
func (s *Service) GetAccountGroups(ctx context.Context, accountGroupIDs []string) ([]*AccountGroup, error) {
	ags, err := s.accountGroupClient.GetMulti(ctx, accountGroupIDs, accountgroup.IncludeAccountGroupExternalIdentifiers(), accountgroup.IncludeNAPData(), accountgroup.IncludeAccounts())
	if err != nil {
		return nil, err
	}
	if len(ags) == 0 {
		return nil, nil
	}
	partnerIDs := getPartnerIDsFromAccountGroups(ags)
	baseURL, err := s.GetVBCWhitelabelURLs(ctx, partnerIDs)
	if err != nil {
		return nil, err
	}
	accountGroups := []*AccountGroup{}
	for _, a := range ags {
		if a == nil || a.ExternalIdentifiers == nil {
			continue
		}
		appIDs := make([]string, len(a.Accounts))
		for j, account := range a.Accounts {
			appIDs[j] = account.MarketplaceAppID
		}
		wlURL, ok := baseURL[a.PartnerID]
		if !ok {
			return nil, errDomainNotFound
		}
		wlURL.Path = fmt.Sprintf("/account/location/%s/", a.AccountGroupID)
		ag := &AccountGroup{
			AccountGroupID:      a.AccountGroupID,
			ActivatedProductIDs: appIDs,
			URL:                 wlURL.String(),
		}
		if a.NAPData != nil {
			ag.Name = a.CompanyName
			ag.Address = buildAddress(a)
		}
		accountGroups = append(accountGroups, ag)
	}
	return accountGroups, nil
}

func (s *Service) GetAccountGroupCount(ctx context.Context, partnerID string) int64 {
	data, err := s.accountGroupClient.Lookup(
		ctx,
		accountgroup.PartnerID(partnerID),
		accountgroup.PageSize(1),
		accountgroup.Cursor(""),
	)
	if err != nil {
		logging.Warningf(ctx, "error counting account groups: %v", err)
		return -1
	}
	return data.TotalResults
}

func (s *Service) GetGroupCount(ctx context.Context, partnerID string) int64 {
	//nolint:dogsled
	_, _, _, total, err := s.groupClient.List(ctx, group.ForeignKeys{PartnerID: partnerID}, group.Path{},
		group.IncludeDescendantsOption(false),
		group.IncludeMembersOption(false),
		group.ListFiltersOption(
			group.MemberTypeFilter("account-group"),
			group.NamespaceFilter("brands")),
		group.ListPageOptions(
			group.Cursor(""),
			group.PageSize(1)),
	)
	if err != nil {
		logging.Warningf(ctx, "error counting brands: %v", err)
		return -1
	}
	return total
}

func buildAddress(a *accountgroup.AccountGroup) string {
	if a == nil || a.NAPData == nil {
		return ""
	}
	var addr []string
	if a.Address != "" {
		addr = append(addr, a.Address)
	}
	if a.Address2 != "" {
		addr = append(addr, a.Address2)
	}
	if a.City != "" {
		addr = append(addr, a.City)
	}
	if a.State != "" {
		addr = append(addr, a.State)
	}
	return strings.Join(addr, ", ")
}

// GetBrands returns the data for the given brand ids
func (s *Service) GetBrands(ctx context.Context, groupIDs []string) ([]*Brand, error) {
	paths := buildPaths(groupIDs)
	groups, err := s.groupClient.GetMulti(ctx, paths)
	if err != nil {
		return nil, err
	}
	if len(groups) == 0 {
		return nil, nil
	}
	partnerIDs := getPartnerIDsFromGroup(groups)
	whitelabelURLs, err := s.GetVBCWhitelabelURLs(ctx, partnerIDs)
	if err != nil {
		return nil, err
	}
	return buildBrands(groups, groupIDs, whitelabelURLs)
}

// GetBrandsWithTabEnablement will return a brand with it's tab statuses
func (s *Service) GetBrandWithTabEnablement(ctx context.Context, partnerID string, groupPath string) (*Brand, error) {
	brands, err := s.GetBrands(ctx, []string{groupPath})
	if err != nil {
		return nil, err
	}
	if len(brands) == 0 {
		return nil, verrors.New(verrors.NotFound, "brand not found")
	}
	brand := brands[0]
	brandPath, err := multilocation.BuildBrandPath(partnerID, brand.Name)
	if err != nil {
		return nil, err
	}
	resp, err := s.multiLocationClient.GetBrand(ctx, &multilocation_v1.GetBrandRequest{Path: &multilocation_v1.GetBrandRequest_BrandPath{BrandPath: brandPath}})
	if err != nil {
		logging.Errorf(ctx, "error getting brand: %v", err)
		return nil, err
	}
	statuses := resp.GetBrand().GetTabStatuses()
	if statuses == nil {
		return brand, nil
	}
	brand.TabStatuses = &BrandTabStatuses{
		ReviewsTabEnabled:     statuses.GetReviewsTabEnabled(),
		ListingsTabEnabled:    statuses.GetListingsTabEnabled(),
		SocialTabEnabled:      statuses.GetSocialTabEnabled(),
		MapTabEnabled:         statuses.GetMapTabEnabled(),
		AdvertisingTabEnabled: statuses.GetAdvertisingTabEnabled(),
		ReportTabEnabled:      statuses.GetReportTabEnabled(),
		DataExportTabEnabled:  statuses.GetDataExportTabEnabled(),
	}
	return brand, nil
}

func (s *Service) GetDefaultLocation(ctx context.Context, partnerID string) (*DefaultLocation, error) {
	err := validation.NewValidator().
		Rule(validation.StringNotEmpty(partnerID, verrors.InvalidArgument, "partnerId is required")).
		Validate()
	if err != nil {
		return nil, err
	}
	tokenInfo := iaminterceptor.GetRequestInfo(ctx).EffectiveCallerTokenInfo()
	if tokenInfo == nil {
		return nil, verrors.New(verrors.Unauthenticated, "token info not found")
	}
	userID := tokenInfo.UserID()
	if impersonatedUserID, ok := tokenInfo.ImpersonateeUserID(); ok {
		userID = impersonatedUserID
	}
	// If we cannot obtain a UserID, the session does not have a default location
	if userID == "" {
		return &DefaultLocation{}, nil
	}
	res, err := s.defaultLocationRepository.Get(ctx, partnerID, userID)
	if err != nil {
		return nil, err
	}
	if res != nil {
		return &DefaultLocation{
			AccountGroupID: res.AccountGroupID,
			GroupID:        res.GroupID,
		}, nil
	}

	// The default location was not found in our repository, but it may not have been migrated from the IAM SMB subject yet.
	// Execute the lazy migration
	// The lazy migration is non transactional, but we expect purely user-driven traffic to this endpoint, so we should be relatively low risk for race conditions.
	smb, err := s.iamClient.SMBBySessionID(ctx, partnerID, tokenInfo.Token())
	if err != nil {
		logging.Warningf(ctx, "Failed to get SMB persona for lazy migration: %s", err.Error())
		return s.checkifOnlyOneLocation(ctx, partnerID)
	}
	if smb.DefaultAccountGroup != "" {
		err = s.SetDefaultLocation(ctx, partnerID, smb.DefaultAccountGroup, "")
		if err != nil {
			logging.Warningf(ctx, "Failed to set default location for lazy migration: %s", err.Error())
		}
		return &DefaultLocation{
			AccountGroupID: smb.DefaultAccountGroup,
		}, nil
	}
	return s.checkifOnlyOneLocation(ctx, partnerID)
}

func (s *Service) checkifOnlyOneLocation(ctx context.Context, partnerID string) (*DefaultLocation, error) {
	locations, _, _, err := s.ListLocations(ctx, partnerID, "", 0, 2, true, true)
	if err != nil {
		return nil, err
	}
	if len(locations) == 1 {
		if locations[0].IsBrand {
			return &DefaultLocation{
				GroupID: locations[0].LocationId,
			}, nil
		}
		return &DefaultLocation{
			AccountGroupID: locations[0].LocationId,
		}, nil
	}
	return &DefaultLocation{}, nil
}

func (s *Service) SetDefaultLocation(ctx context.Context, partnerID string, accountGroupID, groupID string) error {
	err := validation.NewValidator().
		Rule(validation.StringNotEmpty(partnerID, verrors.InvalidArgument, "partnerId is required")).
		Rule(validation.AtLeastOneStringRequired([]string{accountGroupID, groupID}, verrors.InvalidArgument, "one of accountGroupID or groupID is required")).
		Validate()
	if err != nil {
		return err
	}
	tokenInfo := iaminterceptor.GetRequestInfo(ctx).EffectiveCallerTokenInfo()
	userID := tokenInfo.UserID()
	if impersonatedUserID, ok := tokenInfo.ImpersonateeUserID(); ok {
		userID = impersonatedUserID
	}
	return s.defaultLocationRepository.Set(ctx, partnerID, userID, defaultlocation.CreateOrUpdateMutator(accountGroupID, groupID))
}

// Builds group paths to include the parent paths in the get request
// the group list should only contain unique group paths.
func buildPaths(groupIDs []string) []group.Path {
	var paths []group.Path
	for _, id := range groupIDs {
		paths = append(paths, group.PathFromString(id).ParentPaths()...)
	}
	return group.UniquePaths(paths)
}

// Takes a list of groups (including parent groups) and creates a list of brands
// that only include the requested brand ids. The parent groups are used to build the
// brand's name correctly
func buildBrands(groups []*group.Group, groupIDs []string, whitelabelURLs map[string]url.URL) ([]*Brand, error) {
	if len(groups) == 0 || len(groupIDs) == 0 {
		return nil, nil
	}
	groupIDsToGroup := make(map[string]*group.Group)
	for _, g := range groups {
		if g != nil {
			groupIDsToGroup[g.GroupID] = g
		}
	}
	brands := make([]*Brand, len(groupIDs))
	for i, g := range groupIDs {
		groupParts := strings.Split(g, "|")
		nameParts := make([]string, len(groupParts))
		var partnerID string
		for idx, p := range groupParts {
			groupData, ok := groupIDsToGroup[p]
			if !ok {
				continue
			}
			nameParts[idx] = groupData.Name
			if partnerID == "" {
				partnerID = groupData.ForeignKeys.PartnerID
			}
		}
		brandName := buildBrandName(nameParts)
		wlURL, ok := whitelabelURLs[partnerID]
		if !ok {
			return nil, verrors.New(verrors.NotFound, "domain not found")
		}

		// url needs to be built as /account/brands/<BrandName>/
		// since some brand names have a '/' in them we need to do this.
		wlURL.Path = "/account/brands"
		wlURLStr := fmt.Sprintf("%s/%s/", wlURL.String(), url.PathEscape(g))
		brands[i] = &Brand{
			Name:      brandName,
			PathNodes: groupParts,
			URL:       wlURLStr,
		}
	}
	return brands, nil
}

// buildBrandName will build a Brand's name. It will be returned in the
// format: `Parent | Child - Child` the first two elements have a pipe
// separator in the name, the rest of the children will  be separated by dashes.
func buildBrandName(nameParts []string) string {
	if len(nameParts) > 1 {
		// merge the first two name to be `Parent | Child`
		name := nameParts[0] + " | " + nameParts[1]
		// https://github.com/golang/go/wiki/SliceTricks#delete
		nameParts = append(nameParts[:0], nameParts[1:]...)
		nameParts[0] = name
	}
	return strings.Join(nameParts, " - ")
}

func (s *Service) GetVBCWhitelabelURLs(ctx context.Context, partnerIDs []string) (map[string]url.URL, error) {
	eg, egCtx := errgroup.WithContext(ctx)
	mutex := &sync.Mutex{}
	domains := make(map[string]url.URL)
	for _, partnerID := range partnerIDs {
		pid := partnerID
		eg.Go(func() error {
			dm, err := s.domainClient.GetDomainByIdentifier(egCtx, domain.VBCPartner(pid))
			if err != nil {
				return err
			}
			if dm == nil || dm.Primary == nil {
				logging.Errorf(ctx, "unable to get domain for %s")
				return verrors.New(verrors.NotFound, "domain not found")
			}
			scheme := "https"
			if !dm.Primary.Secure {
				scheme = "http"
			}
			mutex.Lock()
			domains[pid] = url.URL{
				Scheme: scheme,
				Host:   dm.Primary.Name,
			}
			mutex.Unlock()
			return nil
		})
	}
	if err := eg.Wait(); err != nil {
		return nil, err
	}
	return domains, nil
}

// nolint:golint,stylecheck
type Location struct {
	LocationId        string `tesseract:"location_id"`
	Name              string `tesseract:"name"`
	Address           string `tesseract:"address"`
	City              string `tesseract:"city"`
	State             string `tesseract:"state"`
	Url               string
	ActivatedProducts []string
	IsBrand           bool `tesseract:"is_brand"`
}

// nolint:gocyclo,staticcheck,stylecheck,golint
func (s *Service) ListLocations(ctx context.Context, partnerId string, search string, cursor int64, pageSize int64, includeAccountGroups, includeBrands bool) ([]Location, int64, bool, error) {
	const CombineQueries = `%v UNION %v`
	const FilterAndPage = `
		SELECT * FROM (%v) AS loc
		WHERE loc.name ILIKE '%%' || @search || '%%'
			OR address ILIKE '%%' || @search || '%%'
			OR city ILIKE '%%' || @search || '%%'
			OR state ILIKE '%%' || @search || '%%'
		ORDER BY loc.name
		LIMIT @pageSize OFFSET @cursor
	`
	const AccountGroupQuery = `
	 SELECT account_group_id AS location_id, company_name AS name, address, city, state, FALSE AS is_brand
	 FROM account_group$accountgroupv2 AS agid
	 WHERE agid.account_group_id IN (@associatedAGIDs)
	   AND agid.partner_id = @partnerId
	   AND agid.deleted IS NULL
	`
	const BrandQuery = `
	 SELECT path AS location_id, name AS name, NULL AS address, NULL AS city, NULL AS state, TRUE AS is_brand
	 FROM group$groupwithmembersv2 AS brand
	 WHERE brand.path IN (@associatedGroupIds)
	   AND brand.partner_id = @partnerId
	   AND brand.deleted IS NULL
	   AND brand.namespace = 'brands'
	`
	const ElevatedAccountGroupQueryWithMarket = `
	 SELECT account_group_id AS location_id, company_name AS name, address, city, state, FALSE AS is_brand
	 FROM account_group$accountgroupv2 AS agid
	 WHERE (agid.account_group_id IN (@associatedAGIDs)
	   OR agid.market_id in (@marketIds))
	   AND agid.partner_id = @partnerId
	   AND agid.deleted IS NULL
	`
	const ElevatedAccountGroupQueryWithoutMarket = `
	 SELECT account_group_id AS location_id, company_name AS name, address, city, state, FALSE AS is_brand
	 FROM account_group$accountgroupv2 AS agid
	 WHERE agid.partner_id = @partnerId
	   AND agid.deleted IS NULL
	`
	const ElevatedBrandQuery = `
	 SELECT path AS location_id, name AS name, NULL AS address, NULL AS city, NULL AS state, TRUE AS is_brand
	 FROM group$groupwithmembersv2 AS brand
	 WHERE brand.partner_id = @partnerId
	   AND brand.deleted IS NULL
	   AND brand.namespace = 'brands'
	`
	//Get whitelabel business app url
	whitelabelURLs, err := s.GetVBCWhitelabelURLs(ctx, []string{partnerId})
	if err != nil {
		return nil, 0, false, err
	}
	//Set elevatedUserView and salesperson
	partnerConfig, err := s.partnerClient.GetConfiguration(ctx, partnerId, "")
	if err != nil {
		return nil, 0, false, fmt.Errorf("could not get partner configuration: %s", err.Error())
	}
	allowSalesPeople := partnerConfig.SalesConfiguration.StSalespersonSharedAccountAccess && partnerConfig.SalesConfiguration.StBusinessCenterAccess
	elevatedUserView := internaliam.HasElevatedAccess(ctx, s.iamClient, partnerId, allowSalesPeople)
	//elevatedUserView := false
	var salesPerson subject.Subject
	var partnerSubject subject.Subject
	if allowSalesPeople && elevatedUserView {
		session, _ := iam.GetSessionFromContext(ctx)
		salesPerson, _ = s.iamClient.GetBySessionID(ctx, subjectcontext.New(subject.SalesPersonSubjectType, partnerId), session)
		partnerSubject, _ = s.iamClient.GetBySessionID(ctx, subjectcontext.New(subject.PartnerSubjectType, ""), session)
	}
	isMarketAdmin := partnerSubject != nil && partnerSubject.Attributes()["accessible_markets"].GetListAttribute() != nil && len(partnerSubject.Attributes()["accessible_markets"].GetListAttribute().GetAttributes()) > 0
	//Build query
	var tesseractQuery string
	accountGroupQuery := AccountGroupQuery
	brandQuery := BrandQuery
	if salesPerson != nil && (partnerSubject == nil || (partnerSubject.PartnerID() != partnerId && !partnerSubject.Attributes()["is_super_admin"].GetBoolAttribute())) {
		accountGroupQuery = ElevatedAccountGroupQueryWithMarket
	} else if isMarketAdmin {
		accountGroupQuery = ElevatedAccountGroupQueryWithMarket
	} else if elevatedUserView {
		accountGroupQuery = ElevatedAccountGroupQueryWithoutMarket
		brandQuery = ElevatedBrandQuery
	}
	if includeAccountGroups && includeBrands {
		tesseractQuery = fmt.Sprintf(CombineQueries, accountGroupQuery, brandQuery)
	} else if includeAccountGroups {
		tesseractQuery = accountGroupQuery
	} else if includeBrands {
		tesseractQuery = brandQuery
	} else {
		return nil, 0, false, fmt.Errorf("neither account groups nor brands were requested")
	}
	tesseractQuery = fmt.Sprintf(FilterAndPage, tesseractQuery)
	smbData, err := s.smbFetcher.GetSMBData(ctx, partnerId)
	if err != nil {
		tesseractQuery = strings.ReplaceAll(tesseractQuery, "@associatedAGIDs", "''")
		tesseractQuery = strings.ReplaceAll(tesseractQuery, "@associatedGroupIds", "''")
	} else {
		tesseractQuery = strings.ReplaceAll(tesseractQuery, "@associatedAGIDs", fmt.Sprintf("'%s'", strings.Join(smbData.GetAssociatedAccountGroups(), "', '")))
		tesseractQuery = strings.ReplaceAll(tesseractQuery, "@associatedGroupIds", fmt.Sprintf("'%s'", strings.Join(smbData.GetGroupAssociations(), "', '")))
	}
	stmt := tesseract.NewStatement(tesseractQuery)
	//Set query parameters
	stmt.Params["partnerId"] = partnerId
	stmt.Params["search"] = search
	stmt.Params["cursor"] = cursor
	stmt.Params["pageSize"] = pageSize
	if salesPerson != nil {
		stmt.Params["marketIds"] = []string{salesPerson.Attributes()["market_id"].String()}
	} else if isMarketAdmin {
		markets := make([]string, len(partnerSubject.Attributes()["accessible_markets"].GetListAttribute().Attributes))
		for i, attr := range partnerSubject.Attributes()["accessible_markets"].GetListAttribute().Attributes {
			markets[i] = attr.String()
		}
		stmt.Params["marketIds"] = markets
	}
	//Execute SQL
	iter, err := s.tesseractClient.ExecuteSQL(ctx, stmt)
	if err != nil {
		return nil, 0, false, fmt.Errorf("executeSQL err: %s", err.Error())
	}
	//Construct array of locations from query results
	locations := make([]Location, 0, pageSize)
	accountGroupIds := make([]string, 0, pageSize)
	accountGroupIndex := make([]int, 0, pageSize)
	i := 0
	err = iter.Do(func(r tesseract.Result) error {
		var loc Location
		err := r.ToStruct(&loc)
		if err != nil {
			i++
			return fmt.Errorf("error deserializing into loc: %s", err.Error())
		}
		wlURL := whitelabelURLs[partnerId]
		if loc.IsBrand {
			wlURL.Path = fmt.Sprintf("/account/brands/%s", loc.LocationId)
		} else {
			accountGroupIds = append(accountGroupIds, loc.LocationId)
			accountGroupIndex = append(accountGroupIndex, i)
			wlURL.Path = fmt.Sprintf("/account/location/%s", loc.LocationId)
		}
		loc.Url = wlURL.String()
		locations = append(locations, loc)
		i++
		return nil
	})
	if err != nil {
		return nil, 0, false, fmt.Errorf("error iterating location results: %s", err.Error())
	}
	//Get product information for account groups. If the request fails, skip this step.
	accountGroupInfo, err := s.GetAccountGroups(ctx, accountGroupIds)
	if err == nil {
		for i, info := range accountGroupInfo {
			locations[accountGroupIndex[i]].ActivatedProducts = info.ActivatedProductIDs
		}
	}
	return locations, cursor + pageSize, int64(len(locations)) >= pageSize, nil
}
