package internal

import (
	"github.com/vendasta/atlas/internal/accountsdata"
	"github.com/vendasta/atlas/internal/location"
	"github.com/vendasta/atlas/internal/navigation/internal/constants"
	"github.com/vendasta/atlas/internal/product"
	"github.com/vendasta/atlas/internal/sidenavigation"
)

type ConditionEntry = string
type Attribute = string

const (
	// Tabs that the user has access to
	TabPermissions         ConditionEntry = "tab_permissions"
	PartnerAllowedFeatures ConditionEntry = "partner_allowed_features"
	BrandTabPermissions    ConditionEntry = "brand_tab_permissions"
	Products               ConditionEntry = "products"
	FeatureFlags           ConditionEntry = "feature_flags"
	Country                ConditionEntry = "country"
	Editions               ConditionEntry = "editions"
	PlatformMode           ConditionEntry = "platform_mode"
)

type ConditionData = map[ConditionEntry]map[Attribute]struct{}

// ConditionData is the data that is used to determine if a navigation item should be shown
//
// If a map is nil, all queries to that map will return false
func NewConditionData(features *sidenavigation.Features, tabPermissions []string, brandPermissions *location.BrandTabStatuses, products []*product.Product, featureFlags map[string]bool, marketID string, country string, accounts []*accountsdata.Account,
	platformMode string) ConditionData {
	return ConditionData{
		TabPermissions:         buildTabPermissions(tabPermissions),
		PartnerAllowedFeatures: buildPartnerAllowedFeatures(features),
		BrandTabPermissions:    buildBrandTabPermissions(brandPermissions),
		Products:               buildProducts(products),
		FeatureFlags:           buildFeatureFlags(featureFlags),
		Country:                buildCountryData(country),
		Editions:               buildEditions(accounts),
		PlatformMode:           buildPlatformMode(platformMode, marketID),
	}
}

func buildEditions(accounts []*accountsdata.Account) map[string]struct{} {
	if accounts == nil {
		return nil
	}

	editions := make(map[string]struct{})
	for _, a := range accounts {
		editions[a.EditionID] = struct{}{}
	}
	return editions
}

func buildPlatformMode(platformMode string, marketID string) map[string]struct{} {
	if platformMode == "" {
		return nil
	}

	platformModeMap := make(map[string]struct{})
	platformModeMap[platformMode] = struct{}{}
	if platformMode == constants.MobileMode && marketID == constants.MobileDevMarket {
		platformModeMap[constants.MobileDevMode] = struct{}{}
	}

	return platformModeMap
}

func buildFeatureFlags(features map[string]bool) map[string]struct{} {
	if features == nil {
		return nil
	}

	featureFlags := make(map[string]struct{})
	for k, v := range features {
		if v {
			featureFlags[k] = struct{}{}
		}
	}
	return featureFlags
}

func buildCountryData(country string) map[string]struct{} {
	if country == "" {
		return nil
	}

	locationData := make(map[string]struct{})
	locationData[country] = struct{}{}
	return locationData
}

func buildTabPermissions(tabPermissions []string) map[string]struct{} {
	if tabPermissions == nil {
		return nil
	}

	tabPermissionsMap := make(map[string]struct{})
	for _, t := range tabPermissions {
		tabPermissionsMap[t] = struct{}{}
	}
	return tabPermissionsMap
}

func buildProducts(products []*product.Product) map[string]struct{} {
	if products == nil {
		return nil
	}

	productsMap := make(map[string]struct{})
	for _, p := range products {
		productsMap[p.ServiceProviderID] = struct{}{}
	}
	return productsMap
}

func buildBrandTabPermissions(statuses *location.BrandTabStatuses) map[string]struct{} {
	if statuses == nil {
		return nil
	}

	brandTabPermissions := make(map[string]struct{})
	if statuses.AdvertisingTabEnabled {
		brandTabPermissions[constants.AdvertisingTab] = struct{}{}
	}
	if statuses.DataExportTabEnabled {
		brandTabPermissions[constants.DataExportTab] = struct{}{}
	}
	if statuses.MapTabEnabled {
		brandTabPermissions[constants.MapTab] = struct{}{}
	}
	if statuses.ReportTabEnabled {
		brandTabPermissions[constants.ReportTab] = struct{}{}
	}
	if statuses.ReviewsTabEnabled {
		brandTabPermissions[constants.ReviewsTab] = struct{}{}
	}
	if statuses.SocialTabEnabled {
		brandTabPermissions[constants.SocialTab] = struct{}{}
	}
	if statuses.ListingsTabEnabled {
		brandTabPermissions[constants.ListingsTab] = struct{}{}
	}

	return brandTabPermissions
}

// nolint: gocyclo
func buildPartnerAllowedFeatures(features *sidenavigation.Features) map[string]struct{} {
	if features == nil {
		return nil
	}

	partnerAllowedFeatures := make(map[string]struct{})
	if features.ShowContentLibrary {
		partnerAllowedFeatures[constants.UserConfigContentLibraryFeature] = struct{}{}
		partnerAllowedFeatures[constants.UserConfigGuidesFeature] = struct{}{}
		partnerAllowedFeatures[constants.UserConfigRecommendationsFeature] = struct{}{}
		partnerAllowedFeatures[constants.UserConfigLocalMarketingIndexFeature] = struct{}{}
	}
	if features.ShowStore && features.HasProductMarketplace {
		partnerAllowedFeatures[constants.UserConfigMarketplaceFeature] = struct{}{}
	}
	if features.ShowExecutiveReport {
		partnerAllowedFeatures[constants.UserConfigExecutiveReportFeature] = struct{}{}
	}
	if features.ShowFulfillment {
		partnerAllowedFeatures[constants.UserConfigFulfillmentFeature] = struct{}{}
	}
	if features.ShowInviteTeam {
		partnerAllowedFeatures[constants.UserConfigInviteTeam] = struct{}{}
	}
	if features.ShowOrderPage {
		partnerAllowedFeatures[constants.UserConfigOrderPageFeature] = struct{}{}
	}
	if features.ShowInvoices {
		partnerAllowedFeatures[constants.UserConfigInvoicesFeature] = struct{}{}
	}
	if features.ShowMeetingScheduler {
		partnerAllowedFeatures[constants.UserConfigMeetingSchedulerFeature] = struct{}{}
	}
	if features.ShowFiles {
		partnerAllowedFeatures[constants.UserConfigFilesFeature] = struct{}{}
	}
	if features.ShowMyProducts {
		partnerAllowedFeatures[constants.UserConfigMyProductsFeature] = struct{}{}
	}
	if features.ShowCustomers {
		partnerAllowedFeatures[constants.UserConfigCustomersFeature] = struct{}{}
	}
	if features.ShowCRMCompanies {
		partnerAllowedFeatures[constants.UserConfigCRMCompaniesFeature] = struct{}{}
	}
	if features.ShowCRMTasks {
		partnerAllowedFeatures[constants.UserConfigCRMTasksFeature] = struct{}{}
	}
	if features.ShowCRMOpportunities {
		partnerAllowedFeatures[constants.UserConfigCRMOpportunitiesFeature] = struct{}{}
	}
	if features.ShowCRMCustomObjects {
		partnerAllowedFeatures[constants.UserConfigCRMCustomObjectsFeature] = struct{}{}
	}
	if features.ShowDynamicLists {
		partnerAllowedFeatures[constants.UserConfigDynamicListsFeature] = struct{}{}
	}
	if features.ShowLeaderboard {
		partnerAllowedFeatures[constants.UserConfigLeaderboardFeature] = struct{}{}
	}
	if features.ShowCustomForms {
		partnerAllowedFeatures[constants.UserConfigCustomFormsFeature] = struct{}{}
	}
	if features.ShowAutomations {
		partnerAllowedFeatures[constants.UserConfigAutomationsFeature] = struct{}{}
	}
	if features.ShowHome {
		partnerAllowedFeatures[constants.UserConfigDashboardFeature] = struct{}{}
	}
	if features.ShowInboxMessage {
		partnerAllowedFeatures[constants.UserConfigInboxFeature] = struct{}{}
	}
	if features.ShowAIAssistant {
		partnerAllowedFeatures[constants.UserConfigAIAssistantFeature] = struct{}{}
	}
	if features.ShowLeadScoring {
		partnerAllowedFeatures[constants.UserConfigLeadScoringFeature] = struct{}{}
	}

	return partnerAllowedFeatures
}
